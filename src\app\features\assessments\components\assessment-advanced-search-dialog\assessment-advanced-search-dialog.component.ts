import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { ActivatedRoute } from '@angular/router';

// Shared Components
import { FormBuilderComponent } from '@shared/components/form-builder/form-builder.component';
import { CustomButtonComponent } from '@shared/components/custom-button/custom-button.component';

// Core Services and Enums
import { ButtonTypeEnum, IconEnum } from '@core/enums/icon-enum';
import { InputType } from '@shared/enum/input-type.enum';
import { IControlOption } from '@shared/interfaces/i-control-option';

// API Services
import {
  AssessmentServiceProxy,
  AssessmentStatus,
  AssessmentType
} from '@core/api/api.generated';
import { DateConversionService } from '@shared/services/date.service';
import moment from 'moment';

export interface AssessmentSearchFilters {
  search?: string;
  status?: number | string;
  type?: number;
  fromDate?: string;
  toDate?: string;
  createdBy?: string;
}

@Component({
  selector: 'app-assessment-advanced-search-dialog',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    TranslateModule,
    FormBuilderComponent,
    CustomButtonComponent,
  ],
  templateUrl: './assessment-advanced-search-dialog.component.html',
  styleUrls: ['./assessment-advanced-search-dialog.component.scss'],
})
export class AssessmentAdvancedSearchDialogComponent implements OnInit {
  formGroup!: FormGroup;
  isFormSubmitted = false;
  isLoadingData = false;
  currentFundId = 0;

  // Enums for template
  buttonEnum = ButtonTypeEnum;
  IconEnum = IconEnum;

  formControls: IControlOption[] = [
    {
      type: InputType.Text,
      formControlName: 'search',
      id: 'search',
      name: 'search',
      label: 'ASSESSMENTS.SEARCH',
      placeholder: 'ASSESSMENTS.SEARCH_PLACEHOLDER',
      isRequired: false,
      class: 'col-md-12',
      maxLength: 255,
    },
    {
      type: InputType.Dropdown,
      formControlName: 'status',
      id: 'status',
      name: 'status',
      label: 'ASSESSMENTS.FILTER_BY_STATUS',
      placeholder: 'ASSESSMENTS.ALL_STATUSES',
      isRequired: false,
      class: 'col-md-12',
      options: [], // Will be populated dynamically
    },
    {
      type: InputType.Dropdown,
      formControlName: 'type',
      id: 'type',
      name: 'type',
      label: 'ASSESSMENTS.FILTER_BY_TYPE',
      placeholder: 'ASSESSMENTS.ALL_TYPES',
      isRequired: false,
      class: 'col-md-12',
      options: [], // Will be populated dynamically
    },
    {
      type: InputType.Date,
      formControlName: 'fromDate',
      id: 'fromDate',
      name: 'fromDate',
      label: 'ASSESSMENTS.FROM_DATE',
      placeholder: 'ASSESSMENTS.FROM_DATE',
      isRequired: false,
      class: 'col-md-12',
    },
    {
      type: InputType.Date,
      formControlName: 'toDate',
      id: 'toDate',
      name: 'toDate',
      label: 'ASSESSMENTS.TO_DATE',
      placeholder: 'ASSESSMENTS.TO_DATE',
      isRequired: false,
      class: 'col-md-12',
    },
  ];

  constructor(
    public dialogRef: MatDialogRef<AssessmentAdvancedSearchDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: AssessmentSearchFilters,
    private formBuilder: FormBuilder,
    private assessmentServiceProxy: AssessmentServiceProxy,
    private translateService: TranslateService,
    private DateConversionService: DateConversionService,
    private route: ActivatedRoute
  ) {
    this.initForm();
  }

  ngOnInit(): void {
    // Get fundId from URL parameters
    this.route.params.subscribe((params) => {
      if (params['fundId']) {
        this.currentFundId = +params['fundId'];
      } else {
        // Try query params if not in route params
        this.route.queryParams.subscribe((queryParams) => {
          this.currentFundId = +queryParams['fundId'] || 0;
        });
      }

      // Load API data
      this.loadStatusOptions();
      this.loadTypeOptions();
      this.setDateRange();

      // Pre-populate form with existing filter data
      if (this.data) {
        this.formGroup.patchValue(this.data);
        this.setDateIniValue(this.data.fromDate, 'fromDate');
        this.setDateIniValue(this.data.toDate, 'toDate');
      }
    });
  }

  private initForm(): void {
    this.formGroup = this.formBuilder.group({
      search: [''],
      status: [''],
      type: [''],
      fromDate: [''],
      toDate: [''],
      createdBy: [''],
    });
  }

  private loadStatusOptions(): void {
    const statusOptions = [
      { id: '', name: this.translateService.instant('ASSESSMENTS.ALL_STATUSES') },
      { id: AssessmentStatus._1, name: this.translateService.instant('ASSESSMENTS.STATUS.DRAFT') },
      { id: AssessmentStatus._2, name: this.translateService.instant('ASSESSMENTS.STATUS.WAITINGFORAPPROVAL') },
      { id: AssessmentStatus._3, name: this.translateService.instant('ASSESSMENTS.STATUS.APPROVED') },
      { id: AssessmentStatus._4, name: this.translateService.instant('ASSESSMENTS.STATUS.REJECTED') },
      { id: AssessmentStatus._5, name: this.translateService.instant('ASSESSMENTS.STATUS.ACTIVE') },
      { id: AssessmentStatus._6, name: this.translateService.instant('ASSESSMENTS.STATUS.COMPLETED') },
    ];

    const statusControl = this.formControls.find(control => control.formControlName === 'status');
    if (statusControl) {
      statusControl.options = statusOptions;
    }
  }

  private loadTypeOptions(): void {
    const typeOptions = [
      { id: '', name: this.translateService.instant('ASSESSMENTS.ALL_TYPES') },
      { id: AssessmentType._1, name: this.translateService.instant('ASSESSMENTS.TYPE.QUESTIONNAIRE') },
      { id: AssessmentType._2, name: this.translateService.instant('ASSESSMENTS.TYPE.ATTACHMENT') },
    ];

    const typeControl = this.formControls.find(control => control.formControlName === 'type');
    if (typeControl) {
      typeControl.options = typeOptions;
    }
  }

  private setDateRange(): void {
    // Remove all date validation constraints
    // Only logical validation (fromDate < toDate) will be handled in dateSelected method
    const fromDateField = this.formControls.find(
      (f) => f.formControlName === 'fromDate'
    );
    const toDateField = this.formControls.find(
      (f) => f.formControlName === 'toDate'
    );

    if (fromDateField && toDateField) {
      // Clear any existing constraints
      fromDateField.minGreg = undefined;
      fromDateField.maxGreg = undefined;
      fromDateField.minHijri = undefined;
      fromDateField.maxHijri = undefined;
      toDateField.minGreg = undefined;
      toDateField.maxGreg = undefined;
      toDateField.minHijri = undefined;
      toDateField.maxHijri = undefined;
    }
  }

  private setDateIniValue(Date: any, contrilName: string) {
    if (Date) {
      const DateValue = this.DateConversionService.mapStringToSelectedDate(
        moment(Date).format('DD-MM-YYYY')
      );
      this.formGroup.get(contrilName)?.setValue(DateValue);
    }
    // Don't set any default date if Date is null/undefined - leave field empty
  }
  // Event handlers
  dateSelected(event: { event: any; control: IControlOption }) {
    // Store the date in ISO format for API compatibility
    const isoDate = event.event.formattedGregorian;
    this.formGroup
      .get(event.control.formControlName)
      ?.setValue(isoDate);

    console.log(`Date selected for ${event.control.formControlName}:`, isoDate);

    // Only logical validation: ensure fromDate < toDate when both are selected
    this.validateDateRange();
  }

  private validateDateRange(): void {
    const fromDateValue = this.formGroup.get('fromDate')?.value;
    const toDateValue = this.formGroup.get('toDate')?.value;

    if (fromDateValue && toDateValue) {
      const fromDate = new Date(fromDateValue);
      const toDate = new Date(toDateValue);

      if (fromDate > toDate) {
        // Clear the toDate if it's earlier than fromDate
        this.formGroup.get('toDate')?.setValue('');
      }
    }
  }

  dropdownChanged(event: any): void {
    console.log('Dropdown changed:', event);
  }

  applyFilters(): void {
    this.isFormSubmitted = true;
    if (this.formGroup.valid) {
      const filters = this.formGroup.value;
      console.log('Raw form values:', filters);

      // Remove empty values
      const cleanFilters = Object.keys(filters).reduce((acc: any, key) => {
        const value = filters[key];
        console.log(`Checking filter key: ${key}, value: ${value}, type: ${typeof value}`);

        if (value !== '' && value !== null && value !== undefined) {
          acc[key] = value;
          console.log(`Added ${key} to clean filters:`, value);
        } else {
          console.log(`Filtered out ${key} because it was empty/null/undefined`);
        }
        return acc;
      }, {});

      console.log('Clean filters being sent:', cleanFilters);
      this.dialogRef.close(cleanFilters);
    }
  }

  resetFilters(): void {
    console.log('Resetting filters...');

    // Reset form controls first
    this.formGroup.reset();
    this.isFormSubmitted = false;

    // Reinitialize the form to ensure clean state
    this.initForm();

    // Clear all date field constraints and reset date range settings
    this.clearDateConstraints();
    this.setDateRange();

    // Force change detection to ensure UI updates
    setTimeout(() => {
      this.formGroup.markAsUntouched();
      this.formGroup.markAsPristine();
      console.log('Filters reset complete - form values:', this.formGroup.value);
    }, 0);
  }

  private clearDateConstraints(): void {
    const fromDateField = this.formControls.find(f => f.formControlName === 'fromDate');
    const toDateField = this.formControls.find(f => f.formControlName === 'toDate');

    if (fromDateField) {
      fromDateField.minGreg = undefined;
      fromDateField.maxGreg = undefined;
      fromDateField.minHijri = undefined;
      fromDateField.maxHijri = undefined;
    }

    if (toDateField) {
      toDateField.minGreg = undefined;
      toDateField.maxGreg = undefined;
      toDateField.minHijri = undefined;
      toDateField.maxHijri = undefined;
    }
    this.formGroup.controls["fromDate"].setValue('');
    this.formGroup.controls["toDate"].setValue('');
  }

  closeDialog(): void {
    this.dialogRef.close();
  }
}
