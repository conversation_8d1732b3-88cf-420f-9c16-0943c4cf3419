{"sidebar": {"requests": "Requests", "funds": "Funds", "fund_strategies": "Fund Strategies", "document-categories": "Document Categories", "dashboard": "Dashboard", "settings": "Settings", "members": "Members", "users": "User Management", "reports": "Reports"}, "NOT_FOUND": {"TITLE": "Page Not Found", "DESCRIPTION": "Sorry, the page you are looking for does not exist or has been moved to another location.", "GO_BACK": "Go Back", "GO_HOME": "Go Home", "HELP_TEXT": "If you believe this is an error, please contact our technical support team."}, "HEADER": {"SEARCH_PLACEHOLDER": "Search in system..."}, "FORM": {"ERROR_REQUIRED": "Required Field", "ERROR_PATTERN": "Please enter a vaild pattern", "ERROR_UNEXPECTED": "An error is occurred while saving data", "RECORD_SAVED_SUCCESSFULLY": "Record Saved Successfully", "IS_REQUIRED": "Required field", "INVALID_AGE": "Invalid age", "NO_FUTURE_DATE": "Future dates are not allowed", "MIN_LENGTH_ERROR": "Minimum length is {{min}} characters", "MAX_LENGTH_ERROR": "Maximum length is {{max}} characters", "MIN_VALUE_ERROR": "Minimum value allowed is {{min}}", "MAX_VALUE_ERROR": "Maximum value allowed is {{max}}", "IS_EMAIL": "Invalid email format.", "PATTERN_ERROR": "The value does not match the required pattern", "optional": "(optional)", "ERROR_DATE_RANGE": "Start date must be before end date", "ERROR_INT": "Please enter integer numbers only", "ERROR_DUPLICATION": "This value already exists. Please choose a different value.", "ERROR_ARABIC_TEXT": "Please enter Arabic text only", "ERROR_ENGLISH_TEXT": "Please enter English text only", "ERROR_POSITIVE_INTEGER": "Please enter a positive integer", "ERROR_SAUDI_PHONE": "Please enter a valid Saudi phone number (05xxxxxxxx)", "PHONE_PLACEHOLDER": "5xxxxxxxx", "ERROR_PAST_DATE": "Past dates are not allowed", "ERROR_MINIMUM_AGE": "Minimum age required is {{requiredAge}} years", "ERROR_FILE_SIZE": "File size must be less than {{maxSize}} MB", "ERROR_FILE_TYPE": "File type not supported. Supported types: {{allowedTypes}}", "ERROR_NUMBER_RANGE": "Value must be between {{min}} and {{max}}", "ERROR_PERCENTAGE": "Please enter a valid percentage (0-100)", "ERROR_ALPHANUMERIC": "Please enter letters and numbers only", "MAX_FILES_EXCEEDED": "You can upload a maximum of {{max}} files.", "ERROR_ALPHANUMERIC_SPACES": "Please enter letters, numbers and spaces only", "PASSWORD_MIN_LENGTH": "Password must be at least {{requiredLength}} characters long", "PASSWORD_UPPERCASE_REQUIRED": "Password must contain at least one uppercase letter", "PASSWORD_LOWERCASE_REQUIRED": "Password must contain at least one lowercase letter", "PASSWORD_NUMBER_REQUIRED": "Password must contain at least one number", "PASSWORD_SPECIAL_CHAR_REQUIRED": "Password must contain at least one special character", "PASSWORD_LETTER_REQUIRED": "Password must contain at least one letter", "PASSWORD_STRENGTH_WEAK": "Weak", "PASSWORD_STRENGTH_MEDIUM": "Medium", "PASSWORD_STRENGTH_STRONG": "Strong", "PASSWORD_MISMATCH": "Passwords do not match", "ERROR_SAUDI_IBAN": "IBAN must be in Saudi format (SA followed by 22 digits, e.g., ************************)", "ERROR_SAUDI_MOBILE": "Mobile number must be a valid Saudi number (05X or 5X where X is 5,0,3,6,4,9,1,8,7 followed by 7 digits)", "ERROR_SAUDI_PASSPORT": "Passport number must be 1 letter followed by 8 digits (e.g., *********)", "ERROR_SAUDI_NATIONAL_ID": "National ID must be exactly 10 digits"}, "FILE_UPLOAD": {"DRAG_DROP_TEXT": "Drag and drop file here to upload", "MAX_SIZE_TEXT": "Maximum allowed file size is {{maxSize}} MB", "SUPPORTED_FORMATS": "and supported file formats include", "FILE_NOT_SUPPORTED": "File extension not allowed", "FILE_TOO_LARGE": "Invalid file format or size. Please upload a PDF or DOCX file up to {{maxSize}}MB.", "UPLOAD_FAILED": "File upload failed", "FILES_SELECTED": "files selected", "CLEAR_ALL": "Clear All"}, "FUND_GROUP": {"COUNT": {"ONE": "Fund", "TWO": "Two Funds", "FEW": "Funds", "MANY": "Funds"}}, "LOGIN_PAGE": {"LOGIN": "<PERSON><PERSON>", "USER_NAME": "Username", "USER_NAME_IS_REQUIRED": "Username is required", "PASSWORD": "Password", "PASSWORD_IS_REQUIRED": "Password is required", "LOGIN_SUCCESS": "Login successful", "LOGIN_FAILED": "<PERSON><PERSON> failed", "LOGOUT": "Logout", "LOGOUT_SUCCESS": "Logged out successfully", "LOGOUT_FAILED": "An error occurred during logout. Please try again.", "WELCOME_TEXT": "Welcome to ArabDT!", "WELCOME_BACK": "Welcome Back", "APP_DESCRIPTION": "Real Estate Investment Fund Management Application", "ENTER_CREDENTIALS": "Enter your username and password to login", "LOGIN_BUTTON": "<PERSON><PERSON>", "COPYRIGHT": "All rights reserved for Public Investment Fund", "LANG": "عربي", "CHANGE_PASSWORD": "Change Password", "NEW_PASSWORD": "new Password", "CONFIRM_PASSWORD": "Confirm Password", "PASSWORD_RULES": {"TITLE": "This field must follow the following specifications:", "RULE_1": "A. At least one number (0–9).", "RULE_2": "B. At least one lowercase letter (a–z).", "RULE_3": "C. At least one non-alphanumeric character (e.g., !, @, #, $, %, ^, &, (, )).", "RULE_4": "D. At least one uppercase letter (A–Z).", "RULE_5": "E. Minimum length of 6 characters.", "RULE_6": "<PERSON>. At least one unique character."}, "PASSWORDS_DO_NOT_MATCH": "Passwords do not match", "CHANGE_PASSWORD_SUCCESSFUL": "Your password has been changed successfully."}, "RESOLUTIONS": {"TITLE": "Resolutions", "CREATE_NEW": "Create New Resolution", "CREATE_TITLE": "Create New Resolution", "EDIT_TITLE": "Edit Resolution", "CODE": "Resolution Code", "DATE": "Resolution Date", "DATE_PLACEHOLDER": "Select resolution date", "DESCRIPTION": "Description", "DESCRIPTION_PLACEHOLDER": "Enter resolution description (optional)", "TYPE": "Resolution Type", "TYPE_PLACEHOLDER": "Select resolution type", "CUSTOM_TYPE": "Custom Type", "CUSTOM_TYPE_PLACEHOLDER": "Enter custom resolution type", "FILE": "Resolution File", "FILE_REQUIREMENTS": "File type: PDF, Maximum size: 10 MB", "FILE_UPLOAD_TEXT": "Drag file here or click to choose file", "CHOOSE_FILE": "Choose <PERSON>", "UPLOADING": "Uploading...", "VOTING_METHODOLOGY": "Voting Methodology", "VOTING_RESULT": "Members Voting Result", "ALL_ITEMS": "All Items", "MAJORITY_ITEMS": "Majority of Items", "STATUS": "Status", "ACTIONS": "Actions", "SAVE_AS_DRAFT": "Save as Draft", "SAVED_AS_DRAFT": "Resolution saved as draft successfully", "SUCCESS_SAVED": "Record Saved Successfully", "SEND": "Send", "CREATED_SUCCESSFULLY": "Resolution created successfully", "UPDATED_SUCCESSFULLY": "Resolution updated successfully", "ERROR_LOADING": "Error loading resolutions", "ERROR_CREATING": "Error creating resolution", "ERROR_UPDATING": "Error updating resolution", "ERROR_LOADING_DETAILS": "Error loading resolution details", "ERROR_FILE_TYPE": "Unsupported file type. Please choose a PDF file", "ERROR_FILE_SIZE": "File size too large. Maximum 10 MB allowed", "ERROR_FILE_UPLOAD": "Error uploading file", "ERROR_FILE_REQUIRED": "Resolution file is required", "ERROR_DATE_REQUIRED": "Resolution date is required", "ERROR_TYPE_REQUIRED": "Resolution type is required", "ERROR_CUSTOM_TYPE_REQUIRED": "Custom type name is required", "ERROR_VOTING_METHOD_REQUIRED": "Voting methodology is required", "ERROR_VOTING_RESULT_REQUIRED": "Members voting result is required", "NO_RESOLUTIONS": "No Resolutions", "TYPE_DECISION_ADDED": "Added type", "RESOLUTION_OLD_CODE": "Old Code ", "NO_RESOLUTIONS_MESSAGE": "No resolutions have been created for this fund yet", "CREATE_FIRST": "Create First Resolution", "SEARCH_PLACEHOLDER": "Search by resolution code...", "ADVANCED_SEARCH": "Advanced Search", "SEARCH": "Search", "RESET": "Reset", "ITEMS": "Items", "ITEMS_WITHOUT": "Items", "ITEM_ONLY": "<PERSON><PERSON>", "FILTER_BY_STATUS": "Filter by Status", "FILTER_BY_TYPE": "Filter by Type", "FILTER_BY_DATE_RANGE": "Filter by Date Range", "FILTER_BY_CREATED_BY": "Filter by Created By", "FROM_DATE": "From Date", "TO_DATE": "To Date", "ALL_STATUSES": "All Statuses", "ALL_TYPES": "All Types", "ALL_USERS": "All Users", "APPLY_FILTERS": "Apply Filters", "CLEAR_FILTERS": "Clear Filters", "STATUSDDL": {"DRAFT": "Draft", "PENDING": "Pending", "COMPLETING_DATA": "Completing Data", "WAITING_CONFIRMATION": "Waiting for Confirmation", "CONFIRMED": "Confirmed", "REJECTED": "Rejected", "VOTING_IN_PROGRESS": "Voting in Progress", "APPROVED": "Approved", "NOT_APPROVED": "Not Approved", "CANCELLED": "Cancelled"}, "TYPES": {"ALL_TYPES": "All Types", "BOARD_DECISION": "Board Decision", "INVESTMENT_DECISION": "Investment Decision", "OPERATIONAL_DECISION": "Operational Decision", "FINANCIAL_DECISION": "Financial Decision", "ADMINISTRATIVE_DECISION": "Administrative Decision", "OTHER": "Other"}, "CONFIRM": "Confirm", "REJECT": "Reject", "SEND_TO_VOTE": "Send to Vote", "CONFIRM_RESOLUTION": "Confirm Resolution", "CONFIRM_RESOLUTION_TEXT": "Are you sure you want to confirm this resolution?", "REJECT_RESOLUTION": "Reject Resolution", "REJECT_RESOLUTION_TEXT": "Please provide a reason for rejecting this resolution:", "REJECTION_REASON": "Rejection Reason", "ENTER_REJECTION_REASON": "Enter rejection reason...", "REJECTION_REASON_REQUIRED": "Rejection reason is required", "SEND_TO_VOTE_TEXT": "Are you sure you want to send this resolution for voting by board members?", "CONFIRMED_SUCCESSFULLY": "Resolution confirmed successfully", "REJECTED_SUCCESSFULLY": "Resolution rejected successfully", "SENT_TO_VOTE_SUCCESSFULLY": "Resolution sent to vote successfully", "CONFIRM_FAILED": "Failed to confirm resolution", "REJECT_FAILED": "Failed to reject resolution", "SEND_TO_VOTE_FAILED": "Failed to send resolution to vote", "CONFIRM_CANCEL": "Cancel Resolution", "DELETE_RESOLUTION": "Delete Resolution", "CANCEL_CONFIRM": "Are you sure you want to cancel this resolution?", "VOTING": {"ONE_MEMBER": "Member", "MEMBERS": "Members", "APPROVED": "Approved", "REJECTED": "Rejected", "CONFLICT": "Conflict", "NOT_VOTED": "Not Voted", "APPROVED_NUM": "Approved", "REJECTED_NUM": "Not Approved", "NOT_VOTED_NUM": "Not Voted", "APPROVERS_MEMBERS": "Approvers", "REJECTORS_MEMBERS": "Rejectors", "NOT_VOTED_MEMBERS": "Not Voted", "SEND_REMINDER": "Send Reminder", "REVOTE": "Request Re-vote", "REVOTE_REQUEST": "Request re-voting", "CONFIRM_REMINDER_TEXT": "Are you sure you want to send a reminder to {{memberName}} for resolution {{resolutionId}}?", "REMINDER_SENT_SUCCESSFULLY": "<PERSON><PERSON><PERSON> sent successfully", "REVOTE_REQ_SEND_SUCCESSFULLY": "Re-vote request sent successfully", "CONFIRM_REVOTE_REQUEST": "Are you sure you want to send re-vote request?", "CONFIRM_ACCEPT_REVOTE_REQUEST": "Are you sure you want to accept the re-voting request for Resolution {{resolutionId}} from {{memberName}}? This will set the resolution status to 're-voting'.", "CONFIRM_REJECT_REVOTE_REQUEST": "Are you sure you want to reject the re-voting request for Resolution {{resolutionId}} from {{memberName}}?", "ENTER_REJECT_RESOAN": "Enter reject reason", "REVOTE_REQ_ACCEPTED": "Re-voting request for Resolution {{resolutionId}} has been Approved.", "REVOTE_REQ_REJECTED": "Re-voting request for Resolution {{resolutionId}} has been Rejected."}}, "ASSESSMENTS": {"FILES": "Files", "FILE": "Decision File", "TITLE": "Assessments", "CREATE_NEW": "Create New Assessment", "EDIT_ASSESSMENT": "Edit Assessment", "CREATE_FIRST": "Create First Assessment", "SEARCH": "Search", "SEARCH_PLACEHOLDER": "Search assessments...", "ADVANCED_SEARCH": "Advanced Search", "NO_DATA": "No Assessments Available", "NO_ASSESSMENTS": "No assessments have been created for this fund yet", "LOADING": "Loading assessments...", "LOADING_ASSESSMENT": "Loading assessment data...", "LOADING_FILTERS": "Loading filters...", "LOAD_ERROR": "Error loading assessments", "INVALID_FUND_ID": "Invalid fund ID provided", "INVALID_ASSESSMENT_ID": "Invalid assessment ID provided", "ASSESSMENT_NOT_FOUND": "Assessment not found", "ITEMS": "assessments", "REJECT": "Reject", "DELETE": "Delete", "APPROVE_SUCCESS": "Assessment approved successfully", "REJECT_SUCCESS": "Assessment rejected successfully", "DELETE_SUCCESS": "Assessment deleted successfully", "APPROVE_ERROR": "Failed to approve assessment", "Distribute_ERROR": "Failed to distribute assessment", "REJECT_ERROR": "Failed to reject assessment", "DELETE_ERROR": "Failed to delete assessment", "APPROVE_FAILED": "Failed to approve assessment", "REJECT_FAILED": "Failed to reject assessment", "DELETE_CONFIRM": "Are you sure you want to delete the assessment '{{title}}'? This action cannot be undone.", "Distribute_CONFIRM": "Are you sure you want to send this assessment to board members? It cannot be recalled.", "REJECT_ASSESSMENT_TITLE": "Reject Assessment: {{title}}", "REJECT_CONFIRMATION_MESSAGE": "Please provide a reason for rejecting this assessment. This information will be recorded for audit purposes.", "REJECTION_REASON": "Rejection Reason", "REJECTION_REASON_PLACEHOLDER": "Enter the reason for rejection...", "ADDITIONAL_COMMENTS": "Additional Comments", "ADDITIONAL_COMMENTS_PLACEHOLDER": "Enter any additional comments (optional)...", "QUESTIONNAIRE": "Questionnaire", "ATTACHMENT": "Attachment", "Distribute": "Distribute", "Distribute_SUCCESS": "Assessment Distributed successfully", "FILTER_BY_STATUS": "Filter by Status", "FILTER_BY_TYPE": "Filter by Type", "ALL_STATUSES": "All Statuses", "ALL_TYPES": "All Types", "FROM_DATE": "From Date", "TO_DATE": "To Date", "APPLY_FILTERS": "Apply Filters", "RESET": "Reset", "CREATED_BY": "Created By", "CREATED_DATE": "Created Date", "QUESTIONS_COUNT": "Questions", "RESPONSES": "Responses", "DETAILS": "Details", "RESPOND": "Respond", "RESULTS": "Results", "APPROVE": "Approve", "DISTRIBUTE": "Distribute", "VIEW_DETAILS": "View Details", "VIEW_RESULTS": "View Results", "Edit": "Edit", "Delete": "Delete", "STATUS": {"DRAFT": "Draft", "WAITINGFORAPPROVAL": "Waiting for <PERSON><PERSON><PERSON><PERSON>", "APPROVED": "Approved", "ACTIVE": "Active", "COMPLETED": "Completed", "REJECTED": "Rejected"}, "RESPONSE_STATUS": {"PENDING": "Pending", "SUBMITTED": "Submitted", "UNKNOWN": "Unknown"}, "TYPE": {"QUESTIONNAIRE": "Questionnaire", "ATTACHMENT": "Attachment"}, "COLUMNS": {"TITLE": "Title", "TYPE": "Type", "STATUS": "Status", "CREATED_BY": "Created By", "CREATED_DATE": "Created Date", "UPDATED_AT": "Updated At", "ACTIONS": "Actions"}, "ASSESSMENT_TITLE": "Assessment Title", "ASSESSMENT_TITLE_PLACEHOLDER": "Enter assessment title", "ASSESSMENT_TYPE": "Assessment Type", "ASSESSMENT_STATUS": {"UNDER_REVIEW": "Under Review", "APPROVED": "Approved", "REJECTED": "Rejected", "DRAFT": "Draft", "DISTRIBUTED": "Distributed", "COMPLETED": "Completed", "PENDING": "Pending", "UNKNOWN": "Unknown"}, "DESCRIPTION": "Description", "DESCRIPTION_PLACEHOLDER": "Enter assessment description (optional)", "INSTRUCTIONS": "Instructions", "INSTRUCTIONS_PLACEHOLDER": "Enter instructions for participants (optional)", "BASIC_INFO": "Basic Information", "ASSESSMENT_QUESTIONS": "Assessment Questions", "QUESTIONS": "Questions", "ADD_QUESTION": "Add Question", "EDIT_QUESTION": "Edit Question", "ADD_FIRST_QUESTION": "Add First Question", "QUESTION": "Question", "QUESTION_TEXT": "Question Text", "QUESTION_TEXT_PLACEHOLDER": "Enter your question", "QUESTION_TYPE": "Question Type", "SINGLE_CHOICE": "Single Choice", "MULTI_CHOICE": "Multiple Choice", "TEXT_ANSWER": "Text Answer", "OPTIONS": "Options", "OPTIONS_DESCRIPTION": "Add options for this question (minimum 2 required)", "OPTION_PLACEHOLDER": "Option", "ADD_OPTION": "Add Option", "REMOVE_OPTION": "Remove Option", "CORRECT_ANSWER": "Correct Answer", "CORRECT_ANSWERS": "Correct Answers", "MARK_CORRECT": "<PERSON> as correct", "EXPECTED_ANSWER": "Expected Answer", "EXPECTED_ANSWER_PLACEHOLDER": "Enter the expected answer", "EXPECTED_ANSWER_DESCRIPTION": "Enter the expected answer for this text question.", "SAVE_AS_DRAFT": "Save as Draft", "UPDATE_ASSESSMENT": "Update Assessment", "SUBMIT_FOR_APPROVAL": "Submit for Approval", "SUCCESS_CREATED": "Assessment created successfully", "SUCCESS_UPDATED": "Assessment updated successfully", "SUCCESS_SAVED_DRAFT": "Assessment saved as draft", "CREATE_ERROR": "Error creating assessment", "UPDATE_ERROR": "Error updating assessment", "NO_QUESTIONS_ADDED": "No questions have been added yet", "CURRENT_ATTACHMENT": "Current Attachment", "AT_LEAST_ONE_QUESTION_REQUIRED": "At least one question is required for questionnaire type", "VALIDATION": {"TITLE_REQUIRED": "Assessment title is required", "TYPE_REQUIRED": "Assessment type is required", "QUESTIONS_REQUIRED": "At least one question is required for questionnaire type", "ATTACHMENT_REQUIRED": "Attachment is required for attachment type", "OPTION_REQUIRED": "Option text is required", "CORRECT_ANSWER_REQUIRED": "At least one correct answer must be selected", "EXPECTED_ANSWER_REQUIRED": "Expected answer is required for text questions"}, "ASSESSMENT_DETAILS": "Assessment Details", "REQUIRED": "Required", "TEXT_ANSWER_EXPECTED": "Text answer expected", "POPULATE_ERROR": "Failed to populate form data. Please refresh and try again.", "ANSWERED": "Answered", "VOTING_SUMMARY": {"APPROVED": "Approved", "REJECTED": "Rejected", "NOT_VOTED": "Not Voted"}, "RESPONSE_STATISTICS": {"PENDING_RESPONSES": "Pending Responses", "SUBMITTED_RESPONSES": "Submitted Responses", "EXPECTED_TO_SUBMIT": "Expected to Submit"}, "MEMBER_RESPONSE": {"TITLE": "Assessment Response", "RESPOND_TO_ASSESSMENT": "Respond to Assessment", "MEMBER_RESPONSE": "Member Assessment", "YOUR_RESPONSE": "Your Response", "SAVE_DRAFT": "Save as Draft", "SUBMIT_RESPONSE": "Submit Response", "RESPONSE_SAVED": "Response saved successfully", "RESPONSE_SUBMITTED": "Response submitted successfully", "SAVE_ERROR": "Failed to save response", "SUBMIT_ERROR": "Failed to submit response", "SELECT_OPTION": "Select an option", "SELECT_OPTIONS": "Select one or more options", "ENTER_ANSWER": "Enter your answer", "ANSWER_PROVIDED": "Answer Provided", "REQUIRED_FIELD": "This field is required", "RESPONSE_INSTRUCTIONS": "Please answer all questions below. You can save your progress as a draft or submit your final response.", "INVALID_ASSESSMENT_ID": "Invalid assessment ID provided", "INVALID_MEMBER_ID": "Invalid member ID provided", "INVALID_PARAMETERS": "Invalid parameters provided for assessment response", "NO_QUESTIONS_AVAILABLE": "No questions available for this assessment", "LOAD_ERROR": "Failed to load assessment data", "ASSESSMENT_NOT_FOUND": "Assessment not found", "ACCESS_DENIED": "You don't have permission to access this assessment", "NETWORK_ERROR": "Network connection error. Please check your internet connection.", "VALIDATION_ERROR_DRAFT": "Validation error while saving draft. Please check your answers.", "VALIDATION_ERROR_SUBMIT": "Validation error while submitting response. Please check your answers.", "CONFLICT_ERROR_DRAFT": "Another response is being processed. Please try saving again.", "CONFLICT_ERROR_SUBMIT": "Another response is being processed. Please try submitting again.", "FUND_MANAGER_READ_ONLY": "As a Fund Manager, you are viewing this assessment response in read-only mode.", "RESPONSE_COMPLETED": "This assessment response has been completed and cannot be edited.", "READ_ONLY_MODE": "You are viewing this assessment in read-only mode.", "CANNOT_EDIT_READ_ONLY": "You cannot edit this assessment response in read-only mode."}, "TIMELINE": {"ACTIONS_TITLE": "Actions", "STATUS_LABEL": "Status", "REJECTION_REASON": "Rejection Reason", "UNKNOWN_USER": "Unknown User", "ROLES": {"FUND_MANAGER": "Fund Manager", "BOARD_MEMBER": "Board Member", "LEGAL_COUNCIL": "Legal Council", "BOARD_SECRETARY": "Board Secretary"}, "ACTIONS": {"ASSESSMENT_CREATED": "Assessment Created", "ASSESSMENT_UPDATED": "Assessment Updated", "SUBMITTED_FOR_APPROVAL": "Submitted for Approval", "ASSESSMENT_APPROVED": "Assessment Approved", "ASSESSMENT_REJECTED": "Assessment Rejected", "ASSESSMENT_DISTRIBUTED": "Assessment Distributed", "ASSESSMENT_COMPLETED": "Assessment Completed", "ASSESSMENT_UPDATE": "Assessment Update"}, "STATUSES": {"DRAFT": "Draft", "WAITING_FOR_APPROVAL": "Waiting for <PERSON><PERSON><PERSON><PERSON>", "APPROVED": "Approved", "REJECTED": "Rejected", "ACTIVE": "Active", "COMPLETED": "Completed", "UNKNOWN": "Unknown"}}}, "name": "First name", "age": "Age", "dashboard": "Dashboard", "DASHBOARD": {"DASHBOARD": "Dashboard", "WELCOME": "Welcome", "VOTING": "Voting", "FOR_VOTING": "For Voting", "VOTE": "Vote"}, "FUND_STRATEGIES": {"RECORD_SAVED_SUCCESSFULLY": "Record Saved Successfully", "ERROR_UNEXPECTED": "An error is occurred while saving data", "FUND_STRATEGIES": "Fund Strategies", "ADD_FUND_STRATEGIES": "Add Strategies ", "STRATEGIES_Name_Ar": "Strategy Name (Arabic)", "STRATEGIES_Name_En": "Strategy name (English)", "STRATEGY_NAME_AR": "Strategy Name (Arabic)", "STRATEGY_NAME_EN": "Strategy Name (English)", "UPDATED_DATE": "Updated Date", "SORRY_THERE_IS_NO_RECORDED_DATA_TO_DISPLAY": "No records exist to display", "ACTIONS": "Actions", "EDIT": "Edit", "SAVE": "Save", "ADD": "Add", "CANCEL": "Cancel"}, "Document_Categories": {"Document_Categories": "Document Categories", "ADD_Document_Categories": "ADD Document Categories", "EDIT_Document_Categories": "Edit Document Categories", "NAME_Document_Categories": "Document Categories Name", "Document_Categories_Name_En": " Document Categories Name (English) ", "Document_Categories_NAME_AR": " Document Categories Name (Arabic)    ", "Document_Categories_NAME_EN": " Document Categories Name (English) ", "UPDATED_DATE": " Updated Date", "DISPLAY_ORDER": "Display Order", "ACTIONS": "Actions", "EDIT": "Edit", "SAVE": "Save", "ADD": "Add", "CANCEL": "Cancel", "SORRY_THERE_IS_NO_RECORDED_DATA_TO_DISPLAY": "No records exist to display", "DATA_MODIFIED_SUCCESSFULLY": "Data Modified Successfully", "RECORD_SAVED_SUCCESSFULLY": "Record Saved Successfully", "ERROR_UNEXPECTED": " An error is occurred while saving data"}, "FUND_DETAILS": {"FUND_DETAILS_DATE": "Exit Date", "EDIT_DETAILS_DATE": "Edit Exit Date", "BASIC_INFO": "Basic Data", "FUND_CODE": "Fund Code", "STRATEGY": "Strategy", "BUILDING_COUNT": "Number of properties", "CREATION_DATE": "Initiation date", "END_DATE": "Exit Date", "STATUS": "Status", "EXIT": "Exited", "DICISIONS": "Decisions", "RESOLUTIONS": "Resolutions", "RATINGS": "Evaluations", "DOCUMENTS": "Documents", "MEETINGS": "Meetings", "MEMBERS": "Members", "ACTIVATED": "Activate", "NO_RECORDS": "No records exist to display", "NOTIFICATIONS_LIST": "    Notifications List  ", "STATUS_HISTORY": " Status History"}, "DOCUMENTS": {"TITLE": "Documents", "DESCRIPTION": "Manage and organize fund documents", "UPLOAD_DOCUMENT": "Upload Document", "CATEGORY": "Category", "SELECT_CATEGORY": "Select document category", "DOCUMENT_TITLE": "Document Title", "DOCUMENT_TITLE_PLACEHOLDER": "Enter document title (optional)", "SELECT_FILES": "Select Files", "UPLOADING": "Uploading...", "UPLOAD": "Upload", "UPLOAD_SUCCESS": "Document uploaded successfully", "UPLOAD_FAILED": "Failed to upload document", "LOADING": "Loading documents...", "NO_DOCUMENTS": "No Documents Found", "NO_DOCUMENTS_MESSAGE": "No documents have been uploaded for this category yet.", "NO_CATEGORIES": "No Document Categories", "NO_CATEGORIES_MESSAGE": "No document categories are available at this time.", "UNKNOWN_CATEGORY": "Unknown Category", "UNKNOWN_FILE": "Unknown File", "VIEW": "View", "DELETE": "Delete", "DOWNLOAD": "Download", "DOWNLOAD_TO_VIEW": "Download to View", "DOWNLOAD_INSTEAD": "Download Instead", "LOADING_PREVIEW": "Loading preview...", "PREVIEW_NOT_SUPPORTED": "Preview Not Available", "PREVIEW_NOT_SUPPORTED_MESSAGE": "This file type cannot be previewed. Please download the file to view it.", "PREVIEW_FAILED": "Preview Failed", "PREVIEW_ERROR": "Preview Error", "PREVIEW_ERROR_DETAILS": "The document preview could not be loaded. You can try downloading the file instead or retry the preview.", "DELETE_CONFIRM": "Are you sure you want to delete {{fileName}}?", "DELETE_SUCCESS": "Document deleted successfully", "DELETE_FAILED": "Failed to delete document", "COLUMNS": {"NAME": "Document Name", "SIZE": "File Size", "UPLOAD_DATE": "Upload Date", "ACTIONS": "Actions"}, "ERRORS": {"LOAD_CATEGORIES_FAILED": "Failed to load document categories", "LOAD_DOCUMENTS_FAILED": "Failed to load documents", "CREATE_DOCUMENT_FAILED": "Failed to create document records", "CREATE_SINGLE_DOCUMENT_FAILED": "Failed to create document record for {{fileName}}", "PREVIEW_FAILED": "Failed to load document preview", "DOWNLOAD_FAILED": "Failed to download document"}}, "NOTIFICATIONS": {"TITLE": "Notifications", "MARK_ALL_READ": "Mark all as read", "LOADING": "Loading...", "LOADING_MORE": "Loading more...", "NO_NOTIFICATIONS": "No notifications", "VIEW_ALL": "View all notifications", "ERROR_LOADING": "Error loading notifications", "RETRY": "Retry", "MARK_AS_READ": "<PERSON> as read", "NEW_NOTIFICATION": "New notification", "MINUTES_AGO": "{0} minutes ago", "HOURS_AGO": "{0} hours ago", "DAYS_AGO": "{0} days ago", "NOW": "Now"}, "INVESTMENT_FUNDS": {"NO_DATA": "No records exist to display", "TITLE": "Investment Funds", "SEARCH_PLACEHOLDER": "Search new fund", "CREATE_NEW_FUND": "Create New Fund", "UPDATE_FUND": "Update Fund", "COMPLETE_FUND_INFO": "Complete Fund Information", "SUCCESS_SAVED": "Record Saved Successfully", "GROUPS": {"DEVELOPMENT": "Development", "INCOME": "Income Generator", "CLOSED": "Closed"}, "STATUS": {"ACTIVE": "Active", "EXITED": "Exited"}, "DATES": {"EXIT_DATE": "Exit Date", "CREATION_DATE": "Initiation Date"}, "FUND_NAMES": {"HARAMAIN_REIT": "Jadwa REIT Al Haramain Fund"}, "FORM": {"FUND_NAME": "Fund Name", "FILTER": "Advanced Search", "FUND_NAME_PLACEHOLDER": "Enter fund name here...", "OLD_CODE": "Old Fund Code", "STATUS": "Status", "OLD_CODE_PLACEHOLDER": "Enter old fund code here...", "STRATEGY": "Strategy", "STRATEGY_PLACEHOLDER": "Select strategy...", "PROPERTY_COUNT": "Number of Properties", "PROPERTY_COUNT_PLACEHOLDER": "Enter number of properties...", "CREATION_DATE": "Initiation Date", "CREATION_DATE_FROM": "Initiation Date From", "CREATION_DATE_TO": "Initiation Date To", "CREATION_DATE_PLACEHOLDER": "Select Initiation date...", "EXIT_DATE": "Exit Date", "EXIT_DATE_PLACEHOLDER": "Select exit date...", "VOTING_METHOD": "Voting Methodoloy", "VOTING_MEMBERS": "Majority", "VOTING_ALL": "All Members", "FUND_MANAGER": "Fund Manager", "SECRETARY": "Board secretary", "LEGAL_ADVISOR": "Legal council", "SELECT_OFFICIALS": "Select from list...", "TERMS_FILE": "Upload TNC", "DRAG_DROP_FILES": "Drag and drop files here to upload", "FILE_SIZE_LIMIT": "Maximum file size allowed is 10MB, and files must be in PDF format", "VALIDATION": {"REQUIRED": "Required field", "MIN_LENGTH": "Field must be at least {{value}} characters", "MAX_LENGTH": "Field must not exceed {{value}} characters", "INVALID_NUMBER": "Please enter a valid number", "INVALID_DATE": "Please enter a valid date", "FILE_SIZE": "File size exceeds the allowed limit", "FILE_TYPE": "File type not supported"}}, "RESOLUTIONS": {"RESOLUTION_OLD_CODE": "Old Code ", "TYPE_DECISION_ADDED": "Added type", "CODE_RELATED_DECISION": "Code related decision", "SUCCESS_SAVED": "Record Saved Successfully", "TITLE": "Resolutions", "ADD": "Add Resolution", "SEARCH": "Search", "SEARCH_PLACEHOLDER": "Search by resolution number...", "SEARCHWITHCODE_PLACEHOLDER": "Search by resolution number...", "ADVANCED_SEARCH": "Advanced Search", "RESET": "Reset", "FILTER_BY_STATUS": "Filter by Status", "FILTER_BY_TYPE": "Filter by Type", "FILTER_BY_DATE_RANGE": "Filter by Date Range", "FILTER_BY_CREATED_BY": "Filter by Creator", "FROM_DATE": "From Date", "TO_DATE": "To Date", "ALL_STATUSES": "All Statuses", "ALL_TYPES": "All Types", "ALL_USERS": "All Users", "APPLY_FILTERS": "Apply Filters", "CLEAR_FILTERS": "Clear Filters", "REQUEST_CREATE": "Request Create Resolution", "BASIC_INFO": "Basic Information", "STATUS": "Status", "STATUS_DRAFT": "Draft", "STATUS_PENDING": "Pending Approval", "STATUS_APPROVED": "Approved", "STATUS_WAITING_CONFIRMATION": "Waiting for Confirmation", "STATUS_CONFIRMED": "Confirmed", "STATUS_NOT_APPROVED": "Not Approved", "STATUS_REJECTED": "Rejected", "STATUS_VOTING_IN_PROGRESS": "Voting in Progress", "STATUS_COMPLETING_DATA": "Completing Data", "STATUS_UNDER_REVIEW": "Under Review", "STATUS_CANCELLED": "Cancelled", "STATUS_EXPIRED": "Expired", "STATUS_ARCHIVED": "Archived", "TYPE_BOARD_DECISION": "Board Decision", "TYPE_INVESTMENT_DECISION": "Investment Decision", "TYPE_OPERATIONAL_DECISION": "Operational Decision", "DECISION_CODE": "Decision Code", "DECISION_DATE": "Decision Date", "ENTER_DECISION_DATE": "Enter decision date here...", "INITIATIONDATELABEL": "Initiation Date", "DECISION_TYPE": "Decision Type", "ADDED_DECISION_TYPE": "Added Decision Type", "ENTER_DECISION_TYPE": "Add decision type here...", "VOTING_MECHANISM": "Voting Mechanism for Decision", "ALL_MEMBERS": "All Members", "MAJORITY_MEMBERS": "Majority of Members", "VOTING_RESULT_BY_MEMBER": "Calculate Voting Result by Member", "MAJORITY_ITEMS": "Majority of Items", "ALL_ITEMS": "All Items", "DESCRIPTION": "Decision Description", "ENTER_DESCRIPTION": "Enter decision description here...", "ATTACHMENTS": "Attachments", "DECISION_FILE": "Decision File", "STATUS_FILE": "Decision Status", "REJECTION_REASON": "Rejection Reason", "MEMBERS_COUNT": "members", "CANCEL": "Cancel", "CODE": "code", "CANCEL_CONFIRM": "Are you sure you want to cancel this resolution?", "SAVE_AS_DRAFT": "Save as Draft", "SUBMIT": "Submit", "SEND": "Send", "VOTING_METHODOLOGY": "Voting Methodology", "VOTING_RESULT_CALCULATION": "Voting Result Calculation", "RESOLUTION_FILE": "Resolution File", "DELETE_ITEM_CONFIRM_MSG": "Are you sure you want to delete this item?", "NO_RESOLUTIONS": "No Resolutions", "NO_RESOLUTIONS_MESSAGE": "No resolutions have been created for this fund yet", "NO_SEARCH_RESULTS": "No resolutions match your search criteria", "CREATE_FIRST": "Create First Resolution", "STATUS_UNKNOWN": "Unknown", "DELETE_CONFIRM": "Are you sure you want to delete this item?", "DELETED_SUCCESS": "Item is deleted successfully", "DELETE_NOT_ALLOWED": "You don't have permission to delete this resolution", "DELETE_ERROR": "Error occurred while deleting resolution", "LOAD_ERROR": "Error occurred while loading resolutions", "NO_FUND_ID": "Fund ID is required to load resolutions", "INVALID_FUND_ID": "Invalid fund ID provided", "LOADING": "Loading resolutions...", "EDIT_RESOLUTION": "Edit Resolution Data", "Complete_RESOLUTION": "Complete Resolution Data", "RESOLUTION_DETAILS": "Resolution Details", "BACK": "Back", "RESOLUTION_INFO": "Resolution Information", "RESOLUTION_CODE": "Resolution Code", "OLD_RESOLUTION": "Old Resolution", "OLD_RESOLUTION_PLACEHOLDER": "Enter old resolution code here...", "FUND_NAME": "Fund Name", "LAST_UPDATED": "Last Updated", "LOADING_RESOLUTION": "Loading resolution data...", "RESOLUTION_NOT_FOUND": "Resolution Not Found", "RESOLUTION_NOT_FOUND_DESC": "The requested resolution could not be found or you don't have permission to access it.", "BACK_TO_LIST": "Back to List", "INVALID_PARAMETERS": "Invalid parameters provided", "TYPES_LOAD_ERROR": "Error loading resolution types", "RESOLUTION_ITEMS": "Resolution Items", "ITEM": "<PERSON><PERSON>", "ITEMS": "Items", "ADD_ITEM": "Add Item", "EDIT_ITEM": "<PERSON>em", "ITEM_TITLE": "Item Title", "ITEMS_WITHOUT": "Items", "ITEM_ONLY": "<PERSON><PERSON>", "FILES": "Files", "DATE": "Resolution Date ", "TYPE": " Resolution Type", "VOTING_RESULT": "Calculating the voting results for members", "ITEM_TITLE_PLACEHOLDER": "Item title (auto-generated)", "ITEM_DESCRIPTION": "Enter Item Description (optional)", "ITEM_DESCRIPTION_PLACEHOLDER": "Enter item description (optional, max 500 characters)", "CONFLICT_OF_INTEREST": "Has conflict of interest", "CONFLICT_MEMBERS": "Conflict Members", "SELECT_CONFLICT_MEMBERS": "Select members with conflict of interest", "SELECTED_CONFLICT_MEMBERS": "Selected Conflict Members", "HAS_CONFLICT": "Has Conflict", "MEMBERS": "Members", "VIEW_CONFLICT_MEMBERS": "View Conflict Members", "CONFLICT_MEMBERS_COUNT": "Conflict Members Count", "CONFLICT_NOTES": "Conflict Notes", "NO_CONFLICT_MEMBERS": "No conflict members found", "NO_ITEMS_YET": "No resolution items have been added yet", "ADDITIONAL_ATTACHMENTS": "Additional Attachments", "ATTACHMENTS_COUNT": "Attachments Count", "REMAINING": "remaining", "NO_ADDITIONAL_ATTACHMENTS": "No additional attachments have been added yet", "ADD_ATTACHMENT": "Add Attachment", "UPLOAD_REQUIREMENTS": "Upload Requirements", "MAX_SIZE": "<PERSON>", "MAX_ATTACHMENTS_REACHED": "Maximum number of attachments reached", "DELETE_ATTACHMENT_CONFIRM": "Are you sure you want to delete this attachment?", "DOWNLOAD_STARTED": "Download started", "INVALID_FILE_TYPE": "Invalid file type. Only PDF files are allowed.", "FILE_TOO_LARGE": "File size too large. Maximum 10MB allowed.", "SEND_FOR_CONFIRMATION": "Send for Confirmation", "VOTE_SUSPENSION_WARNING": "This will suspend the current voting process. Are you sure you want to continue?", "REFERRAL_RESOLUTION_WARNING": "Updating approved/not approved resolutions initiates a new resolution related to this one", "NO_ITEMS_CONFIRMATION": "If the resolution items are not added, the resolution as a whole will be voted on", "UPDATE_SUCCESS": "Resolution updated successfully", "SAVED_AS_DRAFT": "Resolution saved as draft successfully", "SENT_SUCCESS": "Resolution sent successfully", "SENT_FOR_CONFIRMATION": "Resolution sent for confirmation successfully", "REFERRAL_CREATED": "Referral resolution created successfully", "UPDATE_ERROR": "Error updating resolution", "REFERRAL_ERROR": "Error creating referral resolution", "DATE_REQUIRED": "Resolution date is required", "DESCRIPTION_REQUIRED": "Resolution description is required", "TYPE_REQUIRED": "Resolution type is required", "VOTING_TYPE_REQUIRED": "Voting methodology is required", "VOTING_RESULT_REQUIRED": "Voting result calculation is required", "ATTACHMENT_REQUIRED": "Resolution file is required", "CUSTOM_TYPE_REQUIRED": "Custom type is required when 'Other' is selected", "MAX_LENGTH_ERROR": "Maximum length of custom type is 100 characters", "INVALID_STATUS": "Invalid resolution status for this operation", "LOADING_FILTERS": "Loading filter options...", "CUSTOM_TYPE_NAME": "Custom Type Name", "ITEMS_NOT_IMPLEMENTED": "Resolution items functionality will be available soon", "STATUS_1": "Draft", "STATUS_2": "Pending Approval", "STATUS_3": "Approved", "STATUS_4": "Waiting for Confirmation", "STATUS_5": "Voting in Progress", "STATUS_6": "Confirmed", "STATUS_7": "Not Approved", "STATUS_8": "Rejected", "STATUS_9": "Cancelled", "STATUS_10": "Expired", "REFERRED_RESOLUTION_CODE": "Associated Resolution Code", "ACTIONS": "Actions", "TIMELINE": {"ACTIONS": {"RESOLUTION_CREATION": "Resolution Creation", "RESOLUTION_DATA_UPDATE": "Resolution Data Update", "RESOLUTION_REJECTED": "Resolution Rejected", "RESOLUTION_APPROVED": "Resolution Approved", "RESOLUTION_UPDATE": "Resolution Update"}, "STATUSES": {"DRAFT": "Draft", "PENDING": "Pending", "COMPLETING_DATA": "Completing Data", "WAITING_FOR_CONFIRMATION": "Waiting for Confirmation", "CONFIRMED": "Confirmed", "REJECTED": "Rejected", "VOTING_IN_PROGRESS": "Voting in Progress", "APPROVED": "Approved", "NOT_APPROVED": "Not Approved", "CANCELLED": "Cancelled", "UNKNOWN": "Unknown"}, "ROLES": {"FUND_MANAGER": "Fund Manager"}}, "HAVE_CONFLICT": "Has conflict of interest", "MEMBERS_HAVE_CONFLICT": "Members with conflict of interest", "DESCRIPTION_CARD": "Description", "LAST_UPDATED_DATE": "Last Updated Date"}, "VOTING": {"TITLE": "Vote on the resolution", "REJECT": "Reject", "AGREE": "Agree", "DISAGREEING_ITEM": "Disagreeing Item", "COMMENTS": "Comments", "ADD_COMMENT": "Add Comment", "ITEM_COMMENTS": "Item Comments", "CANCEL": "Cancel", "VOTING_COMMENTS": "Voting Comments", "DECISION_COMMENTS": "Decision Comments", "SUBMIT_VOTE": "Submit <PERSON><PERSON>", "REQUEST_REVOTE": "Request Revote", "CONFIRM": "Confirm", "RETURN": "Return", "WRITE_HERE": "Write here", "ITEM_APPROVED": "<PERSON>em Approved", "APPROVED": "Approved", "ITEM_REJECTED": "<PERSON><PERSON> Rejected  ", "ITEM_CONFLICT": " Item Conflict", "NOW": "Now", "MINUTE": "Minute", "SINCE": "Since", "HOUR": "Hour", "DAY": "Day", "VOTING_SEND_SUCCESSFUL": "Your vote has been submitted successfully.", "MEMBER_VOTING_DETAILS": "  Member Voting Details", "VOTING_FAILED": "Please select your vote (Applicable or Not Applicable) for all required items.", "ItemVotingApproved": "<PERSON><PERSON>", "ItemVotingUnApproved": "<PERSON>em UnApproved", "ItemVotingNotVotedYet": "Not Voted Yet", "ResolutionVotingApproved": "Aprroved", "ResolutionVotingUnApproved": "UnApproved", "REPLY_SUBMITTED_SUCCESSFULLY": "Reply submitted successfully"}, "MEMBERS": {"MEMBERS": "Members", "ADD_MEMBER": "Add Member", "ADD_MEMBER_TO_BOX": "Add Member to Fund", "SEARCH": "Search...", "LAST_UPDATED_DATE": "Last Updated Date", "MEMBER": "Member", "MEMBER_NAME": "Member Name", "CHOOSE_MEMBER_HERE": "Choose member name here...", "TYPE_MEMBER": "Member Type", "CEO": "Board Chairman", "IS_CHAIRMAN": "Is Board Chairman", "INDEPENDENT": "Independent", "DEPENDENT": "Not Independent", "ADD": "Add", "CANCEL": "Cancel", "SUCCESS_ADDED": "Member added successfully", "SUCCESS_SAVED": "Record Saved Successfully", "ERROR_REQUIRED_FIELD": "Required Field", "ERROR_UNKNOWN": "An error occurred while saving data", "ERROR_MAX_INDEPENDENT_MEMBERS": "You have reached the maximum number of independent board members of this fund", "NO_MEMBERS": "No board members found", "NO_MEMBERS_MESSAGE": "No board members have been added to this fund yet", "LOADING": "Loading board members...", "LOAD_ERROR": "An error occurred, can't display data", "MAX_MEMBERS_REACHED": "Maximum number of board members reached (15)", "ACTIVE": "Active", "INACTIVE": "Inactive", "CHAIRMAN": "Chairman", "LAST_UPDATED": "Last Updated", "MINIMUM_MEMBERS_WARNING": "You should add board members to be able to perform fund activities, at least 2 independent members", "ADD_MEMBERS": " Add members"}}, "USER_MANAGEMENT": {"TITLE": "User Management", "ADD_USER": "Add User", "ADD_FIRST_USER": "Add First User", "LOADING_USERS": "Loading users...", "NO_USERS": "No Users", "NO_USERS_MESSAGE": "No users have been added yet", "NO_RESULTS": "No Results Found", "NO_RESULTS_MESSAGE": "No users match your search criteria. Try adjusting your filters.", "FILTERS": {"MOBILE_PLACEHOLDER": "Search by mobile number", "ADVANCED_FILTERS": "Advanced Filters", "CLEAR_FILTERS": "Clear Filters", "APPLY_FILTERS": "Apply Filters", "RESET": "Reset", "NAME": "Name", "NAME_PLACEHOLDER": "Name", "STATUS": "Status", "ALL_STATUSES": "Status", "ROLE": "Role", "ALL_ROLES": "Role"}, "COLUMNS": {"NAME": "Name", "EMAIL": "Email", "MOBILE": "Mobile", "STATUS": "Status", "ROLE": "Role", "LAST_UPDATE_DATE": "Last Update Date", "ACTIVATE": "Activate", "ACTIONS": "Actions"}, "STATUS": {"ACTIVE": "Active", "INACTIVE": "Inactive", "PENDING": "Pending", "SUSPENDED": "Suspended", "UNKNOWN": "Unknown"}, "ACTIONS": {"EDIT": "Edit", "DELETE": "Delete", "VIEW_DETAILS": "View Details", "ACTIVATE": "Activate", "DEACTIVATE": "Deactivate", "RESET_PASSWORD": "Reset Password", "RESEND_MESSAGE": "Resend Message", "RESEND": "Resend"}, "MESSAGES": {"USER_CREATED": "User created successfully", "USER_UPDATED": "User updated successfully", "USER_DELETED": "User deleted successfully", "DELETE_CONFIRMATION": "Are you sure you want to delete this user?", "OPERATION_FAILED": "Operation failed, please try again"}, "CONFIRM": {"TITLE": "Confirm Action", "ACTIVATE_USER": "Are you sure you want to activate {{name}}?This user will be able to log in.", "DEACTIVATE_USER": "Are you sure you want to deactivate {{name}}?This user will no longer be able to log in.", "RESET_PASSWORD_MESSAGE": "Are you sure you want to reset the password for {{name}}? A new temporary password will be sent via WhatsApp.", "RESEND_MESSAGE": "Are you sure you want to resend the account registration message to {{name}}?"}, "SUCCESS": {"STATUS_UPDATE_SUCCESS": "User status updated successfully", "RESET_PASSWORD_SUCCESS": "Password reset successfully. A new temporary password has been sent to the user via WhatsApp.", "RESEND_MESSAGE_SUCCESS": "Account registration message sent successfully", "ACTIVATE_SUCCESS": "User {{name}} has been activated successfully.", "DEACTIVATE_SUCCESS": "User {{name}} has been deactivated successfully."}, "ERRORS": {"STATUS_UPDATE_FAILED": "Failed to update user status", "RESET_PASSWORD_FAILED": "An error is occurred while resetting password.", "RESEND_MESSAGE_FAILED": "Failed to send account registration message. Please try again.", "ACTIVATE_FAILED": "Failed to activate user {{name}}. Please try again.", "DEACTIVATE_FAILED": "Failed to deactivate user {{name}}. Please try again.", "ACTIVATE_409": "Cannot activate user {{name}} due to role conflicts.", "DEACTIVATE_409": "Cannot deactivate user {{name}} due to system constraints.", "ACTIVATE_404": "User {{name}} not found. Cannot activate.", "DEACTIVATE_404": "User {{name}} not found. Cannot deactivate.", "OPERATION_FAILED": "Operation failed. Please try again.", "RESET_PASSWORD_ERROR": "Failed to reset password", "RESEND_MESSAGE_ERROR": "Failed to resend message", "ACTIVATE_ERROR": "Failed to activate user", "DEACTIVATE_ERROR": "Failed to deactivate user"}, "ERROR_TITLES": {"ACTIVATE": "User Activation Error", "DEACTIVATE": "User Deactivation Error", "RESETPASSWORD": "Password Reset Error", "RESENDMESSAGE": "Message Resend Error"}, "REGISTRATION": {"COMPLETED": "Completed", "PENDING": "Pending"}, "MESSAGE": {"SENT": "<PERSON><PERSON>", "NOT_SENT": "Not Sent"}, "CREATE": {"PAGE_TITLE": "Add New User", "FORM_TITLE": "User Information", "FORM_SUBTITLE": "Please fill in all required fields to create a new user account", "PERSONAL_INFO": "Personal Information", "WORK_INFO": "Work Information", "EMERGENCY_CONTACT": "Emergency Contact", "DOCUMENTS_FILES": "Documents & Files", "ACCOUNT_SETTINGS": "Account <PERSON><PERSON>", "NAME": "Name", "NAME_PLACEHOLDER": "Enter name", "EMAIL": "Email", "EMAIL_PLACEHOLDER": "Enter email", "COUNTRY_CODE": "Code", "COUNTRY_CODE_PLACEHOLDER": "+966", "MOBILE": "Mobile Number", "MOBILE_PLACEHOLDER": "05XXXXXXX or 5XXXXXXX (Saudi mobile format)", "PHONE_NUMBER": "Mobile Number (User Name)", "NATIONALITY": "Nationality", "NATIONALITY_PLACEHOLDER": "Enter nationality", "PASSPORT_NO": "Passport Number", "PASSPORT_NO_PLACEHOLDER": "********* (1 letter + 8 digits)", "ROLE": "Role", "ROLE_PLACEHOLDER": "Select role", "STATUS": "Status", "STATUS_PLACEHOLDER": "Select status", "IBAN": "IBAN", "IBAN_PLACEHOLDER": "************************ (SA + 22 digits)", "CV": "CV", "CV_PLACEHOLDER": "Upload CV or Resume (PDF, DOC, DOCX - Max 10MB)", "PERSONAL_PHOTO": "Personal Photo", "PERSONAL_PHOTO_PLACEHOLDER": "Upload photo (JPG, PNG - Max 2MB)", "PASSWORD": "Password", "PASSWORD_PLACEHOLDER": "Enter password (minimum 8 characters)", "IS_ACTIVE": "Active", "REGISTRATION_MESSAGE_SENT": "Registration Message Sent", "REGISTRATION_COMPLETED": "Registration Completed", "SUBMIT": "Add User", "CANCEL": "Cancel", "CREATING_USER": "Creating user...", "SUCCESS": "User created successfully", "ERROR": "Failed to add user", "VALIDATION_ERROR": "Please correct the errors in the form", "UNIQUE_ROLE_SINGLE_ONLY": "The selected role can only be assigned as a single role. Multi-select is not allowed for Legal Counsel, Finance Controller, Compliance and Legal Managing Director, or Head of Real Estate positions.", "MAX_TWO_ROLES": "Maximum of 2 roles allowed per user.", "INVALID_TWO_ROLE_COMBINATION": "Invalid two-role combination. Only 'Board Member + Fund Manager' or 'Board Member + Associate Fund Manager' combinations are allowed.", "ROLE_REPLACEMENT_TITLE": "Replace Existing User", "ROLE_REPLACEMENT_MESSAGE": "There is already an active user ({{existingUser}}) with the role '{{role}}'.\nDo you want to deactivate them and assign this role to the new user?", "REPLACE_USER": "Replace User", "ROLE_CONFLICT_TITLE": "Role Conflict Detected", "ROLE_CONFLICT_MESSAGE": "The following role(s) already have active users: {{roles}}. Are you sure you want to assign these roles? This will deactivate the existing users.", "CONFIRM_ASSIGN_ROLES": "Yes, Assign Roles", "ROLES_LOAD_ERROR": "Failed to load available roles. Please try again."}, "ROLE_CONFLICT": {"TITLE": "Role Already Assigned", "MESSAGE": "The role '{{roleName}}' is already assigned to {{existingUserName}}. Do you want to replace them with the new user?", "REPLACE": "Replace User", "REPLACEMENT_SCHEDULED": "User replacement scheduled. {{existingUserName}} will be deactivated when the new user is created for role '{{roleName}}'."}, "ROLE_AVAILABILITY": {"TITLE": "Role Already Taken", "MESSAGE": "There is another active user with the role {{roleName}}: {{userName}}.<br>Do you want to replace him?", "CONFIRM": "Yes, Replace User"}, "EDIT": {"PAGE_TITLE": "Edit User", "FORM_TITLE": "Edit User Information", "FORM_SUBTITLE": "Update user details and roles", "NAME": "Name", "NAME_PLACEHOLDER": "Enter name", "EMAIL": "Email", "EMAIL_PLACEHOLDER": "Enter email", "MOBILE": "Mobile Number", "MOBILE_PLACEHOLDER": "Mobile number (not editable)", "MOBILE_READONLY_HINT": "Mobile number cannot be changed", "PHONE_NUMBER": "Mobile Number (User Name)", "IBAN": "IBAN", "IBAN_PLACEHOLDER": "Enter IBAN (SA format)", "NATIONALITY": "Nationality", "NATIONALITY_PLACEHOLDER": "Enter nationality", "PASSPORT_NO": "Passport Number", "PASSPORT_NO_PLACEHOLDER": "Enter passport number (A1234567)", "CV": "CV Document", "CV_PLACEHOLDER": "Upload CV (PDF, DOC, DOCX - Max 10MB)", "PERSONAL_PHOTO": "Personal Photo", "PERSONAL_PHOTO_PLACEHOLDER": "Upload photo (JPG, PNG - Max 2MB)", "ROLE": "Roles", "ROLE_PLACEHOLDER": "Select user roles", "STATUS": "Status", "STATUS_READONLY_HINT": "Status is managed separately", "UPDATE": "Update User", "CANCEL": "Cancel", "UPDATING_USER": "Updating user...", "LOADING_USER_DATA": "Loading user data...", "READONLY_FIELDS_NOTICE": "Note: Mobile number and status cannot be edited from this form.", "USER_NOT_FOUND": "User not found or you don't have permission to edit this user.", "INVALID_USER_ID": "Invalid user ID provided.", "LOAD_ERROR": "Failed to load user data. Please try again.", "UPDATE_ERROR": "Failed to update user. Please try again.", "POPULATE_ERROR": "Failed to populate form with user data.", "VALIDATION_ERROR": "Please correct the errors in the form before submitting.", "SUCCESS": "User updated successfully", "COUNTRY_CODE": "Country Code", "SUCCESS_UPDATE": "Record Updated Successfully."}, "ROLES": {"ADMIN": "Administrator", "MANAGER": "Manager", "EMPLOYEE": "Employee", "VIEWER": "Viewer", "SYSTEM_ADMIN": "System Administrator", "FUND_MANAGER": "Fund Manager", "ASSOCIATE_FUND_MANAGER": "Associate Fund Manager", "BOARD_MEMBER": "Board Member", "LEGAL_COUNSEL": "Legal Council", "BOARD_SECRETARY": "Board Secretary", "INVESTMENT_COMMITTEE_MEMBER": "Investment Committee Member", "FINANCE_CONTROLLER": "Finance Controller", "COMPLIANCE_LEGAL_MANAGING_DIRECTOR": "Compliance and Legal Managing Director", "HEAD_OF_REAL_ESTATE": "Head of Real Estate"}, "VALIDATION": {"INVALID_SAUDI_MOBILE": "Please enter a valid Saudi mobile number (9 digits starting with 5)", "EMAIL_ALREADY_EXISTS": "This email is already registered"}, "DEPARTMENTS": {"HR": "Human Resources", "FINANCE": "Finance", "IT": "Information Technology", "OPERATIONS": "Operations", "MARKETING": "Marketing"}, "CONTRACT_TYPES": {"FULL_TIME": "Full Time", "PART_TIME": "Part Time", "CONTRACT": "Contract", "INTERN": "Intern"}, "WORK_LOCATIONS": {"OFFICE": "Office", "REMOTE": "Remote", "HYBRID": "Hybrid"}, "BREADCRUMB": {"HOME": "Home", "USER_MANAGEMENT": "User Management", "CREATE_USER": "Add User"}}, "USER_PROFILE": {"PAGE_TITLE": "My Profile", "VIEW_TITLE": "User Details", "PERSONAL_PHOTO": "Personal Photo", "NAME": "Name", "NAME_PLACEHOLDER": "Enter your name", "EMAIL": "Email", "EMAIL_PLACEHOLDER": "Enter your email", "COUNTRY_CODE": "Country Code", "MOBILE": "Mobile Number", "IBAN": "IBAN", "IBAN_PLACEHOLDER": "Enter your IBAN (SA format)", "NATIONALITY": "Nationality", "NATIONALITY_PLACEHOLDER": "Enter your nationality", "PASSPORT_NO": "Passport Number", "PASSPORT_NO_PLACEHOLDER": "Enter your passport number", "CV": "CV", "CV_UPLOADED": "CV Document Available", "STATUS": "Status", "ROLE": "Role", "CHANGE_PASSWORD": "Change Password", "CHANGE_PASSWORD_DESCRIPTION": "Update your account password for security", "CURRENT_PASSWORD": "Current Password", "ENTER_CURRENT_PASSWORD": "Enter your current password", "NEW_PASSWORD": "New Password", "ENTER_NEW_PASSWORD": "Enter your new password", "CONFIRM_NEW_PASSWORD": "Confirm New Password", "PASSWORD_CHANGED_SUCCESSFULLY": "Password changed successfully", "PASSWORD_CHANGE_FAILED": "Failed to change password", "CURRENT_PASSWORD_INCORRECT": "Current password is incorrect", "PASSWORD_COMPLEXITY_ERROR": "Password does not meet complexity requirements", "PASSWORD_MISMATCH": "Passwords do not match", "USER_ID_REQUIRED": "User ID is required", "LOADING_PROFILE": "Loading profile...", "UPDATE_SUCCESS": "Profile updated successfully", "UPDATE_ERROR": "An error is occurred while saving data", "VALIDATION_ERROR": "Please correct the errors in the form", "POPULATE_ERROR": "Error loading profile data", "LOAD_ERROR": "Failed to load profile data", "NO_CV_AVAILABLE": "No CV file available for download", "UPDATE_PROFILE": "Update Profile", "MOBILE_PLACEHOLDER": "Enter mobile number (e.g., 0512345678)", "INVALID_PHOTO_TYPE": "Invalid photo type. Only JPG, JPEG, and PNG files are allowed.", "PHOTO_TOO_LARGE": "Photo file size exceeds 2MB limit.", "PHOTO_UPLOAD_ERROR": "Failed to upload photo. Please try again.", "PHOTO_UPLOAD_SUCCESS": "Photo uploaded successfully.", "UPLOAD_PHOTO": "Upload Photo", "CHANGE_PHOTO": "Change Photo", "CURRENT_PHOTO": "Current Photo", "STATUS_ACTIVE": "Active", "STATUS_INACTIVE": "Inactive", "ROLES": "Roles", "NO_ROLES": "No roles assigned", "REGISTRATION_STATUS": "Registration Status", "REGISTRATION_MESSAGE": "Registration Message", "FULL_NAME": "Name", "PASSPORT_NUMBER": "Passport Number", "CV_FILE": "CV File", "VIEW_CV": "View CV", "LAST_UPDATE_DATE": "Last Update Date"}, "MEETINGS": {"TITLE": "Scheduled Meetings", "SEARCH_PLACEHOLDER": "Search...", "ADD": "Create Meeting", "DESCRIPTION": "Description", "PARTNERS": "Partners", "PARTNER": "Partner", "DATE": "Meeting Date/Time", "PLACE": "Meeting Place", "NO_DATA": "There are no scheduled meetings to display.", "CREATE_FIRST": "Create First Meeting", "ADD_SCHEDULED_MEETING": "Create Scheduled Meeting", "MEETING_SUBJECT": "Meeting Subject", "ENTER_MEETING_SUBJECT": "Enter meeting subject", "MEETING_TYPE": "Meeting Type", "MEETING_TYPE_PLACEHOLDER": "Select meeting type", "MEETING_DATE": "Date", "MEETING_STATUS": "Meeting Status", "MEETING_STATUS_PLACEHOLDER": "Select meeting status", "START_TIME": "Start Time", "END_TIME": "End Time", "MEETING_ROOM": "Meeting Room", "VIA_INTERNET": "Via Internet", "THE_MEETING_ROOM": "The Meeting Room", "MEETING_ROOM_PLACEHOLDER": "Enter Meeting room", "MEETING_LINK": "Meeting Link", "MEETING_DESCRIPTION": "Meeting Description", "MEETING_DESCRIPTION_PLACEHOLDER": "Enter meeting description"}, "BREADCRUMB": {"HOME": "Home", "FUNDS": "Funds", "INVESTMENT_FUNDS": "Investment Funds", "FUND_DETAILS": "Fund Details", "MEMBERS": "Board Members", "ASSESSMENTS": "Assessments", "CREATE_ASSESSMENT": "Create Assessment", "ASSESSMENT_DETAILS": "Assessment Details", "RESPONSE": "Response", "USER_MANAGEMENT": "User Management"}, "PAGINATION": {"PREVIOUS": "Previous", "NEXT": "Next", "FIRST": "First", "LAST": "Last", "PAGE": "Page", "OF": "of", "SHOWING": "Showing", "TO": "to", "RECORDS": "records", "PAGE_SIZE": "<PERSON>", "ITEMS_PER_PAGE": "items per page", "GO_TO_PAGE": "Go to page", "PAGE_INFO": "Page {{current}} of {{total}}", "RECORDS_INFO": "Showing {{start}}-{{end}} of {{total}} records", "NO_DATA": "No data to display"}, "COMMON": {"TOTAL_ITEMS": "Total Items", "SHOWING": "Showing", "OF": "of", "LOADING": "Loading...", "SUBMITTING": "Submitting...", "FILTER": "Filter", "CANCEL": "Cancel", "CLOSE": "Close", "CREATE": "Save", "UPDATE": "Update", "REQUIRED_FIELD": "Required field", "INVALID_INPUT": "Invalid input", "SUCCESS": "Success", "ERROR": "Error", "OK": "OK", "SAVE": "Save", "SAVE_CHANGES": "Save Changes", "EDIT": "Edit", "DELETE": "Delete", "DELETE_RESOLUTION": "Delete the resolution ", "REMOVE": "Remove", "CONFIRM_DELETE": "Confirm Delete", "CONFIRM_Distribute": "Confirm Distribute", "HOME": "Home", "BACK": "Back", "NEXT": "Next", "SUBMIT": "Submit", "RESET": "Reset", "APPLAY": "Apply", "DELETED": "Deleted", "RETRY": "Retry", "CONFIRM": "Confirm", "YES": "Yes", "NO": "No", "NEW": "New", "ADD": "Add", "VALIDATION_ERROR": "Validation Error", "DOWNLOAD": "Download", "Complete": "Complete", "LoadMore": "Load More", "ERROR_LOADING_DATA": "An error occurred while loading data", "VIEW_DETAILS": "View details", "EXIT_FROM_SCREEN": "Exit from screen will not saving all data", "ACCESS_DENIED_SUPER_ADMIN_REQUIRED": "Access denied. Super admin privileges required to access user management."}, "ROLES": {"legalcouncil": "Legal Council", "financecontroller": "Finance Controller", "compliancelegalmanagingdirector": "Compliance and Legal Managing Director", "headofrealestate": "Head of Real Estate", "NotValidRole": "Not Valid Role Selection."}}