import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import {
  FormGroup,
  FormBuilder,
  Validators,
  AbstractControl,
  ValidationErrors,
  ValidatorFn,
} from '@angular/forms';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { ButtonTypeEnum, IconEnum } from '@core/enums/icon-enum';
import { SizeEnum } from '@core/enums/size';
import { IBreadcrumbItem } from '@core/gl-interfaces/IBreadcrumbItem/ibreadcrumb-item';
import { ErrorModalService } from '@core/services/error-modal.service';
import { StrategyService } from '@core/services/strategy.service';
import { NgbDateStruct } from '@ng-bootstrap/ng-bootstrap';
import { TranslateModule } from '@ngx-translate/core';
import { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';
import { CustomButtonComponent } from '@shared/components/custom-button/custom-button.component';
import { FormBuilderComponent } from '@shared/components/form-builder/form-builder.component';
import { PageHeaderComponent } from '@shared/components/page-header/page-header.component';
import { InputType } from '@shared/enum/input-type.enum';
import { IControlOption } from '@shared/interfaces/i-control-option';
import { DateConversionService } from '@shared/services/date.service';
import { UserManagementService } from '@shared/services/users/user-management.service';
import {
  FundsService,
  AddFundCommand,
  EditExitDateCommand,
} from '../../services/fund.service';
import { DateValues } from '@shared/gl-models/inputs/idate-model';
import moment from 'moment';
import {
  TokenService,
  userRole,
} from 'src/app/features/auth/services/token.service';
import { FundStatus } from '@shared/enum/fund-status';
import { FundsServiceProxy, SaveFundCommand } from '@core/api/api.generated';
import { AttachmentModule } from '@shared/enum/AttachmentModule';

@Component({
  selector: 'app-update-fund',
  standalone: true,
  imports: [
    CommonModule,
    FormBuilderComponent,
    PageHeaderComponent,
    TranslateModule,
    RouterModule,
    MatDatepickerModule,
    MatNativeDateModule,
    CustomButtonComponent,
    BreadcrumbComponent,
  ],
  templateUrl: './update-fund.component.html',
  styleUrl: './update-fund.component.scss',
})
export class UpdateFundComponent {
  breadcrumbSizeEnum = SizeEnum;
  breadcrumbItems: IBreadcrumbItem[] = [
    {
      label: 'INVESTMENT_FUNDS.TITLE',
      url: '/admin/investment-funds',
      icon: 'fas fa-home',
    },
    {
      label: 'INVESTMENT_FUNDS.COMPLETE_FUND_INFO',
      url: '/admin/investment-funds/update',
      disabled: true,
    },
  ];

  formGroup!: FormGroup;
  buttonEnum = ButtonTypeEnum;
  IconEnum = IconEnum;
  maxGreg?: NgbDateStruct;
  maxHijri?: NgbDateStruct;
  isFormSubmitted: boolean | undefined = false;
  isValidationFire: boolean | undefined = false;
  formControls: IControlOption[] = [
    {
      type: InputType.Number,
      formControlName: 'id',
      id: 'id',
      name: 'id',
      label: 'INVESTMENT_FUNDS.FORM.Fund_CODE',
      placeholder: 'INVESTMENT_FUNDS.FORM.Fund_CODE_PLACEHOLDER',
      isRequired: false,
      class: 'col-md-4',
    },
    {
      type: InputType.Mixed,
      formControlName: 'oldCode',
      id: 'oldCode',
      name: 'oldCode',
      label: 'INVESTMENT_FUNDS.FORM.OLD_CODE',
      placeholder: 'INVESTMENT_FUNDS.FORM.OLD_CODE_PLACEHOLDER',
      isRequired: false,
      class: 'col-md-4',
      maxLength: 20,
    },
    {
      type: InputType.Custom,
      label: 'INVESTMENT_FUNDS.FORM.STATUS',
      name: '',
      class: 'col-md-4 d-flex align-items-start flex-column',
      formControlName: '',
      id: '',
    },
    {
      type: InputType.Text,
      formControlName: 'name',
      id: 'name',
      name: 'name',
      label: 'INVESTMENT_FUNDS.FORM.FUND_NAME',
      placeholder: 'INVESTMENT_FUNDS.FORM.FUND_NAME_PLACEHOLDER',
      isRequired: true,
      class: 'col-md-4',
    },
    {
      type: InputType.Dropdown,
      formControlName: 'strategyId',
      id: 'strategyId',
      name: 'strategyId',
      label: 'INVESTMENT_FUNDS.FORM.STRATEGY',
      placeholder: 'INVESTMENT_FUNDS.FORM.STRATEGY_PLACEHOLDER',
      isRequired: true,
      class: 'col-md-4',
      options: [],
    },
    {
      type: InputType.Number,
      formControlName: 'propertiesNumber',
      id: 'propertiesNumber',
      name: 'propertiesNumber',
      label: 'INVESTMENT_FUNDS.FORM.PROPERTY_COUNT',
      placeholder: 'INVESTMENT_FUNDS.FORM.PROPERTY_COUNT_PLACEHOLDER',
      isRequired: true,
      class: 'col-md-4',
      max: 50,
      min: 1,
      step: 1,
    },
    {
      type: InputType.Date,
      formControlName: 'initiationDate',
      id: 'initiationDate',
      name: 'initiationDate',
      label: 'INVESTMENT_FUNDS.FORM.CREATION_DATE',
      placeholder: 'INVESTMENT_FUNDS.FORM.CREATION_DATE_PLACEHOLDER',
      isRequired: true,
      class: 'col-md-4',
    },
    // {
    //   type: InputType.Date,
    //   formControlName: 'exitDate',
    //   id: 'exitDate',
    //   name: 'exitDate',
    //   label: 'INVESTMENT_FUNDS.FORM.EXIT_DATE',
    //   placeholder: 'INVESTMENT_FUNDS.FORM.EXIT_DATE_PLACEHOLDER',
    //   isRequired: false,
    //   class: 'col-md-4',
    // },
    {
      type: InputType.Radio,
      formControlName: 'votingTypeId',
      id: 'votingTypeId',
      name: 'votingTypeId',
      label: 'INVESTMENT_FUNDS.FORM.VOTING_METHOD',
      isRequired: true,
      class: 'col-md-4',
      options: [
        { name: 'INVESTMENT_FUNDS.FORM.VOTING_ALL', id: 1 },
        { name: 'INVESTMENT_FUNDS.FORM.VOTING_MEMBERS', id: 2 },
      ],
    },
    {
      type: InputType.Dropdown,
      formControlName: 'fundManagers',
      id: 'fundManagers',
      name: 'fundManagers',
      label: 'INVESTMENT_FUNDS.FORM.FUND_MANAGER',
      placeholder: 'INVESTMENT_FUNDS.FORM.SELECT_OFFICIALS',
      isRequired: true,
      maxLength: 3,
      class: 'col-md-4',
      multiple: true,
      options: [],
    },
    {
      type: InputType.Dropdown,
      formControlName: 'fundBoardSecretaries',
      id: 'fundBoardSecretaries',
      name: 'fundBoardSecretaries',
      label: 'INVESTMENT_FUNDS.FORM.SECRETARY',
      placeholder: 'INVESTMENT_FUNDS.FORM.SELECT_OFFICIALS',
      isRequired: false,
      class: 'col-md-4',
      maxLength: 4,
      multiple: true,
      options: [],
    },
    {
      type: InputType.Dropdown,
      formControlName: 'legalCouncilId',
      id: 'legalCouncilId',
      name: 'legalCouncilId',
      label: 'INVESTMENT_FUNDS.FORM.LEGAL_ADVISOR',
      placeholder: 'INVESTMENT_FUNDS.FORM.LEGAL_ADVISOR',
      isRequired: true,
      class: 'col-md-4',
      isReadonly: true,
      maxLength: 4,
    },
    {
      type: InputType.file,
      formControlName: 'attachmentId',
      id: 'attachmentId',
      name: 'attachmentId',
      label: 'INVESTMENT_FUNDS.FORM.TERMS_FILE',
      placeholder: 'INVESTMENT_FUNDS.FORM.DRAG_DROP_FILES',
      isRequired: true,
      class: 'col-md-12',
      allowedTypes: ['pdf'],
      moduleId : AttachmentModule.Fund
    },
  ];
  fund: any;
  isEditMood: boolean = false;
  title: string = 'INVESTMENT_FUNDS.COMPLETE_FUND_INFO';
  isLegalCouncil: boolean = false;
  isFundManager: boolean = false;
  constructor(
    private formBuilder: FormBuilder,
    public router: Router,
    private route: ActivatedRoute,
    private userService: UserManagementService,
    private strategyService: StrategyService,
    private fundService: FundsServiceProxy,
    private errorModalService: ErrorModalService,
    private DateConversionService: DateConversionService,
    private tokenService: TokenService
  ) {}
  integerValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const value = control.value;
      if (value === null || value === '') return null;
      return Number.isInteger(+value) ? null : { integer: true };
    };
  }
  setDateMaxAndMin() {
    const today = new Date();
    const minGreg: NgbDateStruct = { year: 2010, month: 1, day: 1 };
    const minHijri: NgbDateStruct =
      this.DateConversionService.convertGregorianToHijri(minGreg);

    this.maxGreg = {
      year: today.getFullYear(),
      month: today.getMonth() + 1,
      day: today.getDate(),
    };

    this.maxHijri = this.DateConversionService.convertGregorianToHijri(
      this.maxGreg
    );
    // this.initForm();
    const field = this.formControls.find(
      (f) => f.formControlName === 'initiationDate'
    );
    if (field) {
      field.maxGreg = this.maxGreg;
      field.maxHijri = this.maxHijri;
      field.minHijri = minHijri;
      field.minGreg = minGreg;
    }
  }
  ngOnInit() {
    // this.setDateMaxAndMin();
    this.getStrategyList();
    this.getFundManagerUsers();
    this.getBoardSecretaryUsers();
    this.getLegalCouncilUsers();
    this.getFunddetails();
    this.isLegalCouncil = this.tokenService.hasRole(userRole.legalCouncil);
    this.isFundManager = this.tokenService.hasRole(userRole.fundManager);
  }
  fundId: string | undefined;
  getFunddetails() {
    this.fundId = this.route.snapshot.paramMap.get('id') as string | undefined;
    this.fundService
      .getFundsById(parseInt(this.fundId?.toString() ?? '0'))
      .subscribe((res: any) => {
        if (res.successed) {
          this.fund = res.data;
          if (this.fund.statusId != FundStatus.UnderConstruction || (!this.isFundManager && this.fund.statusId == FundStatus.UnderConstruction)) {
            this.formControls.splice(7, 0, {
              type: InputType.Date,
              formControlName: 'exitDate',
              id: 'exitDate',
              name: 'exitDate',
              label: 'INVESTMENT_FUNDS.FORM.EXIT_DATE',
              placeholder: 'INVESTMENT_FUNDS.FORM.EXIT_DATE_PLACEHOLDER',
              isRequired: false,
              class: 'col-md-4',
            });
          }
          this.initForm();
          this.setDateMaxAndMin();

          this.setFundData();
        }
      });
  }

  shiftNgbDate(date: NgbDateStruct, days: number): NgbDateStruct {
    const jsDate = new Date(date.year, date.month - 1, date.day);
    jsDate.setDate(jsDate.getDate() + days);

    return {
      year: jsDate.getFullYear(),
      month: jsDate.getMonth() + 1,
      day: jsDate.getDate(),
    };
  }

  setFundData() {
    this.formGroup.patchValue(this.fund);
    this.setDateMaxValues();
    const attachment = {
      id: this.fund.attachmentId,
      fileName: this.fund.attachmentName,
      filePath: this.fund.attachmentPath,
    };

    const control = this.formControls.find(
      (c) => c.formControlName === 'attachmentId'
    );
    if (control) {
      control.initialFiles = [attachment];
    }

    this.formGroup.get('id')?.disable();
    this.setTheMood();
  }
  setDateMaxValues() {
    moment.locale('en');
    let initiationDate = this.DateConversionService.mapStringToSelectedDate(
      moment(this.fund.initiationDate.toJSDate()).format('DD-MM-YYYY')
    );
    let exitDate = this.DateConversionService.mapStringToSelectedDate(''
    );
    this.formGroup.get('initiationDate')?.setValue(initiationDate);
    this.formGroup.get('exitDate')?.setValue(exitDate);
    const field = this.formControls.find(
      (f) => f.formControlName === 'exitDate'
    );
    if (field && initiationDate) {
      field.minGreg = this.shiftNgbDate(initiationDate, 1);
      field.minHijri = this.DateConversionService.convertGregorianToHijri(
        field.minGreg
      );
    }
  }
  setTheMood() {
    this.isEditMood = this.fund.statusId !== FundStatus.UnderConstruction;
    if (this.isEditMood) {
      this.title = 'INVESTMENT_FUNDS.UPDATE_FUND';
      this.breadcrumbItems[1].label = 'INVESTMENT_FUNDS.UPDATE_FUND';
      const field = this.formControls.find(
        (f) => f.formControlName === 'exitDate'
      );
      field?.isReadonly;
    }
  }
  getStrategyList() {
    this.strategyService.strategyList(0, 0, '', '').subscribe((res: any) => {
      const field = this.formControls.find(
        (f) => f.formControlName === 'strategyId'
      );
      if (field) {
        field.options = res.data.map((user: any) => ({
          id: user.id,
          name: user.nameAr,
        }));
      }
    });
  }
  getFundManagerUsers() {
    this.userService.getFundManagerUsers().subscribe((managers) => {
      const field = this.formControls.find(
        (f) => f.formControlName === 'fundManagers'
      );
      if (field) {
        field.options = managers.data.map((user) => ({
          id: user.id,
          name: user.fullName,
        }));
      }
      if (!this.isLegalCouncil)
        this.formGroup
          .get('fundManagers')
          ?.setValue([Number(this.tokenService.getuserId())]);
    });
  }
  getBoardSecretaryUsers() {
    this.userService.getBoardSecretaryUsers().subscribe((managers) => {
      const field = this.formControls.find(
        (f) => f.formControlName === 'fundBoardSecretaries'
      );
      if (field) {
        field.options = managers.data.map((user) => ({
          id: user.id,
          name: user.fullName,
        }));
      }
    });
  }
  getLegalCouncilUsers() {
    this.userService.getLegalCouncilUsers().subscribe((managers) => {
      const field = this.formControls.find(
        (f) => f.formControlName === 'legalCouncilId'
      );
      if (field) {
        field.options = managers.data.map((user) => ({
          id: user.id,
          name: user.fullName,
        }));
        this.formGroup.get('legalCouncilId')?.enable();
        this.formGroup.get('legalCouncilId')?.setValue(field.options[0].id);
        this.formGroup.get('legalCouncilId')?.disable();
      }
    });
  }

  private initForm() {
    const formGroup: any = {};

    this.formControls.forEach((control) => {
      const validators = [];
      if (control.formControlName == '') return;
      if (control.isRequired) {
        validators.push(Validators.required);
      }
      if (control.minLength) {
        validators.push(Validators.minLength(control.minLength));
      }
      if (control.maxLength) {
        validators.push(Validators.maxLength(control.maxLength));
      }
      if (control.max) {
        validators.push(Validators.max(control.max));
      }
      if (control.min) {
        validators.push(Validators.min(control.min));
      }
      if (control.type == InputType.Number) {
        validators.push(this.integerValidator());
      }
      formGroup[control.formControlName] = [null, validators];
    });
    this.formGroup = this.formBuilder.group(formGroup);
    this.formGroup.get('legalCouncilId')?.disable();
  }
  onFileUpload(data: any) {
    this.formGroup.get(data.control.formControlName)?.setValue(data.file.id);
  }
  dropdownChanged(data: any) {
    if (
      data.control.formControlName == 'fundManagers' &&
      data.event.length == 0 &&
      !this.isLegalCouncil
    )
      this.formGroup
        .get('fundManagers')
        ?.setValue([Number(this.tokenService.getuserId())]);
  }
  getStatusClass(status: FundStatus | undefined): string {
    switch (status) {
      case FundStatus.Active:
        return 'status-green';
      case FundStatus.UnderConstruction:
        return 'status-blue';
      case FundStatus.WaitingForAddingMembers:
        return 'status-orange';
      case FundStatus.Exited:
        return 'status-grey';
      default:
        return 'status-blue';
    }
  }
  onBreadcrumbClicked(breadcrumbItem: IBreadcrumbItem) {
    this.router.navigate([breadcrumbItem.url]);
  }
  dateSelected(event: { event: any; control: IControlOption }) {
    if (event.control.formControlName == 'initiationDate') {
      let dateValues = event.event.formattedGregorian;
      let initiationDate  = this.DateConversionService.mapStringToSelectedDate(moment(dateValues).format('DD-MM-YYYY'));
        const exitDateControl = this.formControls.find(
          (control) => control.formControlName === 'exitDate'
        );

        if(exitDateControl){
          exitDateControl.minGreg = initiationDate;
          if(exitDateControl.minGreg){
            exitDateControl.minHijri =this.DateConversionService.convertGregorianToHijri(exitDateControl.minGreg);
          }
        }
    }
    this.formGroup.get(event.control.formControlName)?.setValue(event.event.formattedGregorian);
  }
  onSubmit(formValue: any) {
    this.isValidationFire = true;
    if (this.formGroup.valid && !this.isFormSubmitted) {
      this.isFormSubmitted = true;
      this.callApi();
    }
  }
  onRemoveSelectedItem(data: any) {
    const current =
      this.formGroup.get(data.control.formControlName)?.value || [];
    const updated = current.filter((id: any) => id !== data.idToRemove);
    this.formGroup.get(data.control.formControlName)?.setValue(updated);
  }

  setData(): SaveFundCommand {
    return new SaveFundCommand({
      id: this.formGroup.get('id')?.value ?? 0,
      name: this.formGroup.get('name')?.value ?? '',
      strategyId: this.formGroup.get('strategyId')?.value ?? 0,
      strategyName: this.formGroup.get('strategyName')?.value ?? '',
      status: this.formGroup.get('status')?.value ?? '',
      statusId: this.formGroup.get('statusId')?.value ?? 0,
      initiationDate: this.formGroup.get('initiationDate')?.value ?? '',
      exitDate: this.formGroup.get('exitDate')?.value ?? null,
      oldCode: this.formGroup.get('oldCode')?.value ?? 0,
      propertiesNumber:
        Number(this.formGroup.get('propertiesNumber')?.value) ?? 0,
      attachmentId: this.formGroup.get('attachmentId')?.value ?? 0,
      votingTypeId: this.formGroup.get('votingTypeId')?.value ?? 0,
      legalCouncilId: this.formGroup.get('legalCouncilId')?.value ?? 0,
      fundManagers: this.formGroup.get('fundManagers')?.value ?? [],
      fundBoardSecretaries:
        this.formGroup.get('fundBoardSecretaries')?.value ?? [],
      updatedAt: undefined,
    });
  }
  callApi() {
    this.formGroup.get('legalCouncilId')?.enable();
    this.formGroup.get('id')?.enable();
    const body: SaveFundCommand = this.setData();
    this.fundService.editFund(body).subscribe({
      next: (response: any) => {
        this.errorModalService.showSuccess(response.data);
        this.router.navigate(['/admin/investment-funds']);
        this.isFormSubmitted = false;
        this.isValidationFire = false;
      },
      error: (error: any) => {
        this.isFormSubmitted = false;
        this.isValidationFire = false;

        console.log('error', error);
        this.formGroup.get('legalCouncilId')?.disable();
        this.formGroup.get('id')?.disable();
      },
    });
  }
}
