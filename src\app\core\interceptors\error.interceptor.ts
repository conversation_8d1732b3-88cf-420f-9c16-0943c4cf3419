import { HttpInterceptorFn, HttpErrorResponse } from '@angular/common/http';
import { inject } from '@angular/core';
import { ErrorModalService } from '@core/services/error-modal.service';
import { throwError, from } from 'rxjs';
import { catchError, switchMap } from 'rxjs/operators';

export const errorInterceptor: HttpInterceptorFn = (req, next) => {
  const errorModalService = inject(ErrorModalService);

  return next(req).pipe(
    catchError((error: HttpErrorResponse) => {
      console.log('error', error);

      if (
        error.error instanceof Blob &&
        error.error.type === 'application/json'
      ) {
        // 💡 Convert Blob to JSON
        return from(error.error.text()).pipe(
          switchMap((text: string) => {
            try {
              const parsedError = JSON.parse(text);
              let message =
                parsedError.message ||
                parsedError.error_description ||
                parsedError.title ||
                parsedError.detail ||
 
                'حدث خطأ غير متوقع';
              // debugger;
 
              // Handle validation errors array
              if (
                Array.isArray(parsedError.Errors) &&
                parsedError.Errors.length > 0
              ) {
                message = parsedError.Errors.map((e: any) => {
                  const prop = e.propertyName || e.PropertyName || '';
                  const msg = e.errorMessage || e.ErrorMessage || '';
                  return `${prop}: ${msg}`;
                }).join('\n');
              }

              const enrichedError = {
                ...error,
                parsedMessage: message,
                responseBody: parsedError,
              };
              errorModalService.showError(message);

              return throwError(() => enrichedError);
            } catch (parseError) {
              console.error(
                'Failed to parse error response as JSON:',
                parseError
              );
              const message ='حدث خطأ بالنظام , لم يتمكن من عرض البيانات';
              const enrichedError = {
                ...error,
                parsedMessage: message,
                responseBody: text,
              };
              errorModalService.showError(enrichedError.parsedMessage);
              return throwError(() => enrichedError);
            }
          })
        );
      }

      // ✅ Normal JSON or string error
      let message ='حدث خطأ بالنظام , لم يتمكن من عرض البيانات';
      const responseBody = error.error;

      if (responseBody) {
        if (typeof responseBody === 'string') {
          message = responseBody;
        } else if (typeof responseBody === 'object') {
          message =
            responseBody.message ||
            responseBody.error_description ||
            responseBody.title ||
            responseBody.detail ||
            message;

          // Handle validation errors array
          if (
            Array.isArray(responseBody.Errors) &&
            responseBody.Errors.length > 0
          ) {
            // message = responseBody.Errors.map(
            //   (e: any) => e.errorMessage || e.ErrorMessage
            // ).join('\n');

            //         message = responseBody.Errors.map((e: any) => {
            //   const fieldKey = `fields.${e.PropertyName}`;
            //   const localizedName = translateService.instant(fieldKey);
            //   const errorMessage = e.ErrorMessage || e.errorMessage || '';
            //   return `${localizedName}: ${errorMessage}`;
            // }).join('\n');

            message = responseBody.Errors.map((e: any) => {
              const prop = e.propertyName || e.PropertyName || '';
              const msg = e.errorMessage || e.ErrorMessage || '';
              return `${prop} ${msg}`;
            }).join('\n');
          }
        }
      }

      const enrichedError = {
        ...error,
        parsedMessage: message,
        responseBody,
      };
      errorModalService.showError(message);

      return throwError(() => enrichedError);
    })
  );
};
