import { Routes } from '@angular/router';

export const MEETINGS_ROUTES: Routes = [
  
  {
    path: '',
    loadComponent: () =>
      import('./meetings.component').then(
        (m) => m.MeetingsComponent
      ),
  },
  {
    path: 'create',
    loadComponent: () =>
      import('./components/create-meeting/create-meeting.component').then(
        (m) => m.CreateMeetingComponent
      ),
  },
  {
    path: 'edit',
    loadComponent: () =>
      import('./components/edit-meeting/edit-meeting.component').then(
        (m) => m.EditMeetingComponent
      ),
  },
  {
    path: 'add',
    loadComponent: () =>
      import('./components/add-meeting-proposed-meeting/add-meeting-proposed-meeting.component').then(m => m.AddMeetingProposedMeetingComponent),
  },
  {
    path: 'vote',
    loadComponent: () =>
      import('./components/vote-proposed-meeting/vote-proposed-meeting.component').then(m => m.VoteProposedMeetingComponent),
  },
  {
    path: 'details',
    loadComponent: () =>
      import('./components/view-meeting-details/view-meeting-details.component').then(m => m.ViewMeetingDetailsComponent),
  },
];
