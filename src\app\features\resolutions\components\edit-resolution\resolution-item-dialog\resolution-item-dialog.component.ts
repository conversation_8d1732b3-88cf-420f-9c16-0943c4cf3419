import { Component, OnInit, Inject, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormGroup, FormBuilder, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Subject, takeUntil } from 'rxjs';

// Shared Components
import { FormBuilderComponent } from '@shared/components/form-builder/form-builder.component';

// Shared Interfaces and Enums
import { IControlOption } from '@shared/interfaces/i-control-option';
import { InputType } from '@shared/enum/input-type.enum';

// API Generated Types
import {
  BoardMembersServiceProxy,
  BoardMemberResponse,
  ResolutionItemDto,
  ResolutionItemConflictDto,
  ItemVotingResultDto
} from '@core/api/api.generated';

// Services
import { ErrorModalService } from '@core/services/error-modal.service';

export interface ResolutionItemDialogData {
  fundId: number;
  resolutionId: number;
  item?: ResolutionItemDto; // For editing existing item
  existingItems: ResolutionItemDto[];
  isEdit: boolean;
}

@Component({
  selector: 'app-resolution-item-dialog',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    TranslateModule,
    FormBuilderComponent
  ],
  templateUrl: './resolution-item-dialog.component.html',
  styleUrl: './resolution-item-dialog.component.scss'
})
export class ResolutionItemDialogComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  // Form Properties
  formGroup!: FormGroup;
  isFormSubmitted = false;
  isValidationFire = false;
  isLoading = false;
  isSubmitting = false;

  // Component Data
  fundId: number;
  resolutionId: number;
  currentItem?: ResolutionItemDto;
  existingItems: ResolutionItemDto[];
  isEdit: boolean;
  _itemVotingResult : any;
  boardMembers: BoardMemberResponse[] = [];
  selectedConflictMembers: BoardMemberResponse[] = [];

  // Form Controls Configuration
  formControls: IControlOption[] = [
    {
      type: InputType.Textarea,
      formControlName: 'description',
      id: 'description',
      name: 'description',
      // label: 'INVESTMENT_FUNDS.RESOLUTIONS.ITEM_DESCRIPTION',
      placeholder: 'INVESTMENT_FUNDS.RESOLUTIONS.ITEM_DESCRIPTION',
      isRequired: false,
      showOptional: false,
      class: 'col-md-12 pt-0',
      maxLength: 200
    },
    {
      type: InputType.Checkbox,
      formControlName: 'hasConflict',
      id: 'hasConflict',
      name: 'hasConflict',
      label: '',
      isRequired: false,
      showOptional: false,
      class: 'col-md-12',
      options: [{
        name: 'INVESTMENT_FUNDS.RESOLUTIONS.CONFLICT_OF_INTEREST',
        id: 1
      }],
      onChange: (value: any) => this.onConflictChange(value)
    },
    {
      type: InputType.Dropdown,
      formControlName: 'conflictMembers',
      id: 'conflictMembers',
      name: 'conflictMembers',
      label: 'INVESTMENT_FUNDS.RESOLUTIONS.CONFLICT_MEMBERS',
      placeholder: 'INVESTMENT_FUNDS.RESOLUTIONS.SELECT_CONFLICT_MEMBERS',
      isRequired:this.formGroup?.get('hasConflict')?.value ? false: true,
      class: 'col-md-12',
      options: [],
      multiple: true,
      isVisible: () => this.formGroup?.get('hasConflict')?.value === true
    }
  ];

  constructor(
    private formBuilder: FormBuilder,
    public dialogRef: MatDialogRef<ResolutionItemDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ResolutionItemDialogData,
    private boardMembersService: BoardMembersServiceProxy,
    private translateService: TranslateService,
    private errorModalService: ErrorModalService
  ) {
    this.fundId = data.fundId;
    this.resolutionId = data.resolutionId;
    this.currentItem = data.item;
    this.existingItems = data.existingItems || [];
    this.isEdit = data.isEdit;

    this.initForm();
  }

  ngOnInit(): void {
    this.loadBoardMembers();
    // setupForm() will be called after board members are loaded
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initForm(): void {
    this.formGroup = this.formBuilder.group({
      description: [''],
      hasConflict: [false],
      conflictMembers: [[]]
    });
  }

  private setupForm(): void {
    if (this.isEdit && this.currentItem) {
      // Edit mode - populate with existing data
      const conflictMemberIds = this.currentItem.conflictMembers?.map(cm => cm.boardMemberId) || [];

      // Debug: Setting up form for edit mode with conflict members

      this.formGroup.patchValue({
        description: this.currentItem.description || '',
        hasConflict: this.currentItem.hasConflict,
        conflictMembers: conflictMemberIds
      });

      if (this.currentItem.hasConflict) {
        this.showConflictMembersField();
      }
    }
    // Note: Title is now handled by the itemTitle getter and displayed separately
  }

  private loadBoardMembers(): void {
    this.isLoading = true;

    this.boardMembersService.boardMembersList(
      this.fundId,
      0, // page number
      100, // page size - get all members
      '', // search
      'memberName asc' // order by
    ).pipe(takeUntil(this.destroy$))
    .subscribe({
      next: (response) => {
        this.isLoading = false;
        if (response.successed && response.data) {
          this.boardMembers = response.data;
          this.updateConflictMembersOptions();
          // Setup form after board members are loaded
          this.setupForm();
        }
      },
      error: (error) => {
        this.isLoading = false;
        console.error('Error loading board members:', error);
        // this.errorModalService.showError('INVESTMENT_FUNDS.MEMBERS.LOAD_ERROR');
      }
    });
  }

  private updateConflictMembersOptions(): void {
    const conflictMembersControl = this.formControls.find(control => control.formControlName === 'conflictMembers');
    if (conflictMembersControl) {
      const options = this.boardMembers.map(member => ({
        id: member.id,
        name: member.memberName || `Member ${member.userId}` // Fallback to userId if name not available
      }));

      conflictMembersControl.options = options;

      // Debug: Updated conflict members options
    }
  }

  onConflictChange(event: any): void {
    const hasConflict = event.value || false;
    if (hasConflict) {
      this.showConflictMembersField();
    } else {
      this.hideConflictMembersField();
    }
  }

  private showConflictMembersField(): void {
    // Visibility is now handled by the isVisible function in formControls
    this.formGroup.get('conflictMembers')?.setValidators([Validators.required]);
    this.formGroup.get('conflictMembers')?.updateValueAndValidity();
  }

  private hideConflictMembersField(): void {
    // Visibility is now handled by the isVisible function in formControls
    this.formGroup.get('conflictMembers')?.clearValidators();
    this.formGroup.get('conflictMembers')?.setValue([]);
    this.formGroup.get('conflictMembers')?.updateValueAndValidity();
  }

  onRemoveSelectedItem(data: any): void {
    if (data.control.formControlName === 'conflictMembers') {
      const current = this.formGroup.get('conflictMembers')?.value || [];
      const updated = current.filter((id: any) => id !== data.idToRemove);
      this.formGroup.get('conflictMembers')?.setValue(updated);
    }
  }

  onSubmit(): void {
    console.log('Form submitted');
    if (this.isSubmitting) return;
    this.isSubmitting = true;
    this.isFormSubmitted = true;
    this.isValidationFire = true;

    if (!this.isFormValid()) {
      this.isSubmitting = false;
      this.formGroup.markAllAsTouched();
      return;
    }

    const formValue = this.formGroup.value;
    const itemData = new ResolutionItemDto({
      id: this.currentItem?.id || 0,
      resolutionId: this.resolutionId,
      title: this.itemTitle,
      description: formValue.description,
      hasConflict: formValue.hasConflict,
      displayOrder: this.currentItem?.displayOrder || (this.existingItems.length + 1),
      conflictMembers: formValue.hasConflict ? this.createConflictMembers(formValue.conflictMembers) : [],
      conflictMembersCount: formValue.hasConflict ? (formValue.conflictMembers?.length || 0) : 0,
      // Add other properties as needed
      itemVotingResult: this._itemVotingResult

    });

     this.dialogRef.close(itemData);
  }

  private createConflictMembers(memberIds: number[]): ResolutionItemConflictDto[] {
    return memberIds.map(memberId => {
      const member = this.boardMembers.find(m => m.id === memberId);
      return new ResolutionItemConflictDto({
        id: 0, // New conflict record
        resolutionItemId: this.currentItem?.id || 0,
        boardMemberId: memberId,
        conflictNotes: undefined,
        boardMemberName: member?.memberName,
        userName: member?.memberName, // Assuming memberName contains the user name
        memberType: member?.memberTypeDisplay
      }) ;
    });
  }

  private isFormValid(): boolean {

    // Basic form validation
    if (this.formGroup.get('hasConflict')?.value &&
        (!this.formGroup.get('conflictMembers')?.value ||
         this.formGroup.get('conflictMembers')?.value.length === 0)) {
      return false;
    }
    return true;
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  dropdownChanged(_event: any): void {
    // Handle dropdown changes if needed
  }

  // Template helper methods
  get itemTitle(): string {
    if (this.isEdit && this.currentItem) {
      return this.currentItem.title || '';
    } else {
      const nextItemNumber = this.existingItems.length + 1;
      return this.translateService.instant('INVESTMENT_FUNDS.RESOLUTIONS.ITEM') + ' ' + nextItemNumber;
    }
  }

  getBoardMemberName(memberId: number): string {
    const member = this.boardMembers.find(m => m.id === memberId);
    return member?.memberName || `Member ${memberId}`;
  }

  getBoardMemberType(memberId: number): string {
    const member = this.boardMembers.find(m => m.id === memberId);
    return member?.memberTypeDisplay || member?.roleDisplay || 'Member';
  }
}
