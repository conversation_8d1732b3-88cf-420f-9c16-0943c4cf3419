<div class="create-assessment-page">
  <!-- Breadcrumb Section -->
  <div class="breadcrumb-section">
    <app-breadcrumb
      (onClickEvent)="onBreadcrumbClicked($event)"
      [breadcrumbs]="breadcrumbItems"
      [size]="breadcrumbSizeEnum.Medium"
      divider=">">
    </app-breadcrumb>
  </div>

  <!-- Page Header -->
  <div class="page-header-section">
    <app-page-header [title]="title | translate">
    </app-page-header>
  </div>

  <!-- Main Content -->
  <div class="main-content">
    <div class="create-form-container">

      <!-- Form Container using reusable form-builder -->
      <div class="form-section">
        <!-- Form Builder Component -->
        <app-form-builder
          [formControls]="formControls"
          [formGroup]="formGroup"
          [isFormSubmitted]="isValidationFire"
          (fileUploaded)="onFileUpload($event)"
          (dateSelected)="dateSelected($event)">
          <p slot="top" class="header mt-2">{{ 'ASSESSMENTS.BASIC_INFO' | translate }}</p>
        </app-form-builder>
      </div>

      <!-- Assessment Questions Section - Following resolution items pattern -->
      <div class="form-section mb-3" *ngIf="isQuestionnaireType()">
        <div class="section-header">
          <div class="header-content">
            <div class="d-flex align-items-center gap-2">
              <h6>{{ 'ASSESSMENTS.ASSESSMENT_QUESTIONS' | translate }}</h6>
              <span class="items-num" *ngIf="questions.length">{{questions.length}} {{ 'ASSESSMENTS.QUESTIONS' | translate }}</span>
            </div>
            <button
              type="button"
              class="btn add-item-btn"
              (click)="addAssessmentQuestion()">
              <svg width="17" height="18" viewBox="0 0 17 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M8.5 1.5V16.5M1 9H16" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              {{ 'ASSESSMENTS.ADD_QUESTION' | translate }}
            </button>
          </div>
        </div>

        <!-- Questions List -->
        <div class="items-container assessment-questions" *ngIf="questions.length > 0">
          <div class="assessment-question-card" *ngFor="let question of questions; let i = index">
            <div class="item-header">
              <div class="item-info">
                <div class="item-number">{{ 'ASSESSMENTS.QUESTION' | translate }} {{ i + 1 }}</div>
                <div class="item-actions">
                  <button type="button"
                          class="btn edit-btn"
                          (click)="editAssessmentQuestion(question, i)">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M11.334 2.00004C11.5091 1.82494 11.7169 1.68605 11.9457 1.59129C12.1745 1.49653 12.4197 1.44775 12.6673 1.44775C12.9149 1.44775 13.1601 1.49653 13.3889 1.59129C13.6177 1.68605 13.8255 1.82494 14.0007 2.00004C14.1758 2.17513 14.3147 2.383 14.4094 2.61178C14.5042 2.84055 14.553 3.08575 14.553 3.33337C14.553 3.58099 14.5042 3.82619 14.4094 4.05497C14.3147 4.28374 14.1758 4.49161 14.0007 4.66671L5.00065 13.6667L1.33398 14.6667L2.33398 11L11.334 2.00004Z" stroke="currentColor" stroke-width="1.33333" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                  </button>
                  <button type="button"
                          class="btn remove-btn"
                          (click)="removeQuestion(i)">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M2 4H14M12.6667 4V13.3333C12.6667 13.687 12.5262 14.0261 12.2761 14.2761C12.0261 14.5262 11.687 14.6667 11.3333 14.6667H4.66667C4.31304 14.6667 3.97391 14.5262 3.72386 14.2761C3.47381 14.0261 3.33333 13.687 3.33333 13.3333V4M5.33333 4V2.66667C5.33333 2.31304 5.47381 1.97391 5.72386 1.72386C5.97391 1.47381 6.31304 1.33333 6.66667 1.33333H9.33333C9.687 1.33333 10.0261 1.47381 10.2761 1.72386C10.5262 1.97391 10.6667 2.31304 10.6667 2.66667V4" stroke="currentColor" stroke-width="1.33333" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                  </button>
                </div>
              </div>
            </div>

            <div class="item-body">
              <div class="item-description" *ngIf="question.questionText">{{ question.questionText }}</div>
              <div class="question-type-info" *ngIf="question.type">
                <span class="question-type-badge">
                  {{ (question.type === 1 ? 'ASSESSMENTS.SINGLE_CHOICE' :
                      question.type === 2 ? 'ASSESSMENTS.MULTI_CHOICE' :
                      'ASSESSMENTS.TEXT_ANSWER') | translate }}
                </span>
                <div class="question-options" *ngIf="(question.type === 1 || question.type === 2) && question.options">
                  <span class="options-count">{{ question.options.length }} {{ 'ASSESSMENTS.OPTIONS' | translate }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Empty State -->
        <div class="empty-items d-flex align-items-center justify-content-center" *ngIf="questions.length === 0">
          <button type="button" class="btn add-item-btn" (click)="addAssessmentQuestion()">
            <svg width="17" height="18" viewBox="0 0 17 18" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M8.5 1.5V16.5M1 9H16" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            {{ 'ASSESSMENTS.ADD_FIRST_QUESTION' | translate }}
          </button>
        </div>
      </div>

      <!-- Form Actions - Following edit-resolution pattern -->
      <div class="form-actions">
        <div class="actions-container d-flex gap-3 align-items-center justify-content-end">
          <!-- Cancel Button -->
          <app-custom-button
            [btnName]="'COMMON.CANCEL' | translate"
            (click)="onCancel()"
            [buttonType]="buttonEnum.Secondary"
            [iconName]="IconEnum.cancel">
          </app-custom-button>

          <!-- Save as Draft Button -->
          <app-custom-button
            [btnName]="'ASSESSMENTS.SAVE_AS_DRAFT' | translate"
            [buttonType]="buttonEnum.Secondary"
            [iconName]="IconEnum.draft"
            [disabled]="isApiCallInProgress"
            (click)="onSaveAsDraft()">
          </app-custom-button>

          <!-- Submit for Approval Button -->
          <app-custom-button
            [btnName]="isApiCallInProgress ? ('COMMON.SUBMITTING' | translate) : ('ASSESSMENTS.SUBMIT_FOR_APPROVAL' | translate)"
            [buttonType]="buttonEnum.Primary"
            [iconName]="IconEnum.verify"
            [disabled]="isApiCallInProgress"
            (click)="onSubmit()">
          </app-custom-button>
        </div>
      </div>
    </div>
  </div>
</div>
