import { Component, OnInit } from '@angular/core';
import { CommonModule, Location } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDividerModule } from '@angular/material/divider';
import { MatChipsModule } from '@angular/material/chips';

// Core imports
import { IBreadcrumbItem } from '@core/gl-interfaces/IBreadcrumbItem/ibreadcrumb-item';
import { SizeEnum } from '@shared/enum/size-enum';
import { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';
import { CustomButtonComponent } from '@shared/components/custom-button/custom-button.component';
import { ButtonTypeEnum, IconEnum } from '@core/enums/icon-enum';

// API imports
import {
  ResolutionsServiceProxy,
  SingleResolutionResponse,
  ResolutionStatusEnum,
  RejectResolutionCommand,
  ResolutionItemDto,
  ResolutionMemberVoteServiceProxy,
  EditResolutionMemberVoteCommand,
  ResolutionItemVoteCommentDto,
  ItemCommentCommand,
  MemberVoteCommentCommand
} from '@core/api/api.generated';
import { TokenService } from '../../../auth/services/token.service';
import Swal from 'sweetalert2';
import { ConflictsPopupComponent } from '../conflicts-popup/conflicts-popup.component';
import { MatDialog } from '@angular/material/dialog';
import { AttachmentCardComponent } from "../attachment-card/attachment-card.component";
import { TimelineComponent } from "../timeline/timeline.component";
import { ResolutionStatus } from '@shared/enum/resolution-status';
import { ResolutionService } from '@core/services/resolution.service';
import { DateHijriConverterPipe } from '@shared/pipes/dateHijriConverter/dateHijriConverter.pipe';
import { ErrorModalService } from '@core/services/error-modal.service';
import { ConflictMembersDialogData } from '../edit-resolution/conflict-members-dialog/conflict-members-dialog.component';
import { MemberNoteComponent } from "../member-note/member-note.component";
import { SingleNoteDialogComponent } from '../single-note-dialog/single-note-dialog.component';
import { VotingResult } from '@shared/enum/votingResult';
import { MultipleNoteDialogComponent, MultipleNoteDialogData } from '../multiple-note-dialog/multiple-note-dialog.component';
import { MemberVotingResult } from '@core/enums/voting/member-voting-result';

@Component({
  selector: 'app-view-resolution-voting',
  standalone: true,
imports: [
    CommonModule,
    TranslateModule,
    MatButtonModule,
    MatCardModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatDividerModule,
    MatChipsModule,
    BreadcrumbComponent,
    CustomButtonComponent,
    AttachmentCardComponent,
    TimelineComponent,
    DateHijriConverterPipe,
    MemberNoteComponent,
  ],
  providers: [ResolutionMemberVoteServiceProxy],
  templateUrl: './view-resolution-voting.component.html',
  styleUrl: './view-resolution-voting.component.scss'
})
export class ViewResolutionVotingComponent {
  // Data properties
  resolution: any | null = null;
  resolutionId: number = 0;
  memberId: number = 0;
  fundId: number = 0;
  fundName: string = '';

  // Loading and error states
  isLoading = false;
  hasError = false;
  errorMessage = '';

  // Role-based access control
  userRole: string = '';
  canConfirmReject = false;
  canSendToVote = false;
  canViewDetails = false;

  // UI state
  breadcrumbSizeEnum = SizeEnum;
  buttonTypeEnum = ButtonTypeEnum;
  iconEnum = IconEnum;

  // Breadcrumb configuration
  breadcrumbItems: IBreadcrumbItem[] = [];
  isExpanded: boolean= true;
  isExpandedItem: boolean= true;
  isExpandedAction: boolean= true;
  resolutionStatus: any | null = null;
  buttonEnum = ButtonTypeEnum;
  IconEnum = IconEnum;
  resolutionStatusEnum=ResolutionStatus;
  isExpandedNotes: boolean = true;
  isActiveReject: boolean = false;
  isActiveApprove: boolean = false;
  votingResult = VotingResult;
  voteComments: { id: number; comment: string }[] = [];
  itemComments: { id: number; comment: string }[] = [];
  memberVotingResultEnum = MemberVotingResult;


  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private resolutionsProxy: ResolutionsServiceProxy,
    public tokenService: TokenService,
    private translateService: TranslateService,
    private dialog: MatDialog,
    private resolutionService: ResolutionService,
    private errorModalService: ErrorModalService,
    private resolutionMemberVoteServiceProxy: ResolutionMemberVoteServiceProxy,
    private location: Location
  ) {}

  ngOnInit(): void {
    this.memberId = Number(this.route.snapshot.queryParamMap.get('memberId')) || 0;
    console.log(this.memberId)
    this.initializeComponent();
  }

  goBack() {
    this.location.back();
  }

  private initializeComponent(): void {
    // Get route parameters
    this.route.params.subscribe(params => {
      this.resolutionId = +params['id'];

      // Get fundId from query params or route params
      this.route.queryParams.subscribe(queryParams => {
        this.fundId = +queryParams['fundId'] || +params['fundId'] || 0;

        // Always setup breadcrumbs, even if fundId is invalid
        this.setupBreadcrumbs();

        if (this.resolutionId && this.fundId) {
          this.initializeRoleBasedAccess();
          this.loadResolutionDetails();
        } else {
          // Still initialize role-based access for basic functionality
          this.initializeRoleBasedAccess();
          this.handleError('RESOLUTIONS.INVALID_PARAMETERS');
        }
      });
    });
  }

  private initializeRoleBasedAccess(): void {
    // Determine user role based on permissions
    if (this.tokenService.hasRole('fundmanager')) {
      this.userRole = 'fundmanager';
      this.canViewDetails = true;
      this.canConfirmReject = true; // Fund manager can confirm/reject waiting for confirmation resolutions
      this.canSendToVote = false;
    } else if (this.tokenService.hasRole('legalcouncil') || this.tokenService.hasRole('boardsecretary')) {
      this.userRole = 'legalcouncil';
      this.canViewDetails = true;
      this.canConfirmReject = false; // Legal council/board secretary complete data, don't confirm/reject
      this.canSendToVote = true; // Legal council/board secretary can send confirmed resolutions to vote
    } else if (this.tokenService.hasRole('boardmember')) {
      this.userRole = 'boardmember';
      this.canViewDetails = true;
      this.canConfirmReject = false;
      this.canSendToVote = false;
    } else {
      this.userRole = 'Default';
      this.canViewDetails = false;
      this.canConfirmReject = false;
      this.canSendToVote = false;
    }
  }

  private setupBreadcrumbs(): void {
    if (this.fundId && this.fundId > 0) {
      this.updateBreadcrumb();
    } else {
      this.updateBreadcrumbWithFallback();
    }
  }

  private updateBreadcrumb(): void {
    this.breadcrumbItems = [
      // { label: 'BREADCRUMB.HOME', url: '/admin/dashboard' },
      { label: 'sidebar.funds', url: '/admin/investment-funds' },
      {
        label: 'INVESTMENT_FUNDS.FORM.FUND_NAME',
        url: `/admin/investment-funds/fund-details?id=${this.fundId}`
      },
      {
        label: 'RESOLUTIONS.TITLE',
        url: `/admin/investment-funds/resolutions?fundId=${this.fundId}`
      },
      { label: 'INVESTMENT_FUNDS.RESOLUTIONS.RESOLUTION_DETAILS', url: '', disabled: true }
    ];
  }

  private updateBreadcrumbWithFallback(): void {
    // Fallback breadcrumb when fundId is not available
    this.breadcrumbItems = [
      { label: 'BREADCRUMB.HOME', url: '/admin/dashboard' },
      { label: 'BREADCRUMB.FUNDS', url: '/admin/investment-funds' },
      {
        label: 'BREADCRUMB.FUND_DETAILS',
        url: '/admin/investment-funds',
        disabled: true
      },
      {
        label: 'RESOLUTIONS.TITLE',
        url: '/admin/investment-funds',
        disabled: true
      },
      { label: 'INVESTMENT_FUNDS.RESOLUTIONS.RESOLUTION_DETAILS', url: '', disabled: true }
    ];
  }

   getResolutionStatusById(): void {
    this.isLoading = true;
    this.hasError = false;

    this.resolutionService.GetResolutionStatusById(this.resolutionId).subscribe({
      next: (response) => {

        this.isLoading = false;
        if (response.successed && response.data) {
          this.resolutionStatus = response.data;
        } else {
          this.handleError('RESOLUTIONS.FAILED_TO_LOAD');
        }
      },
      error: (error) => {
        this.isLoading = false;
        console.error('Error loading resolution details:', error);
        // this.handleError('RESOLUTIONS.FAILED_TO_LOAD');
      }
    });
  }
  requestRevote(){
    Swal.fire({
      title: this.translateService.instant('RESOLUTIONS.CONFIRM'),
      text: this.translateService.instant('RESOLUTIONS.VOTING.CONFIRM_REVOTE_REQUEST'),
      imageUrl: 'assets/images/confirmation-green.svg',
      showCancelButton: true,
      customClass: {
        confirmButton: 'btn btn-primary ' + (this.translateService.currentLang == 'en' ? 'ms-3' : 'me-3'),
        cancelButton: 'btn outline-btn',
      },
      reverseButtons: true,
      buttonsStyling: false,
      confirmButtonText:`<img src="assets/images/reload.png" alt="verify" class="${this.translateService.currentLang == 'ar' ? 'ms-2' : 'me-2'}"/>` + this.translateService.instant('RESOLUTIONS.VOTING.REVOTE'),
      cancelButtonText: `<img src="assets/images/back-icon.png" alt="back" class="${this.translateService.currentLang == 'ar' ? 'ms-2' : 'me-2'}"/>` + this.translateService.instant('INVESTMENT_FUNDS.RESOLUTIONS.BACK')
    }).then((result)=> {
      if(result.isConfirmed){
        this.resolutionMemberVoteServiceProxy.requestReVote(this.resolution.resolutionMemberVote.id).subscribe((res: any) => {
          console.log(res);
          Swal.fire({
            title: this.translateService.instant('COMMON.SUCCESS'),
            text: this.translateService.instant('RESOLUTIONS.VOTING.REVOTE_REQ_SEND_SUCCESSFULLY'),
            icon: 'success',
            timer: 2000,
            showConfirmButton: false
          });
          this.loadResolutionDetails();
        });
      }
    })
  }
  loadResolutionDetails(): void {
  this.isLoading = true;
  this.hasError = false;

  this.resolutionService.getResolutionMemberVoteByResolutionId(this.resolutionId,this.memberId).subscribe({
    next: (response) => {
      this.isLoading = false;
      if (response.successed && response.data) {
        this.resolution = response.data;
        console.log(this.resolution );
      } else {
        this.handleError('RESOLUTIONS.FAILED_TO_LOAD');
      }
    },
    error: (error) => {
      this.isLoading = false;
      console.error('Error loading resolution details:', error);
      this.location.back();
    }
  });
  }

  private handleError(messageKey: string): void {
    this.hasError = true;
    this.errorMessage = this.translateService.instant(messageKey);

    Swal.fire({
      title: this.translateService.instant('COMMON.ERROR'),
      text: this.errorMessage,
      icon: 'error',
      confirmButtonText: this.translateService.instant('COMMON.OK')
    });
  }


  onBreadcrumbClicked(item: IBreadcrumbItem): void {
    console.log('Breadcrumb clicked:', item);

    if (!item) {
      console.warn('Breadcrumb item is null or undefined');
      return;
    }

    if (item.disabled) {
      console.log('Breadcrumb item is disabled, ignoring click');
      return;
    }

    if (!item.url) {
      console.warn('Breadcrumb item has no URL:', item);
      return;
    }

    console.log('Navigating to:', item.url);
    this.router.navigateByUrl(item.url).catch((error) => {
      console.error('Navigation failed:', error);
    });
  }

  onBackToList(): void {
    this.router.navigate(['/admin/investment-funds/resolutions'], {
      queryParams: { fundId: this.fundId }
    });
  }



  getStatusDisplay(): string {
    if (!this.resolution) return '';
    return this.resolution.resolutionStatus?.nameAr ||
           this.resolution.resolutionStatus?.nameEn ||
           this.translateService.instant(`RESOLUTIONS.STATUS_${this.resolution.status}`);
  }

  shouldShowConfirmRejectButtons(): boolean {
    // Show confirm/reject buttons for fund manager when resolution is waiting for confirmation
    return (
      this.canConfirmReject &&
      this.resolution?.status === ResolutionStatusEnum._4
    ); // Waiting for confirmation status
  }

  shouldShowSendToVoteButton(): boolean {
    // Show send to vote button for legal council/board secretary when resolution is confirmed
    return (
      this.canSendToVote && this.resolution?.status === ResolutionStatusEnum._5
    ); // Confirmed status
  }

  // Status-specific UI methods
  shouldShowBasicInfo(): boolean {
    // Basic info is shown for all authorized users and statuses
    return true;
  }

  shouldShowResolutionItems(): boolean {
    // Resolution items are shown for completing data, waiting for confirmation, confirmed, and rejected statuses
    if (!this.resolution) return false;

    const statusesWithItems = [
      ResolutionStatusEnum._3, // Approved/Confirmed
      ResolutionStatusEnum._4, // Rejected/Cancelled
      ResolutionStatusEnum._5, // Voting in progress (completing data equivalent)
      ResolutionStatusEnum._6  // Not approved (waiting for confirmation equivalent)
    ];

    return statusesWithItems.includes(this.resolution.status);
  }

  shouldShowResolutionHistory(): boolean {
    // History is shown for all statuses except draft
    if (!this.resolution) return false;
    return this.resolution.status !== ResolutionStatusEnum._1; // Not draft
  }

  shouldShowRejectionReason(): boolean {
    // Rejection reason is shown only for rejected status
    if (!this.resolution) return false;
    return this.resolution.status === ResolutionStatusEnum._4; // Rejected/Cancelled
  }


  formatDate(date: any): string {
    if (!date) return '';
    try {
      return new Date(date.toString()).toLocaleDateString('ar-SA');
    } catch {
      return '';
    }
  }

  // File operation methods
  onDownloadFile(): void {
    // TODO: Implement file download using FileManagementServiceProxy
    // Note: attachmentId property doesn't exist in SingleResolutionResponse
    console.log('Downloading file - feature not yet implemented');
  }

  onOpenFile(): void {
    // TODO: Implement file opening/preview
    // Note: attachmentId property doesn't exist in SingleResolutionResponse
    console.log('Opening file - feature not yet implemented');
  }

  onDownloadAttachment(item: any): void {
    // TODO: Implement attachment download
    console.log('Downloading attachment:', item);
  }

  onViewConflictMembers(item: any): void {
    const dialogRef = this.dialog.open(ConflictsPopupComponent, {
      width: '600px',
      data: item,
    });
    console.log('Viewing conflict members for item:', item);
  }

  ViewConflictMembers(item: ResolutionItemDto): void {
    if (!item.conflictMembers || item.conflictMembers.length === 0) {
      return;
    }

    const dialogData: ConflictMembersDialogData = {
      itemTitle: item.title || 'Resolution Item',
      conflictMembers: item.conflictMembers,
    };

    const dialogRef = this.dialog.open(ConflictsPopupComponent, {
      width: '500px',
      data: dialogData,
      disableClose: false,
    });

    dialogRef.afterClosed().subscribe(() => {
      // Dialog closed, no action needed
    });
  }

  getResolutionHistory(): any[] {
    // TODO: Implement resolution history API call
    // For now, return empty array as placeholder
    // This should be replaced with actual API call to get resolution history
    return [];
  }

  toggleExpand() {
    this.isExpanded = !this.isExpanded;
  }
  toggleExpandItems() {
    this.isExpandedItem = !this.isExpandedItem;
  }

  toggleExpandActions() {
    this.isExpandedAction = !this.isExpandedAction;
  }

  toggleExpandNotes() {
    this.isExpandedNotes = !this.isExpandedNotes;
  }

  getStatusVoteClass(statusId: number) {
    switch(statusId){
      case this.memberVotingResultEnum.Accept:
        return 'status-green';
      case this.memberVotingResultEnum.Reject:
        return 'status-red';
      case this.memberVotingResultEnum.NotEligibleToVote:
        return 'status-grey';
      case this.memberVotingResultEnum.NotVotedYet:
        return 'status-orange';
      // case this.memberVotingResultEnum.NotVoted:
      //   return 'not-voted';
      default:
        return '';
    }
  }

  // getItemStatusVoteClass(statusId: number) {
  //   switch (statusId) {
  //     case 2:
  //       return 'approved';
  //     case 3:
  //       return 'rejected';
  //     default:
  //       return '';
  //   }
  // }

  getStatusClass(statusId: number): string {
    switch (statusId) {
      case this.resolutionStatusEnum.Draft:
        return 'draft';
      case this.resolutionStatusEnum.Pending:
        return 'pending';
      case this.resolutionStatusEnum.CompletingData:
        return 'completing-data';
      case this.resolutionStatusEnum.WaitingForConfirmation:
        return 'waiting-for-confirmation';
      case this.resolutionStatusEnum.Confirmed:
        return 'confirmed';
      case this.resolutionStatusEnum.Rejected:
        return 'rejected';
      case this.resolutionStatusEnum.VotingInProgress:
        return 'voting-inProgress';
      case this.resolutionStatusEnum.Approved:
        return 'approved';
      case this.resolutionStatusEnum.NotApproved:
        return 'not-approved';
      case this.resolutionStatusEnum.Cancelled:
        return 'cancelled';
      default:
        return '';
    }
  }

  approveRejectReVote(type: any) {
    Swal.fire({
      title: this.translateService.instant('RESOLUTIONS.CONFIRM'),
      text: type ?
      this.translateService.instant('RESOLUTIONS.VOTING.CONFIRM_ACCEPT_REVOTE_REQUEST', {memberName: this.resolution.resolutionMemberVote.boardMemberName, resolutionId: this.resolution.code}) :
      this.translateService.instant('RESOLUTIONS.VOTING.CONFIRM_REJECT_REVOTE_REQUEST', {memberName: this.resolution.resolutionMemberVote.boardMemberName, resolutionId: this.resolution.code}),
      imageUrl: 'assets/images/confirmation-green.svg',
      input: type ? undefined : 'textarea',
      inputPlaceholder: type ? undefined : this.translateService.instant('RESOLUTIONS.VOTING.ENTER_REJECT_RESOAN'),
      showCancelButton: true,
      customClass: {
        confirmButton: 'btn btn-primary ' + (this.translateService.currentLang == 'en' ? 'ms-3' : 'me-3'),
        cancelButton: 'btn outline-btn',
      },
      reverseButtons: true,
      buttonsStyling: false,
      confirmButtonText:`<img src="assets/images/approve-white.png" alt="verify" class="${this.translateService.currentLang == 'ar' ? 'ms-2' : 'me-2'}"/>` + this.translateService.instant('RESOLUTIONS.CONFIRM'),
      cancelButtonText: `<img src="assets/images/back-icon.png" alt="back" class="${this.translateService.currentLang == 'ar' ? 'ms-2' : 'me-2'}"/>` + this.translateService.instant('INVESTMENT_FUNDS.RESOLUTIONS.BACK')
    }).then((result) => {
      if (result.isConfirmed) {
        // if(result.value){}
        this.resolutionMemberVoteServiceProxy.approveRejectReVote(this.resolution.resolutionMemberVote.id, type).subscribe((res: any) => {
          console.log(res);
          Swal.fire({
            title: this.translateService.instant('COMMON.SUCCESS'),
            text: type ? this.translateService.instant('RESOLUTIONS.VOTING.REVOTE_REQ_ACCEPTED', {resolutionId: this.resolution.code}) : this.translateService.instant('RESOLUTIONS.VOTING.REVOTE_REQ_REJECTED', {resolutionId: this.resolution.code}),
            icon: 'success',
            timer: 2000,
            showConfirmButton: false
          });
          this.loadResolutionDetails();
        });
      }
    })
    }

  editResolution(resolution: any): void {
    // Navigate to edit page
    this.router.navigate(['/admin/investment-funds/resolutions/edit'], {
      queryParams: { id: resolution.id, fundId: this.fundId },
    });
  }

  // cancel() {
  //   this.router.navigate(['/admin/investment-funds/resolutions'], {
  //     queryParams: { fundId: this.fundId },
  //   });
  // }

  addNote(isResolution: boolean,item?:any) {
    debugger;
    this.dialog.closeAll();

    const dialogRef = this.dialog.open(SingleNoteDialogComponent, {
      width: '700px',
      data: { isResolution },
    });

    dialogRef
      .afterClosed()
      .subscribe((result: { id: number; comment: string ;createdAt:any , createdByName:string ,userRoleOrBoardMemberType:string}) => {
        if (result) {
          debugger;
          if(isResolution){
         result.createdByName = this.resolution?.userFullName;
         result.userRoleOrBoardMemberType = this.resolution?.userRoleOrBoardMemberType;
         this.resolution?.resolutionMemberVote?.voteComments.unshift(result);
           const obj = new MemberVoteCommentCommand();
            obj.comment = result.comment;
            obj.resolutionMemberVoteId = this.resolution?.resolutionMemberVote?.id;

          this.resolutionMemberVoteServiceProxy.addMemberVoteComment(obj).subscribe({
              next: (response) => {

                this.isLoading = false;
                if (response.successed) {
                  this.errorModalService.showSuccess(
                    'INVESTMENT_FUNDS.VOTING.REPLY_SUBMITTED_SUCCESSFULLY'
                  );
                } else {
                  this.handleError('RESOLUTIONS.REJECT_FAILED');
                }
              },
              error: (error) => {
                this.isLoading = false;
                console.error('Error confirming resolution:', error);
              },
            });
          }
        else{
            const obj = new ItemCommentCommand();
            obj.comment = result.comment;
            obj.resolutionItemId = item.resolutionItemId;
            obj.memberId = this.memberId || 0;
            this.resolutionMemberVoteServiceProxy.addItemVoteComment(obj).subscribe({
                next: (response) => {
                  this.isLoading = false;
                  if (response.successed) {
                    this.errorModalService.showSuccess(
                      'INVESTMENT_FUNDS.VOTING.REPLY_SUBMITTED_SUCCESSFULLY'
                    );
                  } else {
                    this.handleError('RESOLUTIONS.REJECT_FAILED');
                  }
                },
                error: (error) => {
                  this.isLoading = false;
                  console.error('Error confirming resolution:', error);
                },
              });
        }
      }
      });
  }

  openNotes(item: any){
   this.dialog.closeAll()
      if (!item || item.length === 0) {
        return;
      }

      const dialogData: MultipleNoteDialogData = {
         comments: item,
      };

      const dialogRef = this.dialog.open(MultipleNoteDialogComponent, {
         width: '700px',
        data: dialogData,
        disableClose: false,
      });
      dialogRef.afterClosed().subscribe((result: { id: number; comment: string ,createdByName :string ,userRoleOrBoardMemberType: string}) => {
        if (result) {
        result.createdByName = this.resolution?.userFullName;
        result.userRoleOrBoardMemberType = this.resolution?.userRoleOrBoardMemberType;
        item.itemComments = item.itemComments || [];
        item.itemComments.unshift(result);
        const obj = new ItemCommentCommand();
        obj.comment = result.comment;
        obj.resolutionItemId = item.resolutionItemId;
        obj.memberId = this.memberId || 0;
        this.resolutionMemberVoteServiceProxy.addItemVoteComment(obj).subscribe({
                next: (response) => {
                  this.isLoading = false;
                  if (response.successed) {
                    this.errorModalService.showSuccess(
                      'INVESTMENT_FUNDS.VOTING.REPLY_SUBMITTED_SUCCESSFULLY'
                    );
                  } else {
                    this.handleError('RESOLUTIONS.REJECT_FAILED');
                  }
                },
                error: (error) => {
                  this.isLoading = false;
                  console.error('Error confirming resolution:', error);
                },
              });
  }
  });
  }

  toggleRejectButton(item: any): void {
    item.voteResult =
      item.voteResult === this.votingResult.Reject
        ? null
        : this.votingResult.Reject;
  }

  toggleApproveButton(item: any): void {
    item.voteResult =
      item.voteResult === this.votingResult.Accept
        ? null
        : this.votingResult.Accept;
  }

   getNotificationTime(createdAt: Date): string {
    const now = new Date();
    const notificationDate = new Date(createdAt);
    const diffInMinutes = Math.floor((now.getTime() - notificationDate.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) {
      return this.translateService.instant('INVESTMENT_FUNDS.VOTING.NOW');
    } else if (diffInMinutes < 60) {
      return this.translateService.instant('INVESTMENT_FUNDS.VOTING.SINCE')+' '+`${diffInMinutes}`+this.translateService.instant('INVESTMENT_FUNDS.VOTING.MINUTE');
    } else if (diffInMinutes < 1440) {
      const hours = Math.floor(diffInMinutes / 60);
      return this.translateService.instant('INVESTMENT_FUNDS.VOTING.SINCE')+' '+` ${hours}`+this.translateService.instant('INVESTMENT_FUNDS.VOTING.HOUR');
    } else {
      const days = Math.floor(diffInMinutes / 1440);
      return this.translateService.instant('INVESTMENT_FUNDS.VOTING.SINCE')+' '+` ${days}`+this.translateService.instant('INVESTMENT_FUNDS.VOTING.DAY');
    }
  }
}
