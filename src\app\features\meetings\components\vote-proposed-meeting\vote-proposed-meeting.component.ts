import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { SizeEnum } from '@core/enums/size';
import { IBreadcrumbItem } from '@core/gl-interfaces/IBreadcrumbItem/ibreadcrumb-item';
import { TranslateModule } from '@ngx-translate/core';
import { BreadcrumbComponent } from "@shared/components/breadcrumb/breadcrumb.component";
import { CustomButtonComponent } from '@shared/components/custom-button/custom-button.component';
import { MatCheckboxModule } from "@angular/material/checkbox";
import { FormsModule } from '@angular/forms';
import { ButtonTypeEnum, IconEnum } from '@core/enums/icon-enum';

@Component({
  selector: 'app-vote-proposed-meeting',
  standalone: true,
  imports: [BreadcrumbComponent,
    CustomButtonComponent,
    TranslateModule, MatCheckboxModule,FormsModule],
  templateUrl: './vote-proposed-meeting.component.html',
  styleUrl: './vote-proposed-meeting.component.scss'
})
export class VoteProposedMeetingComponent {

  breadcrumbSizeEnum = SizeEnum;
  breadcrumbItems: IBreadcrumbItem[] = [];
  selectedOption1:any;
  selectedOption2:any;
  selectedOption3:any;
  selectedOption4:any;
    buttonEnum = ButtonTypeEnum;
    IconEnum = IconEnum;

constructor(private router:Router){}

ngOnInit(): void {
    this.initializeBreadcrumbs();
}

    onBreadcrumbClicked(event: IBreadcrumbItem): void {
      if (!event.disabled && event.url) {
        this.router.navigateByUrl(event.url);
      }
    }

    onVoteChange(data:any){

    }
  confirmVote(){}

   private initializeBreadcrumbs(): void {
    this.breadcrumbItems = [
      { label: 'BREADCRUMB.HOME', url: '/admin/dashboard' },
      { label: 'BREADCRUMB.FUNDS', url: '/admin/investment-funds' },
      // {
      //   label: 'BREADCRUMB.FUND_DETAILS',
      //   url: `/admin/investment-funds/fund-details?id=${this.fundId}`,
      // },
      {
        label: '...',
        url: '', disabled: true
      },
      { label: 'INVESTMENT_FUNDS.MEETING.VOTE_MEETING_TITLE', url: '', disabled: true },
    ];
  }
}
