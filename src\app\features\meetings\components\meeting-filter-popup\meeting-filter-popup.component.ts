import { CommonModule } from '@angular/common';
import { Component, Inject } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { ButtonTypeEnum, IconEnum } from '@core/enums/icon-enum';
import { TranslateModule } from '@ngx-translate/core';
import { CustomButtonComponent } from '@shared/components/custom-button/custom-button.component';
import { FormBuilderComponent } from '@shared/components/form-builder/form-builder.component';
import { InputType } from '@shared/enum/input-type.enum';
import { IControlOption } from '@shared/interfaces/i-control-option';

@Component({
  selector: 'app-meeting-filter-popup',
  standalone: true,
  imports: [
    CommonModule,
    CustomButtonComponent,
    FormBuilderComponent,
    TranslateModule
  ],
  templateUrl: './meeting-filter-popup.component.html',
  styleUrl: './meeting-filter-popup.component.scss'
})
export class MeetingFilterPopupComponent {
  IconEnum = IconEnum;
  buttonEnum = ButtonTypeEnum;

  formControls: IControlOption[] = [
    {
      type: InputType.Dropdown,
      formControlName: 'statusId',
      id: 'statusId',
      name: 'statusId',
      label: 'MEETINGS.MEETING_STATUS',
      placeholder: 'MEETINGS.MEETING_STATUS_PLACEHOLDER',
      isRequired: false,
      showOptional: false,
      class: 'col-md-12',
      options: [],
    },
  ];
  
    formGroup!: FormGroup;
    isFormSubmitted!: boolean;
  
    constructor(
      public dialogRef: MatDialogRef<MeetingFilterPopupComponent>,
      @Inject(MAT_DIALOG_DATA) public data: any,
      private formBuilder: FormBuilder,
    ) {
      // this.getStrategyList();
      this.initForm();
    }
    // getStrategyList() {
    //   this.strategyService.strategyList(0, 0, '', '').subscribe((res: any) => {
    //     const field = this.formControls.find(
    //       (f) => f.formControlName === 'strategyId'
    //     );
    //     if (field) {
    //       field.options = res.data.map((strategy: any) => ({
    //         id: strategy.id,
    //         name: strategy.localizedName ,
    //       }));
    //     }
    //   });
    // }
    applyFilters() {
      this.isFormSubmitted = true;
      if (this.formGroup.valid) this.dialogRef.close(this.formGroup.value);
    }
    resetFilters() {
      this.formGroup.reset();
      this.applyFilters();
    }
    initForm() {
      const formGroup: any = {};
      this.formControls.forEach((control) => {
        const validators = [];
        if (control.formControlName == '') return;
        if (control.max) {
          validators.push(Validators.max(control.max));
        }
        if (control.min) {
          validators.push(Validators.min(control.min));
        }
        formGroup[control.formControlName] = [null, validators];
      });
  
      this.formGroup = this.formBuilder.group(formGroup);
      if (this.data) this.formGroup.setValue(this.data);
    }
  
    closeDialog() {
      this.dialogRef.close();
    }
    // dateSelected(event: { event: any; control: IControlOption }) {
    //   const controlName = event.control.formControlName;
    //   const selectedDate: NgbDateStruct = event.event?.gregorian;
  
    //   if (!selectedDate) return;
  
    //   const otherControlName =
    //     controlName === 'creationDateFrom'
    //       ? 'creationDateTo'
    //       : 'creationDateFrom';
    //   const otherDate = this.formGroup.get(otherControlName)
    //     ?.value as NgbDateStruct;
  
    //   const selected = new Date(
    //     selectedDate.year,
    //     selectedDate.month - 1,
    //     selectedDate.day
    //   );
    //   let compareToValid = true;
  
    //   if (otherDate) {
    //     const other = new Date(
    //       otherDate.year,
    //       otherDate.month - 1,
    //       otherDate.day
    //     );
    //     compareToValid =
    //       controlName === 'creationDateFrom'
    //         ? selected <= other
    //         : selected >= other;
    //   }
  
    //   if (compareToValid) {
    //     this.formGroup.get(controlName)?.setValue(selectedDate);
    //   } else {
    //     this.formGroup.get(controlName)?.setErrors({ dateRangeInvalid: true });
    //   }
    // }
    // setDateMaxAndMin() {
    //   const today = new Date();
    //   const minGreg: NgbDateStruct = { year: 2010, month: 1, day: 1 };
    //   const minHijri: NgbDateStruct =
    //     this.DateConversionService.convertGregorianToHijri(minGreg);
  
    //   this.maxGreg = {
    //     year: today.getFullYear(),
    //     month: today.getMonth() + 1,
    //     day: today.getDate(),
    //   };
  
    //   this.maxHijri = this.DateConversionService.convertGregorianToHijri(
    //     this.maxGreg
    //   );
    //   const field = this.formControls.find(
    //     (f) => f.formControlName === 'creationDateFrom'
    //   );
    //   if (field) {
    //     field.maxGreg = this.maxGreg;
    //     field.maxHijri = this.maxHijri;
    //     field.minHijri = minHijri;
    //     field.minGreg = minGreg;
    //   }
    // }
}
