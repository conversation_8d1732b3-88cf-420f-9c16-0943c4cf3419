import { CategoryDialogComponent } from './category/category-dialog.component';
import { DocumentCategoriesServiceProxy } from './../../core/api/api.generated';
import { Component, OnInit } from '@angular/core';
import { ColumnTypeEnum, DataHandlingType } from '@core/enums/column-type';
import {
  ActionDisplayMode,
  ITableColumn,
  SwitchToggleEvent,
  TableActionEvent,
  TextLinkClickEvent,
} from '@core/gl-interfaces/I-table/i-table';
import { TableComponent } from '../../shared/components/table/table.component';
import { MatTableDataSource } from '@angular/material/table';
import { SelectionModel } from '@angular/cdk/collections';
import { PageHeaderComponent } from '../../shared/components/page-header/page-header.component';
import { MatDialog } from '@angular/material/dialog';
import Swal from 'sweetalert2';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { DatePipe } from '@angular/common';
import { CommonModule } from '@angular/common';
import { BreadcrumbComponent } from "../../shared/components/breadcrumb/breadcrumb.component";
import { IBreadcrumbItem } from '@core/gl-interfaces/IBreadcrumbItem/ibreadcrumb-item';
import { SizeEnum } from '@shared/enum/size-enum';
import { TokenService } from '../auth/services/token.service';

export interface PeriodicElement {
  id:number;
  nameAr: string;
  nameEn: string;
  displayOrder: number ;
}

@Component({
  selector: 'app-document-categories',
  standalone: true,
  imports: [CommonModule, TableComponent, PageHeaderComponent, TranslateModule, BreadcrumbComponent],
  templateUrl: './document-categories.component.html',
  styleUrl: './document-categories.component.scss',
  providers: [DatePipe]

})
export class DocumentCategoriesComponent implements OnInit {
  ELEMENT_DATA: PeriodicElement[] = [];
  isDialogOpen = false;

  selection = new SelectionModel<any>(true, []);
  sortingType: DataHandlingType = DataHandlingType.Frontend;
  paginationType: DataHandlingType = DataHandlingType.Frontend;
  tableColumns: ITableColumn[] = [];
  displayedColumns = this.tableColumns.map((c) => c.columnDef);

  tableDataSource = new MatTableDataSource<PeriodicElement>(this.ELEMENT_DATA);

  totalCount: any;
  breadcrumbSizeEnum = SizeEnum;
  breadcrumbItems: IBreadcrumbItem[] = [
    {
      label: 'INVESTMENT_FUNDS.TITLE',
      url: '/admin/document-categories',
      icon: 'fas fa-home',
    },
    {
      label: 'INVESTMENT_FUNDS.CREATE_NEW_FUND',
       url: '/admin/document-categories',
      disabled: true,
    },
  ];

  constructor(
    private dialog: MatDialog,
    private documnetCateogryService: DocumentCategoriesServiceProxy,
    private translateService: TranslateService,
    private datePipe: DatePipe,
    public tokenService:TokenService
  ) {}

  ngOnInit() {
    this.initializeTableColumns();
    this.getList(0,0,'','Id desc');
  }

  private initializeTableColumns(): void {

    this.tableColumns = [
      {
        columnDef: 'nameAr',
        header: 'Document_Categories.Document_Categories_NAME_AR',
        columnType: ColumnTypeEnum.TextLink,
        cell: (element: PeriodicElement) => `${element.nameAr}`,
        class: 'text-underline',
        isSortingBy: true,
      },
      {
        columnDef: 'nameEn',
        header: 'Document_Categories.Document_Categories_NAME_EN',
        columnType: ColumnTypeEnum.TextLink,
        cell: (element: PeriodicElement) => `${element.nameEn}`,
        class: 'text-underline',
        isSortingBy: true,
      },
       {
        columnDef: 'displayOrder',
        header: 'Document_Categories.DISPLAY_ORDER',
        columnType: ColumnTypeEnum.TextLink,
        cell: (element: PeriodicElement) => `${element.displayOrder}`,
        class: 'text-underline',
        isSortingBy: true,
      },
      {
        columnDef: 'ddlActions',
        header: 'Document_Categories.ACTIONS',
        columnType: ColumnTypeEnum.Actions,
        displayMode: ActionDisplayMode.Flex,
        cell: (element: PeriodicElement) => ({
          buttons: [{
            label: 'Document_Categories.EDIT',
            action: 'edit',
            iconSrc: 'assets/images/edit.png',
          }],
        }),
      },
    ];
    this.displayedColumns = this.tableColumns.map((c) => c.columnDef);
  }

  getList(pageNo:number,pageSize:number,search:string,orderBy:string) {
    this.documnetCateogryService.documentCategoryList(pageNo, pageSize, search,orderBy).subscribe((res: any) => {
      this.ELEMENT_DATA = res.data;
   console.log('ELEMENT_DATA', this.ELEMENT_DATA);
      this.tableDataSource.data=res.data
      this.totalCount = res.totalCount
    });
  }

  onClickAction(actionData: TableActionEvent) {
    this.edit(actionData.row);
  }

  edit(row: any) {
    this.documnetCateogryService.getDocumentCategoryById(row.id).subscribe((res: any) => {
      const documentCatregory = res.data;
      const dialogRef = this.dialog.open(CategoryDialogComponent, {
        width: '500px',
        data: {
          isEdit: true,
          arabicName: documentCatregory.nameAr,
          englishName: documentCatregory.nameEn,
          displayOrder: documentCatregory.displayOrder,
          id: documentCatregory.id
          ,documentCategoryNames :this.tableDataSource
        },
      });

      dialogRef.afterClosed().subscribe((result: any) => {
        if (result) {
          let obj: any = {
            id: documentCatregory.id,
            nameAr: result.arabicName,
            nameEn: result.englishName,
            displayOrder: result.displayOrder,
          };
          this.documnetCateogryService.updateDocumentCategory(obj).subscribe((res: any) => {
            if (res.successed) {
              Swal.fire({
                icon: 'success',
                title: this.translateService.instant('Document_Categories.DATA_MODIFIED_SUCCESSFULLY'),
                showConfirmButton: false,
                timer: 1500,
              });
              this.getList(0, 0, '', 'Id desc');
            }
          });
        }
      });
    });
  }


  onSwitchToggle(event: SwitchToggleEvent) {
    console.log('Switch toggled:', event);
  }
  onTextLinkClick(event: TextLinkClickEvent) {
    console.log('TextLink Clicked:', event);
  }
  isAllSelected() {
    const numSelected = this.selection.selected.length;
    const numRows = this.tableDataSource.data.length;
    return numSelected === numRows;
  }
  toggleAllRows() {
    if (this.isAllSelected()) {
      this.selection.clear();
    } else {
      this.selection.select(...this.tableDataSource.data);
    }
  }

  /** Toggle selection of a single row */
  toggleRow(row: any) {
    this.selection.toggle(row);
  }
  handleAction(row: any) {
    console.log('Action triggered for row:', row);
  }

  onSortChanged(event: { active: string; direction: string }): void {
    const { active, direction } = event;
    console.log('Sort column:', active);
    console.log('Sort direction:', direction);
    /*
  get data from api call
  */
  }
  onPageChange(event: any): void {
    console.log('Page index:', event.pageIndex);
    console.log('Page size:', event.pageSize);
    /*
  get data from api call
  */
  }

  customAction(element: any) {
    console.log('Custom action executed for:', element);
  }
  onCreateNewCategory() {
    if (this.isDialogOpen) return;
    this.isDialogOpen = true;

    const dialogRef = this.dialog.open(CategoryDialogComponent, {
      width: '500px',
      data: { isEdit: false,documentCategoryNames :this.tableDataSource },
    });

    dialogRef.afterClosed().subscribe((result: any) => {
      this.isDialogOpen = false;

      if (result) {
        let obj: any = {
          id: 0,
          nameAr: result.arabicName,
          nameEn: result.strategyName,
          displayOrder: result.displayOrder,
        };
        this.documnetCateogryService.createDocumentCategory(obj).subscribe((res: any) => {
          if (res.successed) {
            Swal.fire({
              icon: 'success',
              title: this.translateService.instant(
                'Document_Categories.RECORD_SAVED_SUCCESSFULLY'
              ),
              showConfirmButton: false,
              timer: 1500,
            });
          }
          this.getList(0,0,'','Id desc');
        });
      }
    });
  }
}
