@import "../../../../../assets/scss/variables";

.breadcrumb-container {
  margin-bottom: 1rem;
}

.header-container {
  display: flex;
@media (max-width: 768px) {
    flex-direction: column;
    justify-content: start !important;
    align-items: start !important;
  }

  .submit-vote{
@media (max-width: 768px) {
        height: 46px;
    font-size: 15px;
  }
}

  .title-container {
    .title {
      color: $navy-blue;
      font-size: 24px;
      font-style: normal;
      font-weight: 700;
      line-height: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
.status {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 12px;
      height: 24px;
      border-radius: 20px;
    padding: 10px;

    .circle{
      width: 12px;
      height: 12px;
      border-radius: 50%;
    }

      &.active {
        color: #28a745;

        span {
          color: #28a745;
        }
      }

      span {
        font-size: 12px;
      }
    }
  .approved {
      color: #27AE60;

      background-color: #DDFCDD;
      span {
        background-color: #27AE60;
    }
  }

   .rejected {
      color: #FF5F3D;

      background-color: #FDF1EB;
      span {
        background-color: #FF5F3D;
    }
  }    }

    .sub-title {
      color: #4f4f4f;

      font-size: 12px;
      font-weight: 400;
      line-height: 24px;
      margin-bottom: 33px;

      span {
        color: $navy-blue;
        font-size: 14px;
        font-weight: 500;
        line-height: 20px;
        margin-right: 8px;
      }
    }

  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;

  .loading-text {
    margin-top: 1rem;
    color: var(--text-secondary);
  }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;

  .error-icon {
    font-size: 3rem;
    color: var(--error-color);
    margin-bottom: 1rem;
  }

  .error-message {
    color: var(--error-color);
    margin-bottom: 2rem;
    font-size: 1.1rem;
  }
}

.resolution-details-container,
.attachment-section {
  // padding: 1rem;
  // max-width: 1200px;
  // margin: 0 auto;

  background-color: $card-background;
  border: 1px solid $border-color;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .info-item {
    .info-label {
      color: $text-grey;
      font-size: 14px;
      font-weight: 700;
      line-height: 20px;
    }

    .description-text {
      max-width: 100%; // or set a fixed width like 250px
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      display: block;
    }

    .info-value {
      color: $navy-blue;
      font-size: 16px;
      font-weight: 500;
      line-height: 16px;
    }

    .status {
      display: flex;
      align-items: center;
      gap: 8px;
      height: 24px;
      border-radius: 20px;
      padding: 10px;

      font-size: 14px;
      font-weight: 400;
      line-height: 16px;
      width: fit-content;

      &.draft {
        background: rgba(117, 85, 172, 0.1);
        color: #7555ac;
      }

      &.pending {
        background: #fff3cd;
        color: #856404;
      }

      &.completing-data {
        background: rgba(157, 112, 9, 0.27);
        color: #9d7009;
      }

      &.waiting-for-confirmation {
        background: rgba(226, 180, 138, 0.34);
        color: #d16440;
      }

      &.confirmed {
        background: rgba(97, 253, 97, 0.14);

        color: #27ae60;
      }

      &.rejected {
        color: $text-grey;
        background: #eaeef1;
      }

      &.voting-inProgress {
        background: rgba(47, 128, 237, 0.1);
        color: #2f80ed;
      }

      &.approved {
        background: #f1faf1;
        color: #0e700e;
      }

      &.not-approved {
        background: rgba(197, 15, 31, 0.1);

        color: #c50f1f;
      }

      &.cancelled {
        background: var(--Color---Grey-5, #e0e0e0);

        color: #4f4f4f;
      }
    }
  }
}

.attachment-section {
  padding: 18px;
  height: fit-content;

  .title {
    color: $dark-blue;
    font-size: 16px;
    font-weight: 700;
    line-height: 28px;
    letter-spacing: -0.32px;
  }

  .sub-title {
    color: $text-grey;
    font-size: 14px;
    font-weight: 700;
    line-height: 20px;
    padding: 8px 0;

    .attachment-number {
      border-radius: 14px;
      background: rgba(38, 86, 135, 0.12);
      color: #000;
      font-size: 12px;
      font-weight: 400;
      line-height: 18px;
      margin-right: 6px;
      padding: 0.6px 5.5px;
    }
  }

}

.details-header {
  margin-bottom: 2rem;

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
  }

  .page-title {
    margin: 0;
    color: var(--primary-color);
    font-size: 1.75rem;
    font-weight: 600;
  }

  .header-actions {
    display: flex;
    gap: 0.5rem;

    .expand-button {
      background-color: transparent;
      border: 1px solid transparent;
    }
  }
}

.status-section {
  margin-bottom: 2rem;

  mat-chip {
    font-weight: 500;

    &.status-1 {
      // Draft
      background-color: #e3f2fd;
      color: #1976d2;
    }

    &.status-2 {
      // Pending
      background-color: #fff3e0;
      color: #f57c00;
    }

    &.status-3 {
      // Approved/Confirmed
      background-color: #e8f5e8;
      color: #2e7d32;
    }

    &.status-4 {
      // Rejected/Cancelled
      background-color: #ffebee;
      color: #c62828;
    }

    &.status-5 {
      // Completing data
      background-color: #f3e5f5;
      color: #7b1fa2;
    }

    &.status-6 {
      // Waiting for confirmation
      background-color: #e0f2f1;
      color: #00695c;
    }
  }
}

.card-container {
  &.expanded {
    max-height: 500px;
  }

  .info-card,
  .file-card,
  .attachments-card,
  .items-card,
  .history-card,
  .rejection-card {
    margin-bottom: 2rem;

    mat-card-header {
      margin-bottom: 1rem;

      mat-card-title {
        color: var(--primary-color);
        font-size: 1.25rem;
        font-weight: 600;
      }
    }
  }
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;

  .info-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;

    &.full-width {
      grid-column: 1 / -1;
    }

    .info-label {
      font-weight: 600;
      color: var(--text-primary);
      font-size: 0.9rem;
    }

    .info-value {
      color: var(--text-secondary);
      font-size: 1rem;
      word-break: break-word;
    }
  }
}

.file-actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.attachments-list {
  .attachment-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    margin-bottom: 0.5rem;

    &:last-child {
      margin-bottom: 0;
    }

    .attachment-info {
      display: flex;
      flex-direction: column;
      gap: 0.25rem;

      .attachment-name {
        font-weight: 500;
        color: var(--text-primary);
      }

      .attachment-size {
        font-size: 0.875rem;
        color: var(--text-secondary);
      }
    }

    .attachment-actions {
      display: flex;
      gap: 0.5rem;
    }
  }
}

.attachment-counter {
  font-size: 0.9rem;
  color: var(--text-secondary);
  font-weight: normal;
}

.items-list {
  .item-card {
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1rem;

    &:last-child {
      margin-bottom: 0;
    }

    .item-header {
      margin-bottom: 1rem;

      .item-title {
        margin: 0;
        color: var(--primary-color);
        font-size: 1.1rem;
        font-weight: 600;
      }
    }

    .item-content {
      .item-description {
        margin-bottom: 1rem;
        color: var(--text-secondary);
        line-height: 1.6;
      }

      .conflict-info {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem;
        background-color: #fff3e0;
        border-radius: 6px;
        border-left: 4px solid #ff9800;

        .conflict-icon {
          color: #ff9800;
          font-size: 1.25rem;
        }

        .conflict-text {
          color: #e65100;
          font-weight: 500;
        }

        .view-members-btn {
          margin-left: auto;
          color: var(--primary-color);
        }
      }
    }
  }
}

.history-list {
  .history-item {
    padding: 1rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    margin-bottom: 0.5rem;

    &:last-child {
      margin-bottom: 0;
    }

    .history-info {
      display: grid;
      grid-template-columns: 2fr 1fr 1fr 1fr;
      gap: 1rem;
      align-items: center;

      .action-name {
        font-weight: 600;
        color: var(--text-primary);
      }

      .user-role {
        color: var(--primary-color);
        font-size: 0.9rem;
      }

      .user-name {
        color: var(--text-secondary);
        font-size: 0.9rem;
      }

      .date-time {
        color: var(--text-secondary);
        font-size: 0.875rem;
      }
    }
  }
}

.rejection-card {
  border-left: 4px solid var(--error-color);

  .rejection-content {
    .rejection-text {
      color: var(--text-secondary);
      line-height: 1.6;
      font-style: italic;
    }
  }
}

.action-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
  padding: 2rem 0;
  border-top: 1px solid var(--border-color);
}

// Responsive design
@media (max-width: 768px) {
  .resolution-details-container {
    padding: 0.5rem;
  }

  .details-header .header-content {
    flex-direction: column;
    align-items: flex-start;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .file-actions {
    flex-direction: column;
  }

  .action-buttons {
    flex-direction: column;
  }

  .attachment-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
}

// RTL support
[dir="rtl"] {
  .details-header .header-content {
    text-align: right;
  }

  .info-item {
    text-align: right;
  }

  .conflict-info {
    border-left: none;
    border-right: 4px solid #ff9800;
  }
}

.resolution-details-container {
  background-color: $card-background;
  border: 1px solid $border-color;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  &:last-of-type {
    margin-bottom: 30px;
  }
}

.header {
  margin: 0 0 42px;
  font-size: 24px;
  font-weight: 700;
  line-height: 20px;
}

.resolution-details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;

  .header-actions {
    display: flex;
    gap: 12px;

    button {
      background: none;
      border: none;
      cursor: pointer;
      padding: 8px;
      color: #666;
      transition: color 0.2s ease;

      &:hover {
        color: #333;
      }

      .fa-pencil {
        color: #eaa300;
      }

      .expand {
        color: $navy-blue;
      }

      i {
        font-size: 16px;
      }
    }
  }

  .section-title {
    font-size: 16px;
    font-weight: 700;
    margin: 0;
    line-height: 22px;
    display: flex;
    align-items: center;

    span {
      border-radius: 14px;
      background: rgba(38, 86, 135, 0.12);
      color: var(--Color---Black-1, #000);

      font-size: 16px;
      font-weight: 400;
      line-height: 18px;
      /* 112.5% */
      display: flex;
      padding: 8px;
      justify-content: center;
      align-items: center;
      gap: 10px;
      margin-right: 10px;
      margin-left: 10px;
    }
  }
}

hr {
  color: $border-hr-color;
  border: 1px solid;
  margin: 0;
}

.resolution-details-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;

  &.expanded {
    max-height: fit-content;
  }

  .item-container {
    border-radius: 8px;
    background: #fff;
    padding: 16px;

    // display: flex;
    // justify-content: space-between;
    // align-items: center;
    .top-section {
      padding: 0 18px;

      .title {
        color: $navy-blue;
        font-size: 20px;
        font-weight: 500;
        line-height: 32px;

         .status {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 12px;
      height: 24px;
      border-radius: 20px;
    padding: 10px;

    .circle{
      width: 12px;
      height: 12px;
      border-radius: 50%;
    }

      &.active {
        color: #28a745;

        span {
          color: #28a745;
        }
      }

      span {
        font-size: 12px;
      }
    }
  .approved {
      color: #27AE60;

      background-color: #DDFCDD;
      span {
        background-color: #27AE60;
    }
  }

   .rejected {
      color: #FF5F3D;

      background-color: #FDF1EB;
      span {
        background-color: #FF5F3D;
    }
  }
      }

      .conflict-btn {
        color: #b68107;
        font-size: 14px;
        font-weight: 400;
        line-height: 18px;
        border-radius: 8px;
        border: 1px solid #ffc800;
        background: #fffaeb;
        padding: 7px 13px;
      }
      .btn-action{
      font-size: 14px;
      font-weight: 400;
      line-height: 20px;
      border-radius: 8px;
      background-color: transparent;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 5px 21px;
      //smargin: 0 16px;
    }
       .notes-btn{
       background-color: transparent;
      color: #00205A;
       border: 1px solid #00205A;
         img{
        padding: 0 5px;
      }

    }
    .item-conflict{
      color: #B68107;
font-size: 14px;
font-weight: 400;
line-height: 18px;
border-radius: 8px;
border: 1px solid #FFC800;
background: #FFF;
display: flex;
height: 36px;
min-width: 24px;
padding: 10px;
align-items: center;
gap: 2px;
    }
    }

    .sub-title {
      color: #333;
      font-size: 14px;
      font-weight: 400;
      line-height: 21px;
      margin-top: 12px;
      padding: 0 18px;
    }



  .btn-items-container {
        display: flex;

    @media (max-width: 768px) {
    flex-direction: column;
    justify-content: start !important;
    align-items: start !important;
  }
    justify-content: end;
    .btn-action{
      font-size: 14px;
      font-weight: 400;
      line-height: 20px;
      border-radius: 8px;
      background-color: transparent;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 5px 21px;
      margin: 0 16px;
        @media (max-width: 768px) {
        margin-bottom: 10px;
       }
    }
    .reject-btn {
      color: #C50F1F;
      border: 1px solid #C50F1F;
      img{
        padding: 0 5px;
      }
    }

    button.active-reject-button {
      background-color: #C50F1F;
      color: white;
    }

     .approve-btn {
      color: #27AE60;
      border: 1px solid #27AE60;
      img{
        padding: 0 5px;
      }
    }

    button.active-approve-button {
      background-color: #27AE60;
      color: white;
    }
    .notes-btn,.add-note-btn{
       background-color: transparent;
      color: #00205A;
       border: 1px solid #00205A;
         img{
        padding: 0 5px;
      }

    }
  }

}
}

.info-item {
  .info-label {
    color: $text-grey;
    font-size: 14px;
    font-weight: 700;
    line-height: 20px;
  }

  .info-value {
    color: $navy-blue;
    font-size: 16px;
    font-weight: 500;
    line-height: 16px;
  }

  .status {
    display: flex;
    align-items: center;
    gap: 8px;
    height: 24px;
    border-radius: 20px;
    padding: 10px;

    font-size: 14px;
    font-weight: 400;
    line-height: 16px;

    &.draft {
      background: rgba(117, 85, 172, 0.1);
      color: #7555ac;
    }

    &.pending {
      background: #fff3cd;
      color: #856404;
    }

    &.completing-data {
      background: rgba(157, 112, 9, 0.27);
      color: #9d7009;
    }

    &.waiting-for-confirmation {
      background: rgba(226, 180, 138, 0.34);
      color: #d16440;
    }

    &.confirmed {
      background: rgba(97, 253, 97, 0.14);

      color: #27ae60;
    }

    &.rejected {
      color: $text-grey;
      background: #eaeef1;
    }

    &.voting-inProgress {
      background: rgba(47, 128, 237, 0.1);
      color: #2f80ed;
    }

    &.approved {
      background: #f1faf1;
      color: #0e700e;
    }

    &.not-approved {
      background: rgba(197, 15, 31, 0.1);

      color: #c50f1f;
    }

    &.cancelled {
      background: var(--Color---Grey-5, #e0e0e0);

      color: #4f4f4f;
    }
  }
}

.details-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
  padding: 16px 0;

  .detail-item {
    display: flex;
    flex-direction: column;

    label {
      display: block;
      font-size: 14px;
      color: $text-grey;
      font-weight: 700;
      margin-bottom: 8px;
    }

    button {
      background: none;
      border: none;
      cursor: pointer;
      padding: 8px;
      color: #666;
      transition: color 0.2s ease;

      &:hover {
        color: #333;
      }

      .fa-pencil {
        color: #eaa300;
      }

      .expand {
        color: $navy-blue;
      }

      i {
        font-size: 16px;
      }
    }

    .value {
      font-size: 16px;
      color: $navy-blue;
      font-weight: 500;
    }

    .date-value {
      display: flex;
      flex-direction: row;
      gap: 6px;

      .gregorian {
        font-size: 16px;
        color: $navy-blue;
        font-weight: 500;
      }

      .hijri {
        font-size: 12px;
        color: $text-grey;
      }
    }

    .status {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 12px;
      height: 24px;
      border-radius: 20px;
      padding: 10px;

      .circle {
        width: 12px;
        height: 12px;
        border-radius: 50%;
      }

      &.active {
        color: #28a745;

        span {
          color: #28a745;
        }
      }

      span {
        font-size: 12px;
      }
    }

    .status-new span {
      background-color: gray;
    }

    .status-under-construction {
      background-color: #e5eefb;
      color: #2f80ed;

      span {
        background-color: #2f80ed;
      }
    }

    .status-waiting {
      color: #ff5f3d;

      background-color: #fdf1eb;

      span {
        background-color: #ff5f3d;
      }
    }

    .status-active {
      color: #27ae60;

      background-color: #f1faf1;

      span {
        background-color: #27ae60;
      }
    }

    .status-exited {
      color: $text-grey;

      background-color: #e0e0e0;

      span {
        background-color: $text-grey;
      }
    }

    .custom-btn {
      color: $navy-blue;
      border: 1px solid #00205a;
      background-color: #fff;
      border-radius: 8px;

      cursor: default;
      padding: 0 31.426px;
      height: 28px;
      font-weight: 400;
    }
  }
}

// Responsive styles
@media (max-width: 768px) {
  .details-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .details-grid {
    grid-template-columns: 1fr;
  }
}
