import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { ButtonTypeEnum, IconEnum } from '@core/enums/icon-enum';
import { IBreadcrumbItem } from '@core/gl-interfaces/IBreadcrumbItem/ibreadcrumb-item';
import { DurationTimePipe } from '@core/gl-pipes/duration-time.pipe';
import { TranslateModule } from '@ngx-translate/core';
import { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';
import { CustomButtonComponent } from '@shared/components/custom-button/custom-button.component';
import { MeetingStatusEnum } from '@shared/enum/meeting-enums';
import { SizeEnum } from '@shared/enum/size-enum';
import { Subscription, timer } from 'rxjs';
import { AttachmentCardComponent } from 'src/app/features/resolutions/components/attachment-card/attachment-card.component';

@Component({
  selector: 'app-view-meeting-details',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    BreadcrumbComponent,
    CustomButtonComponent,
    AttachmentCardComponent,
    DurationTimePipe
  ],
  templateUrl: './view-meeting-details.component.html',
  styleUrl: './view-meeting-details.component.scss'
})
export class ViewMeetingDetailsComponent {
  breadcrumbItems: IBreadcrumbItem[] = [
    { label: 'BREADCRUMB.HOME', url: '/admin/dashboard' },
    { label: 'BREADCRUMB.FUNDS', url: '/admin/investment-funds' },
    { label: 'RESOLUTIONS.TITLE', url: '' },
  ];
  breadcrumbSizeEnum = SizeEnum;
  createButtonIcon = IconEnum;
  buttonEnum = ButtonTypeEnum
  meetingStatus = MeetingStatusEnum

  isExpanded: boolean= true;
  isExpandedItem: boolean= true;
  isExpandedAction: boolean= true;

  countDown: Subscription | undefined;
  counterTimer: number = 0
  tick = 1000;

  constructor(
    private router: Router
  ) {}

  ngOnInit() {
    this.initMeetingTimerCounter();
  }

  onBreadcrumbClicked(item: IBreadcrumbItem): void {
    if (item.url && !item.disabled) {
      this.router.navigateByUrl(item.url);
    }
  }

  toggleExpand() {
    this.isExpanded = !this.isExpanded;
  }
  toggleExpandItems() {
    this.isExpandedItem = !this.isExpandedItem;
  }

  toggleExpandActions() {
    this.isExpandedAction = !this.isExpandedAction;
  }

  getStatusClass(statusId: number): string {
    switch (statusId) {
      case this.meetingStatus.NotStartedYet:
        return 'pending';
      case this.meetingStatus.InProgress:
        return 'meeting-inProgress';
      case this.meetingStatus.Finished:
        return 'finished';
      case this.meetingStatus.Cancelled:
        return 'cancelled';
      default:
        return '';
    }
  }
  startMeeting() {}
  cancel() {}

  addAttachment() {}

  saveAttendees() {}

  initMeetingTimerCounter() {
    // this.counterTimer = this.evaluationFormData?.evaluationFormPeriod ? this.evaluationFormData?.evaluationFormPeriod * 60 : 0;
    this.counterTimer = 15 * 60;
    this.countDown = timer(0, this.tick).subscribe(() => {
      --this.counterTimer;
      if (this.counterTimer == 0) {
        this.countDown?.unsubscribe();
      }
    });

  }

  goBack() {}
}
