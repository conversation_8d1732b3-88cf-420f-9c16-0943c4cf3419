import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { ButtonTypeEnum, IconEnum } from '@core/enums/icon-enum';
import { IBreadcrumbItem } from '@core/gl-interfaces/IBreadcrumbItem/ibreadcrumb-item';
import { DurationTimePipe } from '@core/gl-pipes/duration-time.pipe';
import { TranslateModule } from '@ngx-translate/core';
import { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';
import { CustomButtonComponent } from '@shared/components/custom-button/custom-button.component';
import { MeetingStatusEnum } from '@shared/enum/meeting-enums';
import { SizeEnum } from '@shared/enum/size-enum';
import { Subscription, timer } from 'rxjs';
import { AttachmentCardComponent } from 'src/app/features/resolutions/components/attachment-card/attachment-card.component';

// Attendance status enum
export enum AttendanceStatus {
  Unregistered = 0,
  Attending = 1,
  Away = 2
}

// Interface for meeting attendee
export interface MeetingAttendee {
  id: number;
  fullName: string;
  role: string;
  attendanceStatus: AttendanceStatus;
}

@Component({
  selector: 'app-view-meeting-details',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    BreadcrumbComponent,
    CustomButtonComponent,
    AttachmentCardComponent,
    DurationTimePipe
  ],
  templateUrl: './view-meeting-details.component.html',
  styleUrl: './view-meeting-details.component.scss'
})
export class ViewMeetingDetailsComponent {
  breadcrumbItems: IBreadcrumbItem[] = [
    { label: 'BREADCRUMB.HOME', url: '/admin/dashboard' },
    { label: 'BREADCRUMB.FUNDS', url: '/admin/investment-funds' },
    { label: 'RESOLUTIONS.TITLE', url: '' },
  ];
  breadcrumbSizeEnum = SizeEnum;
  createButtonIcon = IconEnum;
  buttonEnum = ButtonTypeEnum
  meetingStatus = MeetingStatusEnum
  attendanceStatus = AttendanceStatus;

  isExpanded: boolean= true;
  isExpandedItem: boolean= true;
  isExpandedAction: boolean= true;

  countDown: Subscription | undefined;
  counterTimer: number = 0
  tick = 1000;

  // Attendees data - this would typically come from an API
  attendees: MeetingAttendee[] = [
    { id: 1, fullName: 'أحمد الراشد', role: 'مدير الصندوق', attendanceStatus: AttendanceStatus.Unregistered },
    { id: 2, fullName: 'فاطمة الزهراء', role: 'المستشار القانوني', attendanceStatus: AttendanceStatus.Unregistered },
    { id: 3, fullName: 'محمد آل سعود', role: 'أمين سر المجلس', attendanceStatus: AttendanceStatus.Unregistered },
    { id: 4, fullName: 'سارة المطيري', role: 'عضو مجلس إدارة مستقل', attendanceStatus: AttendanceStatus.Unregistered },
    { id: 5, fullName: 'عمر الخطيب', role: 'المدير المالي', attendanceStatus: AttendanceStatus.Unregistered },
    // Add more attendees as needed
  ];

  constructor(
    private router: Router
  ) {}

  // Getter methods for dynamic summary counts
  get attendingCount(): number {
    return this.attendees.filter(attendee => attendee.attendanceStatus === AttendanceStatus.Attending).length;
  }

  get awayCount(): number {
    return this.attendees.filter(attendee => attendee.attendanceStatus === AttendanceStatus.Away).length;
  }

  get unregisteredCount(): number {
    return this.attendees.filter(attendee => attendee.attendanceStatus === AttendanceStatus.Unregistered).length;
  }

  ngOnInit() {
    this.initMeetingTimerCounter();
  }

  onBreadcrumbClicked(item: IBreadcrumbItem): void {
    if (item.url && !item.disabled) {
      this.router.navigateByUrl(item.url);
    }
  }

  toggleExpand() {
    this.isExpanded = !this.isExpanded;
  }
  toggleExpandItems() {
    this.isExpandedItem = !this.isExpandedItem;
  }

  toggleExpandActions() {
    this.isExpandedAction = !this.isExpandedAction;
  }

  getStatusClass(statusId: number): string {
    switch (statusId) {
      case this.meetingStatus.NotStartedYet:
        return 'pending';
      case this.meetingStatus.InProgress:
        return 'meeting-inProgress';
      case this.meetingStatus.Finished:
        return 'finished';
      case this.meetingStatus.Cancelled:
        return 'cancelled';
      default:
        return '';
    }
  }
  startMeeting() {}
  cancel() {}

  addAttachment() {}

  saveAttendees() {
    // Implementation for saving attendees data to backend
    console.log('Saving attendees:', this.attendees);
  }

  /**
   * Update attendance status for a specific attendee
   * @param attendeeId - The ID of the attendee
   * @param newStatus - The new attendance status
   */
  updateAttendanceStatus(attendeeId: number, newStatus: AttendanceStatus): void {
    const attendee = this.attendees.find(a => a.id === attendeeId);
    if (attendee) {
      attendee.attendanceStatus = newStatus;
      // The summary counts will automatically update due to the getter methods
    }
  }

  /**
   * Handle attendance status change from dropdown
   * @param event - The change event
   * @param attendeeId - The ID of the attendee
   */
  onAttendanceStatusChange(event: Event, attendeeId: number): void {
    const target = event.target as HTMLSelectElement;
    if (target) {
      const newStatus = parseInt(target.value) as AttendanceStatus;
      this.updateAttendanceStatus(attendeeId, newStatus);
    }
  }

  /**
   * Get attendance status options for dropdown
   */
  getAttendanceStatusOptions() {
    return [
      { value: AttendanceStatus.Unregistered, label: 'MEETINGS.UNREGISTERED' },
      { value: AttendanceStatus.Attending, label: 'MEETINGS.ATTENDANT' },
      { value: AttendanceStatus.Away, label: 'MEETINGS.AWAY' }
    ];
  }

  /**
   * Get the display text for attendance status
   */
  getAttendanceStatusText(status: AttendanceStatus): string {
    switch (status) {
      case AttendanceStatus.Attending:
        return 'حاضر';
      case AttendanceStatus.Away:
        return 'غائب';
      case AttendanceStatus.Unregistered:
      default:
        return 'غير مسجل';
    }
  }

  initMeetingTimerCounter() {
    // this.counterTimer = this.evaluationFormData?.evaluationFormPeriod ? this.evaluationFormData?.evaluationFormPeriod * 60 : 0;
    this.counterTimer = 15 * 60;
    this.countDown = timer(0, this.tick).subscribe(() => {
      --this.counterTimer;
      if (this.counterTimer == 0) {
        this.countDown?.unsubscribe();
      }
    });

  }

  goBack() {}
}
