import { Component, Input, OnInit, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import Swal from 'sweetalert2';
import { MatTableModule, MatTableDataSource } from '@angular/material/table';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDialog } from '@angular/material/dialog';


// Shared components
import { TableComponent } from '@shared/components/table/table.component';
import { CustomButtonComponent } from '@shared/components/custom-button/custom-button.component';

// Interfaces and types
import { ITableColumn, TableActionEvent, ActionDisplayMode } from '@core/gl-interfaces/I-table/i-table';
import { ColumnTypeEnum } from '@core/gl-interfaces/I-table/column-type/column-type-enum';
import { ButtonTypeEnum, IconEnum } from '@core/enums/icon-enum';

// API and services
import {
  DocumentServiceProxy,
  DocumentCategoryDto,
  DocumentDto
} from '@core/api/api.generated';
import { ErrorModalService } from '@core/services/error-modal.service';
import { FilePreviewService } from '@core/services/file-preview.service';
import { TokenService } from '../../../auth/services/token.service';

// Components
import { DocumentViewerComponent } from '../document-viewer/document-viewer.component';

@Component({
  selector: 'app-document-list',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    MatTableModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    TableComponent,
    CustomButtonComponent
  ],
  templateUrl: './document-list.component.html',
  styleUrls: ['./document-list.component.scss']
})
export class DocumentListComponent implements OnInit, OnChanges {
  @Input() category: DocumentCategoryDto | null = null;
  @Input() fundId: number | null = null;

  // Table configuration
  documents: DocumentDto[] = [];
  tableDataSource = new MatTableDataSource<DocumentDto>([]);
  isLoading = false;
  totalCount = 0;
  pageSize = 10;
  currentPage = 1;

  // Table columns
  tableColumns: ITableColumn[] = [];

  displayedColumns = ['name', 'formattedFileSize', 'createdAt', 'actions'];

  // UI enums
  buttonEnum = ButtonTypeEnum;
  iconEnum = IconEnum;

  // Permission properties
  canDeleteDocument = false;

  constructor(
    private documentProxy: DocumentServiceProxy,
    private errorModalService: ErrorModalService,
    private dialog: MatDialog,
    private translateService: TranslateService,
    private tokenService: TokenService,
    private filePreviewService: FilePreviewService
  ) {}

  ngOnInit(): void {
    // Initialize permissions
    this.initializePermissions();

    // Only load documents if category is available
    if (this.category) {
      this.loadDocuments();
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    // Reload documents when category or fundId changes
    if ((changes['category'] || changes['fundId']) && this.category) {
      // Reset pagination when category changes
      if (changes['category']) {
        this.currentPage = 1;
      }
      this.loadDocuments();
    }
  }

  loadDocuments(search?:any): void {
    if (!this.category) {
      console.warn('Cannot load documents: category is null or undefined');
      return;
    }

    this.isLoading = true;
    this.documents = []; // Clear existing documents while loading

    // Use NSwag-generated ApiServiceProxy with proper typing
    this.documentProxy.getAll(
      this.category.id,
      this.fundId || undefined,
      this.currentPage,
      this.pageSize,
      search,
      ''
    ).subscribe({
      next: (response: any) => {
        // Response is properly typed as DocumentDtoPaginatedResult
        if (response?.data && Array.isArray(response.data)) {
          this.documents = response.data;
          this.totalCount = response.totalCount || 0;
        } else {
          this.documents = [];
          this.totalCount = 0;
        }
        this.tableDataSource.data = this.documents;
        this.isLoading = false;
      },
      error: (error: any) => {
        console.error('Error loading documents:', error);
        this.errorModalService.showError(this.translateService.instant('DOCUMENTS.ERRORS.LOAD_DOCUMENTS_FAILED'));
        this.isLoading = false;
      }
    });
  }

  onTableAction(event: TableActionEvent): void {
    switch (event.action) {
      case 'view':
        this.viewDocument(event.row);
        break;
      case 'delete':
        this.deleteDocument(event.row);
        break;
    }
  }

  viewDocument(document: DocumentDto): void {
    console.log('Opening document preview for:', document);

    // Try preview first using FilePreviewService, fallback to direct URL or dialog
    this.filePreviewService.previewFileById(
      document.attachmentId!,
      document.name || '',
      () => {
        // Fallback: use existing preview URL or open dialog
        this.fallbackToDirectPreview(document);
      }
    );
  }

  private fallbackToDirectPreview(document: DocumentDto): void {
    console.log('Falling back to direct preview for:', document);

    // If document has a preview URL, open it directly
    if (document.previewUrl) {
      console.log('Using existing preview URL:', document.previewUrl);
      window.open(document.previewUrl, '_blank');
    } else {
      // Open the document viewer dialog as final fallback
      this.openDocumentViewerDialog(document);
    }
  }

  private openDocumentViewerDialog(document: DocumentDto): void {
    const dialogData = {
      document: document,
      bucketName: 'documents' // Default bucket name since DocumentDto doesn't have bucketName
    };

    console.log('Dialog data:', dialogData);
window.open( dialogData.document.previewUrl, '_blank');
    // const dialogRef = this.dialog.open(DocumentViewerComponent, {
    //   width: '90vw',
    //   height: '90vh',
    //   maxWidth: '1200px',
    //   maxHeight: '800px',
    //   data: dialogData
    // });

    // dialogRef.afterClosed().subscribe(result => {
    //   // Handle any actions after viewer is closed
    //   console.log('Document viewer closed with result:', result);
    // });
  }

  deleteDocument(document: DocumentDto): void {
    Swal.fire({
      title: this.translateService.instant('COMMON.CONFIRM_DELETE'),
      text: this.translateService.instant('DOCUMENTS.DELETE_CONFIRM', { fileName: document.name }),
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#d33',
      cancelButtonColor: '#3085d6',
      confirmButtonText: this.translateService.instant('COMMON.DELETE'),
      cancelButtonText: this.translateService.instant('COMMON.CANCEL')
    }).then((result) => {
      if (result.isConfirmed) {
        // Use NSwag-generated ApiServiceProxy for deletion
        this.documentProxy.delete(document.id).subscribe({
          next: (response: any) => {
            // Show success message
            Swal.fire({
              title: this.translateService.instant('COMMON.SUCCESS'),
              text: this.translateService.instant('DOCUMENTS.DELETE_SUCCESS'),
              icon: 'success',
              timer: 2000,
              showConfirmButton: false
            });
            // Reload the list
            this.loadDocuments();
          },
          error: (error: any) => {
            console.error('Error deleting document:', error);
            // Show error message
            Swal.fire({
              title: this.translateService.instant('COMMON.ERROR'),
              text: this.translateService.instant('DOCUMENTS.DELETE_FAILED'),
              icon: 'error',
              confirmButtonText: this.translateService.instant('COMMON.OK')
            });
          }
        });
      }
    });
  }

  formatFileSize(bytes: number): string {
    if (!bytes || bytes === 0) return '0 B';

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  onPageChange(event: any): void {
    this.currentPage = event.pageIndex + 1;
    this.pageSize = event.pageSize;
    this.loadDocuments();
  }

  /**
   * Public method to refresh the document list
   * Can be called from parent component
   */
  refreshDocuments(): void {
    this.currentPage = 1; // Reset to first page
    this.loadDocuments();
  }

  /**
   * Initialize permission-based access control
   */
  private initializePermissions(): void {
    this.canDeleteDocument = this.tokenService.hasPermission('Document.Delete');
    this.initializeTableColumns();
  }

  /**
   * Initialize table columns with permission-based actions
   */
  private initializeTableColumns(): void {
    this.tableColumns = [
      {
        columnDef: 'name',
        header: 'DOCUMENTS.COLUMNS.NAME',
        columnType: ColumnTypeEnum.Text,
        cell: (element: DocumentDto) => element.name || ''
      },
      {
        columnDef: 'formattedFileSize',
        header: 'DOCUMENTS.COLUMNS.SIZE',
        columnType: ColumnTypeEnum.Text,
        cell: (element: DocumentDto) => this.formatFileSize(element.fileSize || 0)
      },
      {
        columnDef: 'createdAt',
        header: 'DOCUMENTS.COLUMNS.UPLOAD_DATE',
        columnType: ColumnTypeEnum.Text,
        cell: (element: DocumentDto) => element.createdAt ? new Date(element.createdAt.toString()).toLocaleDateString() : ''
      },
      {
        columnDef: 'actions',
        header: 'DOCUMENTS.COLUMNS.ACTIONS',
        columnType: ColumnTypeEnum.Actions,
        displayMode: ActionDisplayMode.Flex,
        cell: (element: DocumentDto) => ({
          buttons: [
            {
              label: 'DOCUMENTS.VIEW',
              action: 'view',
              iconSrc: 'assets/icons/download.png',
              class: 'btn-primary mx-1'
            },
            ...(this.canDeleteDocument ? [{
              label: 'DOCUMENTS.DELETE',
              action: 'delete',
              iconSrc: 'assets/icons/cancel-icon.png',
              class: 'btn-danger mx-1'
            }] : [])
          ]
        })
      }
    ];
  }
}
