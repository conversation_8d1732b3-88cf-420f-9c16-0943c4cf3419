import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { TranslateModule } from '@ngx-translate/core';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { of } from 'rxjs';

import { MeetingsProposalFilterComponent } from './meetings-proposal-filter.component';
import { MeetingsServiceProxy } from '@core/api/api.generated';

describe('MeetingsProposalFilterComponent', () => {
  let component: MeetingsProposalFilterComponent;
  let fixture: ComponentFixture<MeetingsProposalFilterComponent>;
  let mockMeetingsServiceProxy: jasmine.SpyObj<MeetingsServiceProxy>;
  let mockDialogRef: jasmine.SpyObj<MatDialogRef<MeetingsProposalFilterComponent>>;

  beforeEach(async () => {
    const meetingsServiceSpy = jasmine.createSpyObj('MeetingsServiceProxy', ['getMeetingsProposalStatus']);
    const dialogRefSpy = jasmine.createSpyObj('MatDialogRef', ['close']);

    await TestBed.configureTestingModule({
      imports: [
        MeetingsProposalFilterComponent,
        TranslateModule.forRoot(),
        HttpClientTestingModule
      ],
      providers: [
        { provide: MeetingsServiceProxy, useValue: meetingsServiceSpy },
        { provide: MatDialogRef, useValue: dialogRefSpy },
        { provide: MAT_DIALOG_DATA, useValue: {} }
      ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(MeetingsProposalFilterComponent);
    component = fixture.componentInstance;
    mockMeetingsServiceProxy = TestBed.inject(MeetingsServiceProxy) as jasmine.SpyObj<MeetingsServiceProxy>;
    mockDialogRef = TestBed.inject(MatDialogRef) as jasmine.SpyObj<MatDialogRef<MeetingsProposalFilterComponent>>;

    // Mock the API response
    const mockStatusResponse = {
      statusCode: 200,
      successed: true,
      message: 'Success',
      data: [
        { id: 1, nameAr: 'مسودة', nameEn: 'Draft', localizedName: 'Draft' },
        { id: 2, nameAr: 'في الانتظار', nameEn: 'Pending', localizedName: 'Pending' },
        { id: 3, nameAr: 'نشط', nameEn: 'Active', localizedName: 'Active' }
      ],
      errors: []
    } as any;
    mockMeetingsServiceProxy.getMeetingsProposalStatus.and.returnValue(of(mockStatusResponse));

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load status options on init', () => {
    expect(mockMeetingsServiceProxy.getMeetingsProposalStatus).toHaveBeenCalled();
    expect(component.statusOptions.length).toBe(3);
  });

  it('should update status form control with loaded options', () => {
    const statusControl = component.formControls.find(control => control.formControlName === 'status');
    expect(statusControl).toBeTruthy();
    expect(statusControl?.options?.length).toBeGreaterThan(0);
  });

  it('should apply filters and close dialog with clean data', () => {
    component.formGroup.patchValue({ status: 1, search: 'test' });
    component.applyFilters();
    expect(mockDialogRef.close).toHaveBeenCalledWith({ status: 1, search: 'test' });
  });
});
