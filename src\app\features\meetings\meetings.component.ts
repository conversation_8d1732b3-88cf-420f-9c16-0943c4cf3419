import { CommonModule } from '@angular/common';
import { Component, ViewChild, viewChild } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTabsModule } from '@angular/material/tabs';
import { Router } from '@angular/router';
import { ButtonTypeEnum, IconEnum } from '@core/enums/icon-enum';
import { IBreadcrumbItem } from '@core/gl-interfaces/IBreadcrumbItem/ibreadcrumb-item';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';
import { PageHeaderComponent } from '@shared/components/page-header/page-header.component';
import { SizeEnum } from '@shared/enum/size-enum';
import { MeetingsListComponent } from './components/meetings-list/meetings-list.component';
import { ProposalListComponent } from './components/Proposal-List/proposal-list.component';

@Component({
  selector: 'app-meetings',
  standalone: true,
  imports: [
    CommonModule,
    MatTabsModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatCardModule,
    TranslateModule,
    PageHeaderComponent,
    BreadcrumbComponent,
    MeetingsListComponent,
    ProposalListComponent
  ],
  templateUrl: './meetings.component.html',
  styleUrl: './meetings.component.scss'
})
export class MeetingsComponent {
  // Breadcrumb configuration
  breadcrumbItems: IBreadcrumbItem[] = [
    { label: 'BREADCRUMB.HOME', url: '/admin/dashboard' },
    { label: 'BREADCRUMB.FUNDS', url: '/admin/investment-funds' },
    { label: 'INVESTMENT_FUNDS.MEETING.MEETINGS', url: '' },
  ];
  breadcrumbSizeEnum = SizeEnum;
  buttonEnum = ButtonTypeEnum;
  iconEnum = IconEnum;
  isLoading: any;
  documentCategories:any = [];
  selectedTabIndex: any;
  totalCount: any;

  constructor(
    private router: Router,
    private translate: TranslateService
  ) {}

  ngOnInit() {
    this.documentCategories = [
      {name: this.translate.instant('INVESTMENT_FUNDS.MEETING.TITLE'), component: 'proposal'},
      {name: this.translate.instant('MEETINGS.TITLE'), component: 'meetings'},
    ]
  }
  
  onTabChange(arg0: number) {
    throw new Error('Method not implemented.');
  }

  onBreadcrumbClicked(item: IBreadcrumbItem): void {
    if (item.url && !item.disabled) {
      this.router.navigateByUrl(item.url);
    }
  }

  handleData($event: number) {
    console.log("totalcount",$event)
    this.totalCount = $event
  }
}
