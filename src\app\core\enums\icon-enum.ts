export enum IconEnum {
  arrowRight = 'assets/icons/arrow-right.png',
  arrowNavyRight = 'assets/icons/right-navy-arrow.png',
  arrowLeft = 'assets/icons/arrow-left (2).png',
  plus = 'assets/icons/add-icon.png',
  verify = 'assets/icons/verify-icon.png',
  cancel = 'assets/icons/cancel-icon.png',
  cancelWhite = 'assets/images/cancel.png',
  reload = 'assets/images/reload.png',
  add = 'assets/icons/add.svg',
  approve = 'assets/images/approve.png',
  reject = 'assets/images/reject.png',
  notes = 'assets/images/notes.png',
  reset = 'assets/images/reset-icon.png',
  draft = 'assets/images/draft.svg',
  edit = 'assets/images/edit-pencil.png',
  play = 'assets/icons/play.svg',
  pause = 'assets/icons/pause.svg'



}
export enum ButtonTypeEnum {
  Primary = 'primary',
  Secondary = 'secondary',
  Danger = 'danger',
  OutLine='outline',
  Success='success'
}

