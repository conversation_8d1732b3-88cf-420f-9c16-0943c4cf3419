<div class="meetings">
  <!-- Breadcrumb -->
  <!-- <div class="breadcrumb-section">
    <app-breadcrumb
      (onClickEvent)="onBreadcrumbClicked($event)"
      [breadcrumbs]="breadcrumbItems"
      [size]="breadcrumbSizeEnum.Medium"
      divider=">">
    </app-breadcrumb>
  </div> -->

  <!-- Page Header -->
  <div class="page-header-section">

    <app-page-header
      [showSearch]="true"
      [showFilter]="true"
      [searchPlaceholder]="translateService.instant('INVESTMENT_FUNDS.RESOLUTIONS.SEARCH_PLACEHOLDER')"
      [restrictSearchToNumbers]="false"
      [showCreateButton]="isHasPermissionAdd"
      createButtonText="INVESTMENT_FUNDS.MEETING.ADD"
      (search)="onSearch($event)" (filter)="openFilter()"
      (create)="addNewProposalMeeting()">
    </app-page-header>
  </div>

  <!-- Loading State -->
  <!-- <div *ngIf="isLoading" class="loading-container text-center py-5">
    <div class="spinner-border" role="status">
      <span class="sr-only">{{ 'COMMON.LOADING' | translate }}</span>
    </div>
    <p class="mt-2">{{ 'INVESTMENT_FUNDS.MEETING.LOADING_PROPOSALS' | translate }}</p>
  </div> -->

  <!-- Error State -->
  <!-- <div *ngIf="hasError && !isLoading" class="error-container text-center py-5">
    <div class="alert alert-danger">
      <h5>{{ 'COMMON.ERROR' | translate }}</h5>
      <p>{{ errorMessage | translate }}</p>
      <button class="btn btn-primary" (click)="loadMeetingsProposals()">
        {{ 'COMMON.RETRY' | translate }}
      </button>
    </div>
  </div> -->

  <!-- Empty State -->
  <div *ngIf="!isLoading && !hasError && MeetingsProposals.length === 0" class="empty-state text-center py-5">
    <p>{{ 'INVESTMENT_FUNDS.MEETING.NO_PROPOSALS_FOUND' | translate }}</p>
  </div>

  <!-- Meetings Grid -->
  <div *ngIf="!isLoading && !hasError && MeetingsProposals.length > 0" class="resolutions-grid mb-3 mt-5">
    <!-- Card -->
    <div class="resolution-card" *ngFor="let meeting of MeetingsProposals">
      <ng-container>
        <div class="card-header">
          <h3 class="resolution-title">
            {{ meeting.subject }}</h3>
          <div class="card-actions">
            <button class="action-btn details-btn"
              (click)="viewMeetingDetails(meeting.id)"
              [title]="'COMMON.VIEW_DETAILS' | translate">
              <img src="assets/images/eye.png" alt="details" />
            </button>

          </div>
        </div>
        <div class="card-content">

          <p class="title mb-3">{{ meeting.description }}</p>

          <div class="resolution-meta">
            <div class="meta-item">
              <!-- Created Date -->
              <p class="meta-label gregorian">
                <svg width="14" height="14" viewBox="0 0 14 14" fill="none"
                  xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M4 8C4.55228 8 5 7.55229 5 7C5 6.44771 4.55228 6 4 6C3.44772 6 3 6.44771 3 7C3 7.55229 3.44772 8 4 8ZM5 10C5 10.5523 4.55228 11 4 11C3.44772 11 3 10.5523 3 10C3 9.44771 3.44772 9 4 9C4.55228 9 5 9.44771 5 10ZM7 8C7.55229 8 8 7.55229 8 7C8 6.44771 7.55229 6 7 6C6.44771 6 6 6.44771 6 7C6 7.55229 6.44771 8 7 8ZM8 10C8 10.5523 7.55229 11 7 11C6.44771 11 6 10.5523 6 10C6 9.44771 6.44771 9 7 9C7.55229 9 8 9.44771 8 10ZM10 8C10.5523 8 11 7.55229 11 7C11 6.44771 10.5523 6 10 6C9.44771 6 9 6.44771 9 7C9 7.55229 9.44771 8 10 8ZM14 2.5C14 1.11929 12.8807 0 11.5 0H2.5C1.11929 0 0 1.11929 0 2.5V11.5C0 12.8807 1.11929 14 2.5 14H11.5C12.8807 14 14 12.8807 14 11.5V2.5ZM1 4H13V11.5C13 12.3284 12.3284 13 11.5 13H2.5C1.67157 13 1 12.3284 1 11.5V4ZM2.5 1H11.5C12.3284 1 13 1.67157 13 2.5V3H1V2.5C1 1.67157 1.67157 1 2.5 1Z"
                    fill="#616161" />
                </svg>
                {{ meeting.createdDate | georgianDate }}
              </p>

              <!-- Total Votes -->
              <p class="meta-label gregorian">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                  xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M12 6V12L16 14M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12Z"
                    stroke="#181D27" stroke-width="2" stroke-linecap="round"
                    stroke-linejoin="round" />
                </svg>
                {{ meeting.totalVotes }} {{ 'INVESTMENT_FUNDS.MEETING.VOTES' | translate }}
              </p>

              <!-- Proposed Dates Count -->
              <p class="meta-label gregorian">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                  xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M17 21V19C17 17.9391 16.5786 16.9217 15.8284 16.1716C15.0783 15.4214 14.0609 15 13 15H5C3.93913 15 2.92172 15.4214 2.17157 16.1716C1.42143 16.9217 1 17.9391 1 19V21M23 21V19C22.9993 18.1137 22.7044 17.2528 22.1614 16.5523C21.6184 15.8519 20.8581 15.3516 20 15.13M16 3.13C16.8604 3.3503 17.623 3.8507 18.1676 4.55231C18.7122 5.25392 19.0078 6.11683 19.0078 7.005C19.0078 7.89317 18.7122 8.75608 18.1676 9.45769C17.623 10.1593 16.8604 10.6597 16 10.88M13 7C13 9.20914 11.2091 11 9 11C6.79086 11 5 9.20914 5 7C5 4.79086 6.79086 3 9 3C11.2091 3 13 4.79086 13 7Z"
                    stroke="#181D27" stroke-width="2" stroke-linecap="round"
                    stroke-linejoin="round" />
                </svg>
                {{ meeting.proposedDates }} {{ 'INVESTMENT_FUNDS.MEETING.PROPOSED_DATES' | translate }}
              </p>

            </div>
            <div class="status">
              <p class="status" [ngClass]="getStatusClass(meeting.statusId)">
                {{ meeting.status.localizedName}}
              </p>

            </div>
          </div>
        </div>
      </ng-container>
    </div>
  </div>
