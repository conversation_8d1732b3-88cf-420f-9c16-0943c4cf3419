@import "../../../../../assets/scss/variables";

.investment-funds {
  // padding: 1.5rem;
  background-color: var(--system-background);
  min-height: 100%;

  .funds-accordion {
    margin-top: 24px;

    ::ng-deep {
      .mat-expansion-panel {
        box-shadow: none;
        margin-bottom: 24px;
        border: 1px solid $border-color;

        .mat-expansion-panel-header {
          background: $light-bg;
          .h-48 {
            height: 48px;
          }
          .mat-expansion-panel-header-title {
            color: $navy-blue;
            font-size: 20px;
            font-weight: 600;
          }

          .mat-expansion-indicator::after {
            color: $navy-blue;
          }
        }

        .mat-expansion-panel-body {
          padding: 1rem;
        }
      }

      .mat-expansion-indicator::after {
        content: none;
      }
    }
  }

  .fund-card {
    cursor: pointer;
    background: $card_bg;
    border: 1px solid $border-color;
    border-radius: 16px;
    padding: 12px;
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
    justify-content: space-between;
    &.hover-shadow:hover {
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
    }
    .card-header {
      margin-bottom: 16px;
      position: relative;

      .status-section {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;

        .icon-wrapper {
          width: 40px;
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 56px;
          background: linear-gradient(180deg, #dde8ee 0%, #f7fafb 100%);
          padding: 4px;
          cursor: pointer;
          transition: all 0.2s ease;

          img {
            width: 16px;
            height: 16px;
            object-fit: contain;
          }

          &:hover {
            background: darken(#dde8ee, 5%);
          }
        }
      }

      .fund-status {
        display: flex;
        align-items: center;
        gap: 6px;
        color: $text-grey;
        font-size: 14px;
        font-weight: 400;
        padding: 4px 12px;
        border-radius: 100px;
        background: #e0e0e0;
      }

      .fund-name {
        color: $navy-blue;
        font-size: 20px;
        font-weight: 500;
        margin: 0;
      }
    }

    .card-body {
      flex-grow: 1;
      position: relative;

      .info-row {
        margin-bottom: 12px;

        .label {
          display: block;
          color: $text-grey;
          font-size: 16px;
          font-weight: 400;
          margin-bottom: 4px;
        }
        .hijri-value {
          color: $text-grey;
          font-size: 10px;
          font-weight: 400;
          margin-bottom: 4px;
        }

        .value {
          color: $navy-blue;
          font-size: 18px;
          font-weight: 500;
        }
      }
    }

    .card-footer {
      margin-top: auto;
      padding-top: 16px;
      display: flex;
      justify-content: center;

      .light-bg {
        border-radius: 56px;
        background: linear-gradient(180deg, #dde8ee 0%, #f7fafb 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s ease;
        padding: 4px;
        width: 40px;
        height: 40px;

        &:hover {
          background: darken(#dde8ee, 5%);
        }

        img {
          width: 16px;
          height: 16px;
        }
      }
    }
  }
}

.bill-icon-wrapper {
  position: relative;
  cursor: pointer;

  img {
    width: 24px;
    height: 24px;
  }

  .notification-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    min-width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: $notification-badg;
    color: $white;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 4px;
  }
}

.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 500;

  .dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin:0 6px;
  }

  &.status-green {
    background-color: #f1faf1;
    color: #27ae60;

    .dot {
      background-color: #27ae60;
    }
  }

  &.status-blue {
    background-color: #e5eefb;
    color: #2f80ed;

    .dot {
      background-color: #2f80ed;
    }
  }

  &.status-orange {
    background-color: #fdf1eb;
    color: #ff5f3d;

    .dot {
      background-color: #ff5f3d;
    }
  }

  &.status-grey {
    background-color: #e0e0e0;
    color: #828282;

    .dot {
      background-color: #828282;
    }
  }
}
.group-count-badge {
  background-color: #2656871f;
  padding: 4px 10px;
  border-radius: 16px;
  font-weight: bold;
  font-size: 14px;
  position: relative;
  display: flex;
  align-items: center;
  color: #000;
}

.red-dot {
  width: 8px;
  height: 8px;
  background-color: #d92d20;
  border-radius: 50%;
  margin-left: 6px;
  position: absolute;
  top: -3px;
  left: -3px;
}
