import { CommonModule } from '@angular/common';
import { Component, OnInit, OnDestroy, Output, EventEmitter } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { SizeEnum } from '@core/enums/size';
import { IBreadcrumbItem } from '@core/gl-interfaces/IBreadcrumbItem/ibreadcrumb-item';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { BreadcrumbComponent } from "@shared/components/breadcrumb/breadcrumb.component";
import { PageHeaderComponent } from "@shared/components/page-header/page-header.component";
import { MatDialog } from '@angular/material/dialog';
import { Subject, takeUntil } from 'rxjs';
import {
  MeetingsServiceProxy,
  MeetingsProposalListDto,
  MeetingsProposalListDtoPaginatedResult
} from '@core/api/api.generated';
import { TokenService } from 'src/app/features/auth/services/token.service';
import { GeorgianDatePipe } from '@shared/pipes/georgian-date/georgian-date.pipe';
import { MeetingsProposalFilterComponent } from '../meetings-proposal-filter/meetings-proposal-filter.component';


@Component({
  selector: 'app-proposal-list',
  standalone: true,
  imports: [BreadcrumbComponent, PageHeaderComponent, CommonModule, TranslateModule, GeorgianDatePipe],
  templateUrl: './proposal-list.component.html',
  styleUrl: './proposal-list.component.scss'
})
export class ProposalListComponent implements OnInit, OnDestroy {
  breadcrumbSizeEnum = SizeEnum;
  breadcrumbItems: IBreadcrumbItem[] = [];
  fundId = 0;

  // API Integration Properties
  MeetingsProposals: MeetingsProposalListDto[] = [];
  isLoading = false;
  hasError = false;
  errorMessage = '';
  currentPage = 1;
  pageSize = 10;
  totalCount = 0;
  search = '';
  statusId?: number;

  // Lifecycle management
  private destroy$ = new Subject<void>();
  isHasPermissionAdd: any;

  @Output() dataSelected = new EventEmitter<number>();

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    public translateService: TranslateService,
    private dialog: MatDialog,
    private meetingsServiceProxy: MeetingsServiceProxy,
    private TokenService: TokenService
  ) {}

  ngOnInit() {
    this.isHasPermissionAdd = this.TokenService.hasPermission('Meeting.Add');
    this.initializeBreadcrumbs();
    this.route.queryParams.subscribe((queryParams) => {
      this.fundId = +queryParams['fundId'] || 0;
      // Load meetings proposals after getting fundId
      this.loadMeetingsProposals();
    });
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Load meetings proposals from API
   */
  loadMeetingsProposals(): void {
    if (this.fundId <= 0) {
      this.hasError = true;
      this.errorMessage = 'INVESTMENT_FUNDS.MEETING.INVALID_FUND_ID';
      return;
    }

    this.isLoading = true;
    this.hasError = false;

    this.meetingsServiceProxy.getProposalsList(
      this.statusId,
      this.fundId,
      this.search || undefined,
      this.currentPage,
      this.pageSize,
      'id desc'
    )
    .pipe(takeUntil(this.destroy$))
    .subscribe({
      next: (response: MeetingsProposalListDtoPaginatedResult) => {
        this.isLoading = false;
        if (response.successed && response.data) {
          this.MeetingsProposals = response.data;
          this.totalCount = response.totalCount;
          this.dataSelected.emit(this.totalCount)
        } else {
          this.hasError = true;
          this.errorMessage = response.message || 'INVESTMENT_FUNDS.MEETING.LOAD_ERROR';
        }
      },
      error: (error) => {
        this.isLoading = false;
        this.hasError = true;
        this.errorMessage = 'INVESTMENT_FUNDS.MEETING.NETWORK_ERROR';
        console.error('Error loading meetings proposals:', error);
      }
    });
  }

    onBreadcrumbClicked(event: IBreadcrumbItem) {
    if (!event.disabled && event.url) {
      this.router.navigateByUrl(event.url);
    }
  }


    private initializeBreadcrumbs() {
    this.breadcrumbItems = [
     { label: 'BREADCRUMB.HOME', url: '/admin/dashboard' },
      { label: 'BREADCRUMB.FUNDS', url: '/admin/investment-funds' },
        {
        label: 'BREADCRUMB.FUND_DETAILS',
        url: `/admin/investment-funds/fund-details?id=${this.fundId}`,
      },
      { label: 'INVESTMENT_FUNDS.MEETING.TITLE', url: '', disabled: true },

    ];
  }
  /**
   * Handle search input
   */
  onSearch(searchTerm: string): void {
    this.search = searchTerm;
    this.currentPage = 1; // Reset to first page
    this.loadMeetingsProposals();
  }

  /**
   * Open advanced search/filter dialog
   */
  openFilter(): void {
    const dialogRef = this.dialog.open(MeetingsProposalFilterComponent, {
      width: '380px',
      data: {
        search: this.search,
        status: this.statusId,
        fromDate: '',
        toDate: '',
        createdBy: '',
      },
    });

    dialogRef.afterClosed().subscribe((result: any) => {
      if (result) {
        this.search = result.search || '';
        this.statusId = result.status;
        this.currentPage = 1; // Reset to first page
        this.loadMeetingsProposals();
      }
    });
  }
  /**
   * Navigate to add new meeting proposal
   */
  addNewProposalMeeting(): void {
    this.router.navigate(['/admin/investment-funds/meetings/add'], {
      queryParams: { fundId: this.fundId }
    });
  }

  /**
   * Navigate to meeting details view
   */
  viewMeetingDetails(meetingProposalId: number): void {
    if (!meetingProposalId) {
      console.error('Meeting proposal ID is required');
      return;
    }

    // Navigate to meeting details route with the proposal ID
    this.router.navigate(['/admin/investment-funds/meetings/details'], {
      queryParams: {
        id: meetingProposalId,
        fundId: this.fundId
      }
    });
  }

  /**
   * Get CSS class for meeting proposal status
   */
  getStatusClass(statusId: number): string {
    switch (statusId) {
      case 1:
        return 'draft';
      case 2:
        return 'pending';
      case 3:
        return 'active';
      case 4:
        return 'completed';
      case 5:
        return 'cancelled';
      default:
        return 'unknown';
    }
  }
}
