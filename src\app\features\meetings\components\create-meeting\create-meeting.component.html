<div class="create-resolution-page" >
  <!-- Breadcrumb Navigation -->
  <app-breadcrumb
    (onClickEvent)="onBreadcrumbClicked($event)"
    [breadcrumbs]="breadcrumbItems"
    [size]="breadcrumbSizeEnum.Medium"
    divider=">">
  </app-breadcrumb>

  <!-- Page Header -->
  <div class="mt-3">
    <app-page-header [title]="'MEETINGS.ADD_SCHEDULED_MEETING' | translate" >
    </app-page-header>
  </div>

  <div class="row">
    <div class="col-12 col-lg-8">
      <!-- Form Container -->
      <div class="form-container mt-3">
          <!-- (dropdownChanged)="dropdownChanged($event)" -->
        <app-form-builder
        (radioButtonValueChanged)="meetingPlaceChanged($event)"
        [formControls]="formControls" [formGroup]="formGroup" [isFormSubmitted]="isValidationFire"
          (dateSelected)="dateSelected($event)" (fileUploaded)="onFileUpload($event)" (valueChanged)="onValueChange($event, $event.control)">
          <p slot="top" class="header mt-2"> {{'INVESTMENT_FUNDS.RESOLUTIONS.BASIC_INFO' | translate}}</p>
            <div slot="between" class="hr-first-container">
                <hr class="hr-first"/>
            </div>
            <div slot="between">
                <div class="form-section mb-3">
            <div class="section-header">
                <div class="header-content">
                <div class="d-flex align-items-center gap-2">
                    <h6>{{ 'INVESTMENT_FUNDS.RESOLUTIONS.RESOLUTION_ITEMS' | translate }}</h6><span class="items-num">345 {{ 'INVESTMENT_FUNDS.RESOLUTIONS.ITEMS' | translate }}</span>
                </div>
                <!-- *ngIf="resolutionItems.length" -->
                <!-- [disabled]="isSubmitting" -->
                <button
                        type="button"
                        class="btn add-item-btn"
                        >
                    <!-- <i class="fas fa-plus"></i> -->
    
                    <svg width="17" height="18" viewBox="0 0 17 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M13.0855 0.5H3.91447C2.8772 0.502951 1.88325 0.916315 1.14978 1.64978C0.416315 2.38325 0.00295102 3.3772 0 4.41447L0 13.5855C0.00295102 14.6228 0.416315 15.6167 1.14978 16.3502C1.88325 17.0837 2.8772 17.497 3.91447 17.5H13.0855C14.1228 17.497 15.1167 17.0837 15.8502 16.3502C16.5837 15.6167 16.997 14.6228 17 13.5855V4.41447C16.997 3.3772 16.5837 2.38325 15.8502 1.64978C15.1167 0.916315 14.1228 0.502951 13.0855 0.5ZM11.9906 9.73257H9.41039C9.39052 9.73242 9.37082 9.73622 9.35244 9.74376C9.33405 9.75129 9.31735 9.76241 9.3033 9.77646C9.28925 9.79051 9.27813 9.80721 9.2706 9.8256C9.26306 9.84398 9.25926 9.86368 9.25941 9.88355V12.4671C9.25941 12.6685 9.1794 12.8617 9.03698 13.0041C8.89457 13.1465 8.70141 13.2265 8.5 13.2265C8.29859 13.2265 8.10543 13.1465 7.96302 13.0041C7.8206 12.8617 7.74059 12.6685 7.74059 12.4671V9.88355C7.74074 9.86368 7.73694 9.84398 7.7294 9.8256C7.72187 9.80721 7.71075 9.79051 7.6967 9.77646C7.68265 9.76241 7.66595 9.75129 7.64756 9.74376C7.62918 9.73622 7.60947 9.73242 7.58961 9.73257H5.00941C4.90968 9.73257 4.81093 9.71292 4.71879 9.67476C4.62666 9.6366 4.54294 9.58066 4.47243 9.51014C4.40191 9.43962 4.34597 9.35591 4.30781 9.26377C4.26964 9.17163 4.25 9.07288 4.25 8.97316C4.25 8.87343 4.26964 8.77468 4.30781 8.68255C4.34597 8.59041 4.40191 8.50669 4.47243 8.43618C4.54294 8.36566 4.62666 8.30972 4.71879 8.27156C4.81093 8.23339 4.90968 8.21375 5.00941 8.21375H7.58961C7.60947 8.2139 7.62918 8.21009 7.64756 8.20256C7.66595 8.19502 7.68265 8.18391 7.6967 8.16986C7.71075 8.15581 7.72187 8.1391 7.7294 8.12072C7.73694 8.10233 7.74074 8.08263 7.74059 8.06276V5.48257C7.74059 5.28116 7.8206 5.088 7.96302 4.94558C8.10543 4.80317 8.29859 4.72316 8.5 4.72316C8.70141 4.72316 8.89457 4.80317 9.03698 4.94558C9.1794 5.088 9.25941 5.28116 9.25941 5.48257V8.06276C9.25881 8.08291 9.26227 8.10297 9.26961 8.12175C9.27694 8.14053 9.28798 8.15763 9.30208 8.17204C9.31617 8.18645 9.33303 8.19787 9.35164 8.20561C9.37026 8.21336 9.39024 8.21726 9.41039 8.2171H11.9906C12.192 8.2171 12.3852 8.29711 12.5276 8.43953C12.67 8.58195 12.75 8.77511 12.75 8.97651C12.75 9.17792 12.67 9.37108 12.5276 9.5135C12.3852 9.65591 12.192 9.73592 11.9906 9.73592V9.73257Z" fill="#00205A"/>
                    </svg>
                    {{ 'INVESTMENT_FUNDS.RESOLUTIONS.ADD_ITEM' | translate }}
                </button>
                </div>
            </div>
            </div>
            </div>
           <p slot="bottom" class="header mt-2">{{'INVESTMENT_FUNDS.RESOLUTIONS.ATTACHMENTS' | translate}}</p>
            <div slot="between" class="hr-last-container">
                <hr class="hr-last"/>
            </div>
    
        </app-form-builder>
    
        <!-- Action Buttons -->
        <div class="actions">
          <app-custom-button
            [btnName]="'COMMON.CANCEL' | translate"
            (click)="onCancel()"
            [buttonType]="buttonEnum.Secondary"
            [iconName]="iconEnum.cancel">
          </app-custom-button>
    
          <app-custom-button
            [btnName]="'RESOLUTIONS.SAVE_AS_DRAFT' | translate"
            [buttonType]="buttonEnum.OutLine"
            [iconName]="iconEnum.draft"
            (click)="onSubmit(true)">
          </app-custom-button>
    
          <app-custom-button
            [btnName]="'RESOLUTIONS.SEND' | translate"
            [buttonType]="buttonEnum.Primary"
            [iconName]="iconEnum.verify"
            (click)="onSubmit(false)">
          </app-custom-button>
        </div>
      </div>
    </div>
    <div class="col-12 col-lg-4">
        <div class="members-section mt-3">
      <p class="title m-0">
        {{ 'RESOLUTIONS.VOTING.MEMBERS' | translate }}
        <span class="member-count">987 {{ 'RESOLUTIONS.VOTING.ONE_MEMBER' | translate }}</span>
      </p>
      <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="2" viewBox="0 0 278 2" fill="none">
        <path d="M0 1H278" stroke="url(#paint0_linear_15125_99505)"/>
        <defs>
        <linearGradient id="paint0_linear_15125_99505" x1="0" y1="1" x2="275.318" y2="1" gradientUnits="userSpaceOnUse">
        <stop stop-color="#E0E1E2" stop-opacity="0"/>
        <stop offset="0.5" stop-color="#E0E1E2"/>
        <stop offset="1" stop-color="#E0E1E2" stop-opacity="0.15625"/>
        </linearGradient>
        </defs>
    </svg>
      <!-- Member Cards -->
      <div class="members-list">
        <!-- Member Card 1 - موافق -->
        <div class="member-card">
          <div class="member-info">
            <div class="member-avatar">
              <img src="assets/images/avatar-user.png" alt="Member Avatar" class="avatar-image">
            </div>
            <div class="member-details">
              <h4 class="member-name">dvsv</h4>
              <p class="member-role">sdfs</p>
            </div>
          </div>
          <div class="voting-status">
            <div class="status-badge" >
                <span class="dot"></span>
                dfgdg
            </div>
            <button
             class="action-link">
              {{ 'RESOLUTIONS.VOTING.SEND_REMINDER' | translate }}</button>
            <button
              class="action-link">
            {{ 'RESOLUTIONS.VOTING.REVOTE_REQUEST' | translate }}</button>
          </div>
        </div>
      </div>
      <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="2" viewBox="0 0 278 2" fill="none">
        <path d="M0 1H278" stroke="url(#paint0_linear_15125_99505)"/>
        <defs>
        <linearGradient id="paint0_linear_15125_99505" x1="0" y1="1" x2="275.318" y2="1" gradientUnits="userSpaceOnUse">
        <stop stop-color="#E0E1E2" stop-opacity="0"/>
        <stop offset="0.5" stop-color="#E0E1E2"/>
        <stop offset="1" stop-color="#E0E1E2" stop-opacity="0.15625"/>
        </linearGradient>
        </defs>
        </svg>
      <!-- Voting Summary -->
      <!-- <div class="voting-summary">
        <div class="summary-card d-flex flex-column approved-summary">
          <div class="summary-label">{{"RESOLUTIONS.VOTING.APPROVED" | translate}}</div>
          <div class="summary-count">45</div>
        </div>
        <div class="summary-card d-flex flex-column rejected-summary">
          <div class="summary-label">{{"RESOLUTIONS.VOTING.REJECTED" | translate}}</div>
          <div class="summary-count">{{votingMembers?.rejectedMembersCount || 0}}</div>
        </div>
        <div class="summary-card d-flex flex-column not-voted-summary">
          <div class="summary-label">{{"RESOLUTIONS.VOTING.NOT_VOTED" | translate}}</div>
          <div class="summary-count">{{votingMembers?.pendingMembersCount || 0}}</div>
        </div>
      </div> -->
    </div>
    </div>
  </div>
</div>
