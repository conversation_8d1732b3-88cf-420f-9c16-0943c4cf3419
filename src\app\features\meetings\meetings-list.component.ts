import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { IconEnum } from '@core/enums/icon-enum';
import { LanguageEnum } from '@core/enums/language-enum/language-enum';
import { SizeEnum } from '@core/enums/size';
import { IBreadcrumbItem } from '@core/gl-interfaces/IBreadcrumbItem/ibreadcrumb-item';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';
import { CustomButtonComponent } from '@shared/components/custom-button/custom-button.component';
import { PageHeaderComponent } from '@shared/components/page-header/page-header.component';
import { MeetingStatusEnum } from '@shared/enum/meeting-enums';
import { DateHijriConverterPipe } from '@shared/pipes/dateHijriConverter/dateHijriConverter.pipe';
import { MeetingFilterPopupComponent } from './components/meeting-filter-popup/meeting-filter-popup.component';

@Component({
  selector: 'app-meetings-list',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    BreadcrumbComponent,
    PageHeaderComponent,
    DateHijriConverterPipe,
    CustomButtonComponent,
  ],
  templateUrl: './meetings-list.component.html',
  styleUrl: './meetings-list.component.scss'
})
export class MeetingsListComponent {
  // Breadcrumb configuration
  breadcrumbItems: IBreadcrumbItem[] = [
    { label: 'BREADCRUMB.HOME', url: '/admin/dashboard' },
    { label: 'BREADCRUMB.FUNDS', url: '/admin/investment-funds' },
    { label: 'RESOLUTIONS.TITLE', url: '' },
  ];
  breadcrumbSizeEnum = SizeEnum;
  createButtonIcon = IconEnum;
  meetingStatus = MeetingStatusEnum

  isLoading = false;
  hasError = false;
  meetings: any[] = [];
  totalCount: number = 0;
  isHasPermissionAdd: boolean = true;
  filter: any;


  currentPage = 1;
  pageSize = 10;
  totalPages = 0;
  
  constructor(
    private router: Router,
    public translateService: TranslateService,
    private dialog: MatDialog
  ) { }


  onBreadcrumbClicked(item: IBreadcrumbItem): void {
    if (item.url && !item.disabled) {
      this.router.navigateByUrl(item.url);
    }
  }

  canGoPrevious(): boolean {
    return this.currentPage > 1;
  }

  canGoNext(): boolean {
    return this.currentPage < this.totalPages;
  }

  onPageChange(page: number): void {
    if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
      console.log(
        `Pagination: Changing from page ${this.currentPage} to page ${page}`
      );
      this.currentPage = page;
      console.log(
        `Pagination: Will send pageNo=${
          this.currentPage - 1
        } to API (0-based indexing)`
      );
      // this.reloadCurrentData();
    }
  }

  onPreviousPage(): void {
    if (this.canGoPrevious()) {
      this.onPageChange(this.currentPage - 1);
    }
  }

  onNextPage(): void {
    if (this.canGoNext()) {
      this.onPageChange(this.currentPage + 1);
    }
  }

  getPageNumbers(): number[] {
    const pages: number[] = [];
    const maxPagesToShow = 5;
    const halfRange = Math.floor(maxPagesToShow / 2);

    let startPage = Math.max(1, this.currentPage - halfRange);
    let endPage = Math.min(this.totalPages, this.currentPage + halfRange);

    // Adjust if we're near the beginning or end
    if (endPage - startPage + 1 < maxPagesToShow) {
      if (startPage === 1) {
        endPage = Math.min(this.totalPages, startPage + maxPagesToShow - 1);
      } else {
        startPage = Math.max(1, endPage - maxPagesToShow + 1);
      }
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    return pages;
  }

  isEnglish(): boolean {
    return this.translateService.currentLang === LanguageEnum.en;
  }

  onSearch($event: string) {}

  openFilter(): void {
    const dialogRef = this.dialog.open(MeetingFilterPopupComponent, {
      width: '390px',
      height: '599px',
      data: this.filter,
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        // this.getFunds(result);
        this.filter = result;
      }
    });
  }

  addMeeting() {}

  viewMeetingDetails(meeting: any) {}

  editMeeting(meeting: any) {}

  cancelMeeting(meeting: any) {}

  getStatusClass(statusId: number): string {
    switch (statusId) {
      case this.meetingStatus.NotStartedYet:
        return 'pending';
      case this.meetingStatus.InProgress:
        return 'meeting-inProgress';
      case this.meetingStatus.Finished:
        return 'finished';
      case this.meetingStatus.Cancelled:
        return 'cancelled';
      default:
        return '';
    }
  }
}
