<div class="meetings-page">
  <!-- Breadcrumb -->
  <!-- <div class="breadcrumb-section">
    <app-breadcrumb (onClickEvent)="onBreadcrumbClicked($event)" [breadcrumbs]="breadcrumbItems"
      [size]="breadcrumbSizeEnum.Medium" divider=">">
    </app-breadcrumb>
  </div> -->
  <!-- Page Header -->
  <div class="page-header-section">
    <app-page-header [showSearch]="true" [showFilter]="true"
      [showCreateButton]="true"
      [searchPlaceholder]="translateService.instant('MEETINGS.SEARCH_PLACEHOLDER')"
      [restrictSearchToNumbers]="true" createButtonText="MEETINGS.ADD" (search)="onSearch($event)"
      (filter)="openFilter()" (create)="addMeeting()">
    </app-page-header>
  </div>

  <!-- Main Content -->
  <div class="content-section">
    <!-- Resolutions Grid -->
     <!-- *ngIf="!isLoading && !hasError" -->
    <div class="meetings-grid">
      <!-- Resolution Card -->
      <div class="meeting-card">
        <!-- *ngIf="meeting.canView || meeting.canVote || meeting.canViewHisVote" -->
        <ng-container>
          <div class="card-header">
            <div class="status">
              <span class="status" [ngClass]="getStatusClass(1)">
                لم يبدأ بعد
              </span>
              <!-- <span class="meta-value status" [ngClass]="'status-' +(resolution.statusDisplay ? resolution.statusDisplay.toLowerCase() : '')">
              </span> -->
            </div>
            <div class="card-actions">
              <!-- *ngIf="meeting.canView" -->
              <button class="action-btn details-btn" (click)="viewMeetingDetails($event)"
                [title]="'COMMON.VIEW_DETAILS' | translate">
                <img src="assets/images/file-text.svg" alt="details" />
              </button>
              <!-- *ngIf="meeting.canView" -->
              <button class="action-btn details-btn" (click)="viewMeetingDetails($event)"
                [title]="'COMMON.VIEW_DETAILS' | translate">
                <img src="assets/images/eye.png" alt="details" />
              </button>
              <!-- *ngIf="meeting?.canEdit" -->
              <button class="action-btn" (click)="editMeeting($event)" [title]="'COMMON.EDIT' | translate">
                <img src="assets/images/edit.png" alt="edit" />
              </button>
              <!-- *ngIf="meeting.canCancel" -->
              <button class="action-btn" (click)="cancelMeeting($event)" [title]="'COMMON.CANCEL' | translate">
                <img src="assets/images/x.png" class="mx-2" alt="">
              </button>
            </div>
          </div>
          <div class="card-content">
            <h2 class="meeting-title mt-lg-3">
              اجتماع لجنة الاستثمار
            </h2>
            <p class="title">{{ 'MEETINGS.PLACE' | translate }}</p>
            <p class="meeting-type">قاعة الاجتماعات الفرعية - الطابق الثامن</p>
            
            <p class="title">{{ 'MEETINGS.PARTNERS' | translate }}</p>
            <p class="meeting-type">4 {{'MEETINGS.PARTNER' | translate}}</p>

            <p class="title">{{ 'MEETINGS.DESCRIPTION' | translate }}</p>
            <!-- <p class="meeting-description">{{ resolution.description }}</p> -->
            <p class="meeting-description description-text"> مناقشة الفرص الاستثمارية الجديدة في السوق المحلي </p>

            <div class="meeting-meta">
              <div class="meta-item">
                <span class="meta-label">{{ 'MEETINGS.DATE' | translate }}:</span>
                <!-- {{(meeting.lastUpdated ? meeting.lastUpdated.toString() : '') | date:
                  'd/M/y'}} -->
                <p class="gregorian mb-0">08 / 8 / 2025</p>
                <p class="hijri title">14 Safar 1447</p>
              </div>
            </div>
          </div>
        </ng-container>
      </div>
    </div>

    <!-- Pagination Controls -->


    <div *ngIf="totalCount == 0 || hasError && !isLoading"
      class="d-flex  flex-column gap-4 justify-content-center my-5 align-items-center">
      <img src="assets/images/nodata.png" width="350">

      <ng-container>
        <p class="text-center header fs-20">{{'MEETINGS.NO_DATA' | translate}}</p>
        <app-custom-button *ngIf="isHasPermissionAdd" [btnName]="'MEETINGS.CREATE_FIRST' | translate"
          class="w-auto"
          [iconName]="createButtonIcon.plus" (click)="addMeeting()">
        </app-custom-button>
      </ng-container>
    </div>

    <div class="pagination-section" *ngIf="totalCount > 0">
      <!-- Pagination Controls -->
      <div class="pagination-controls">
        <!-- Previous Page Button -->
        <button class="pagination-btn prev-btn" [disabled]="!canGoPrevious()" (click)="onPreviousPage()"
          [title]="'PAGINATION.PREVIOUS' | translate">
          <!-- <i class="fas fa-angle-left"></i> -->
          <img [src]="isEnglish() ? 'assets/images/arrow-left2.png' : 'assets/images/arrow-right.png'" class="mx-2"
            alt="previous">

          <span class="btn-text">{{ 'PAGINATION.PREVIOUS' | translate }}</span>
        </button>

        <!-- Page Numbers -->
        <div class="page-numbers">
          <button *ngFor="let page of getPageNumbers()" class="pagination-btn page-number-btn"
            [class.active]="page === currentPage" (click)="onPageChange(page)">
            {{page}}
          </button>
        </div>

        <!-- Next Page Button -->
        <button class="pagination-btn next-btn" [disabled]="!canGoNext()" (click)="onNextPage()"
          [title]="'PAGINATION.NEXT' | translate">
          <span class="btn-text">{{ 'PAGINATION.NEXT' | translate }}</span>
          <!-- <i class="fas fa-angle-right"></i> -->
          <img [src]="isEnglish() ? 'assets/images/arrow-right.png' : 'assets/images/arrow-left2.png'" class="mx-2"
            alt="next">
        </button>
      </div>
    </div>
  </div>
</div>