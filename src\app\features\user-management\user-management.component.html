<div class="user-management-container">
  <!-- Breadcrumb -->
    <app-breadcrumb [breadcrumbs]="breadcrumbItems"
    [size]="breadcrumbSizeEnum.Medium"  (onClickEvent)="onBreadcrumbClicked($event)" divider=">">
  </app-breadcrumb>

  <!-- Page Header -->
  <app-page-header
    #pageHeader
    [title]="'USER_MANAGEMENT.TITLE' | translate"
    [showCreateButton]="true"
    [searchValue]="mobileSearchTerm"
    [showSearch]="true"
    [showFilter]="true"
    [createButtonText]="'USER_MANAGEMENT.ADD_USER' | translate"
    [searchPlaceholder]="'USER_MANAGEMENT.FILTERS.MOBILE_PLACEHOLDER' | translate"
    (create)="onAddUser()"
    (search)="onSearch($event)"
    (filter)="openAdvancedFilters()">
  </app-page-header>


  <div class="table-container" *ngIf="users && users.length > 0">
    <app-table
      [columns]="columns"
      [displayedColumns]="displayedColumns"
      [dataSource]="dataSource"
      [selection]="selection"
      [sortingType]="sortingType"
      [paginationType]="paginationType"
      [totalItems]="totalItems"
      [pageSize]="pageSize"
      (onClickAction)="onTableAction($event)"
      (switchToggleEvent)="onSwitchToggle($event)"
      (sortChanged)="onSortChanged($event)"
      (pageChange)="onPageChanged($event)">
    </app-table>
  </div>

  <!-- Empty State -->
  <div *ngIf="!isLoading && users.length === 0 && !hasActiveFilters()"
        class="d-flex  flex-column gap-4 justify-content-center mt-5 align-items-center">
        <img src="assets/images/nodata.png" width="350" alt="No Data" />
        <ng-container >
            <p  class="text-center mt-3 header fs-20">{{'INVESTMENT_FUNDS.NO_DATA' | translate}}</p>
        </ng-container>
    </div>

  <!-- No Results State (when filters applied) -->
  <div *ngIf="!isLoading && users.length === 0 && hasActiveFilters()"
       class="no-results-state text-center py-5">
    <div class="no-results-content  d-flex justify-content-center align-items-center flex-column">
      <!-- <i class="fas fa-search fa-3x text-muted mb-3"></i> -->
      <h3>{{ 'USER_MANAGEMENT.NO_RESULTS' | translate }}</h3>
      <p class="text-muted">{{ 'USER_MANAGEMENT.NO_RESULTS_MESSAGE' | translate }}</p>
      <button
        type="button"
        class="btn btn-primary"
        (click)="clearAllFilters()">
        {{ 'USER_MANAGEMENT.FILTERS.CLEAR_FILTERS' | translate }}
      </button>
    </div>
  </div>
</div>
