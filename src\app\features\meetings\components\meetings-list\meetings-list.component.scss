// @import "../../../../../assets/scss/variables";

.meetings-page {
  // padding: 24px;
  // background-color: #f8f9fa;
  min-height: 100vh;
  // direction: rtl;

  .breadcrumb-section {
    margin-bottom: 20px;
  }

  .page-header-section {
    margin-bottom: 32px;
  }

  .content-section {
    padding: 0;

    .search-filters-section {
      // background: white;
      border-radius: 12px;
      padding: 20px;
      margin-bottom: 20px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

      .search-container {
        display: flex;
        align-items: center;
        gap: 16px;
        flex-wrap: wrap;

        .search-field {
          flex: 1;
          min-width: 300px;
          position: relative;

          .search-input {
            width: 100%;
            height: 56px;
            padding: 16px 48px 16px 16px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            font-size: 14px;
            background: white;
            transition: border-color 0.2s ease;

            &:focus {
              outline: none;
              border-color: #00205a;
              box-shadow: 0 0 0 2px rgba(0, 32, 90, 0.1);
            }

            &::placeholder {
              color: #999;
            }
          }

          .search-icon {
            position: absolute;
            right: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
            font-size: 16px;
          }
        }

        .filter-button {
          height: 56px;
          padding: 0 20px;
          border: 1px solid #e0e0e0;
          background: white;
          color: #666;
          border-radius: 8px;
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 14px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
            background-color: #f5f5f5;
            border-color: #ccc;
          }

          i {
            font-size: 16px;
          }
        }
      }
    }

    .meetings-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
      gap: 20px;
      margin-bottom: 24px;

      .meeting-card {
        // background: white;
        border-radius: 12px;
        border: 1px solid #e0e0e0;
        overflow: hidden;
        transition: all 0.2s ease;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);

        &:hover {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          transform: translateY(-2px);
        }

        .card-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 12px 10px 0;
          // background: #f8f9fa;
          // border-bottom: 1px solid #e0e0e0;
          .meeting-type {
            color: #00205a;
            font-size: 14px;
            font-weight: 500;
            line-height: 20px;
            margin-bottom: 0;
            .title {
              color: #828282;
              font-size: 12px;
              font-weight: 400;
              line-height: 16px;
              margin-bottom: 10px;
            }
          }

          .meeting-number {
            font-size: 18px;
            font-weight: 700;
            color: #00205a;
            background: white;
            padding: 8px 16px;
            border-radius: 6px;
            border: 1px solid #e0e0e0;
          }

          .card-actions {
            display: flex;
            gap: 8px;

            .action-btn {
              width: 36px;
              height: 36px;
              border: none;
              border-radius: 6px;
              display: flex;
              align-items: center;
              justify-content: center;
              cursor: pointer;
              transition: all 0.2s ease;
              background-color: transparent;
              border: 1px solid transparent;

              i {
                font-size: 14px;
              }

              &.edit-btn {
                background: #fff3cd;
                color: #856404;

                &:hover {
                  background: #ffeaa7;
                }
              }

              &.delete-btn {
                background: #f8d7da;
                color: #721c24;

                &:hover {
                  background: #f5c6cb;
                }
              }
            }
          }

          .status {
              display: flex;
              align-items: center;
              gap: 8px;
              height: 24px;
              border-radius: 20px;
              padding: 10px;

              font-size: 14px;
              font-weight: 400;
              line-height: 16px;
              &.draft {
                background: rgba(117, 85, 172, 0.1);
                color: #7555ac;
              }
              &.pending {
                background: #fff3cd;
                color: #856404;
              }
              &.completing-data {
                background: rgba(157, 112, 9, 0.27);
                color: #9d7009;
              }
              &.waiting-for-confirmation {
                background: rgba(226, 180, 138, 0.34);
                color: #d16440;
              }
              &.confirmed {
                background: rgba(97, 253, 97, 0.14);

                color: #27ae60;
              }

              &.rejected {
                color: #828282;
                background: #eaeef1;
              }

              &.meeting-inProgress {
                background: rgba(47, 128, 237, 0.1);
                color: #2f80ed;
              }

              &.finished {
                background: #f1faf1;
                color: #0e700e;
              }

              &.cancelled {
                background: rgba(197, 15, 31, 0.1);

                color: #c50f1f;
              }

              &.cancelled {
                background: var(--Color---Grey-5, #e0e0e0);

                color: #4f4f4f;
              }
            }
        }

        .card-content {
          padding: 0 10px 12px 10px;

          .meeting-title{
            color: #00205a;
            font-size: 20px;
            font-weight: 700;
            line-height: 20px;
            margin-bottom: 10px;
          }
          .meeting-type {
            color: #00205a;
            font-size: 14px;
            font-weight: 500;
            line-height: 20px;
            margin-bottom: 8px;

            .title {
              color: #828282;
              font-size: 12px;
              font-weight: 400;
              line-height: 16px;
              margin-bottom: 4px;
            }
          }

          .meeting-description {
            color: #333;
            font-size: 14px;
            font-weight: 500;
            line-height: 21px; /* 150% */
            margin-bottom: 8px;
          }
          .description-text {
            max-width: 100%; // or set a fixed width like 250px
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            display: block;
          }

          .title {
            color: #828282;
            font-size: 12px;
            font-weight: 400;
            line-height: 16px;
            margin-bottom: 4px;
          }

          .meeting-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            .meta-item {
              margin-bottom: 8px;
              font-size: 13px;

              .meta-label {
                color: #828282;
                font-size: 12px;
                font-weight: 400;
                line-height: 16px;
                margin-bottom: 4px;
              }

              .gregorian {
                color: #00205a;
                font-size: 14px;
                font-weight: 400;
                line-height: 16px;
              }

              .meta-value {
                color: #333;
                font-weight: 600;
              }
            }
          }
        }

        .card-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 16px 20px;
          // background: #f8f9fa;
          // border-top: 1px solid #e0e0e0;

          .footer-info {
            display: flex;
            flex-direction: column;
            gap: 4px;
            font-size: 12px;
            color: #666;

            .creation-date {
              font-weight: 500;
            }

            .creator {
              color: #999;
            }
          }

          .indicator {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;

            &.indicator-approved {
              background: #28a745;
              color: white;
            }

            &.indicator-pending {
              background: #ffc107;
              color: #212529;
            }

            &.indicator-draft {
              background: #6c757d;
              color: white;
            }

            &.indicator-rejected {
              background: #dc3545;
              color: white;
            }

            &.indicator-voting-in-progress {
              background: #6f42c1;
              color: white;
            }

            &.indicator-not-approved {
              background: #fd7e14;
              color: white;
            }

            &.indicator-under-review {
              background: #17a2b8;
              color: white;
            }

            &.indicator-cancelled {
              background: #6c757d;
              color: white;
            }

            &.indicator-expired {
              background: #e83e8c;
              color: white;
            }

            &.indicator-archived {
              background: #495057;
              color: white;
            }
          }
        }
      }
    }

    .empty-state {
      background: white;
      border-radius: 12px;
      padding: 60px 20px;
      text-align: center;
      border: 2px dashed #e0e0e0;

      .empty-state-content {
        max-width: 400px;
        margin: 0 auto;
        align-items: center;
    justify-content: center;
    display: flex;
    flex-direction: column;

        .empty-icon {
          font-size: 64px;
          color: #ccc;
          margin-bottom: 24px;
        }

        h3 {
          font-size: 20px;
          font-weight: 600;
          color: #333;
          margin: 0 0 12px 0;
        }

        p {
          font-size: 14px;
          color: #666;
          margin: 0 0 24px 0;
          line-height: 1.5;
        }

        .btn {
          padding: 12px 24px;
          font-size: 14px;
          font-weight: 600;
          border-radius: 8px;
          border: none;
          cursor: pointer;
          transition: all 0.2s ease;

          &.btn-primary {
            background: #00205a;
            color: white;

            &:hover {
              background: #001a4d;
            }
          }
        }
      }
    }
  }
  // Responsive Design
  @media (max-width: 768px) {
    .content-section {
      // padding: 0 16px 16px;

      .search-filters-section {
        padding: 16px;
        border-radius: 8px;
        margin-bottom: 16px;

        .search-container {
          flex-direction: column;
          gap: 12px;

          .search-field {
            min-width: 100%;
          }

          .filter-button {
            width: 100%;
            height: 48px;
            justify-content: center;
          }
        }
      }

      .meetings-grid {
        grid-template-columns: 1fr;
        gap: 16px;

        .meeting-card {
          .card-header {
            padding: 12px 16px;

            .meeting-number {
              font-size: 16px;
              padding: 6px 12px;
            }
          }

          .card-content {
            padding: 16px;

            .meeting-description {
              font-size: 13px;
            }
          }

          .card-footer {
            padding: 12px 16px;
          }
        }
      }

      // Loading, Error, and Empty States
      .loading-state,
      .error-state,
      .empty-state {
        text-align: center;
        padding: 80px 20px;
        background: white;
        border-radius: 12px;
        border: 1px solid #e0e0e0;

        .loading-content,
        .error-content,
        .empty-state-content {
          max-width: 400px;
          margin: 0 auto;

          .loading-icon {
            font-size: 48px;
            color: #00205a;
            margin-bottom: 24px;
          }

          .error-icon {
            font-size: 64px;
            color: #dc3545;
            margin-bottom: 24px;
          }

          .empty-icon {
            font-size: 64px;
            color: #ccc;
            margin-bottom: 24px;
          }

          h3 {
            font-size: 24px;
            color: #333;
            margin-bottom: 16px;
            font-weight: 600;
          }

          p {
            font-size: 16px;
            color: #666;
            margin-bottom: 32px;
            line-height: 1.5;
          }

          .btn-primary,
          .retry-btn {
            background-color: #00205a;
            border-color: #00205a;
            padding: 12px 32px;
            font-size: 16px;
            font-weight: 600;
            border-radius: 8px;
            transition: all 0.2s ease;
            border: none;
            color: white;
            cursor: pointer;

            &:hover {
              background-color: #001a4d;
              border-color: #001a4d;
              transform: translateY(-1px);
            }
          }
        }
      }
    }
  }

  // Pagination Styles
  .pagination-section {
    margin-top: 32px;
    padding: 24px;
    // background: white;
    border-radius: 12px;
    // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

    .pagination-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      flex-wrap: wrap;
      gap: 16px;

      .records-info {
        color: #666;
        font-size: 14px;
        font-weight: 500;
      }

      .page-size-selector {
        display: flex;
        align-items: center;
        gap: 8px;
        color: #666;
        font-size: 14px;

        label {
          margin: 0;
          font-weight: 500;
        }

        .page-size-select {
          padding: 6px 12px;
          border: 1px solid #ddd;
          border-radius: 6px;
          background: white;
          color: #333;
          font-size: 14px;
          cursor: pointer;
          transition: border-color 0.2s ease;

          &:hover {
            border-color: #007bff;
          }

          &:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
          }
        }
      }

      @media (max-width: 768px) {
        flex-direction: column;
        align-items: flex-start;
      }
    }

    .pagination-controls {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 8px;
      margin-bottom: 16px;
      flex-wrap: wrap;

      .pagination-btn {
        display: flex;
        align-items: center;
        gap: 6px;
        padding: 8px 12px;
        border: 1px solid #ddd;
        background: white;
        color: #333;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.2s ease;
        font-size: 14px;
        font-weight: 500;
        min-width: 40px;
        justify-content: center;
        background: transparent;
        border: transparent;

        &:hover:not(:disabled) {
          //  background: #f8f9fa;
          border-color: #007bff;
          color: #007bff;
        }

        &:disabled {
          // background: #f8f9fa;
          color: #6c757d;
          cursor: not-allowed;
          opacity: 0.6;
        }

        &.active {
          background: #ebf3fc;
          color: white;
          border-color: #007bff;
          border-radius: 10px;
        }

        .btn-text {
          @media (max-width: 576px) {
            display: none;
          }
        }

        i {
          font-size: 12px;
        }
      }

      .page-numbers {
        display: flex;
        gap: 4px;
        margin: 0 8px;

        .page-number-btn {
          min-width: 44px;
          min-height: 44px;
          padding: 8px 4px;
          color: #00205a;
        }
      }

      @media (max-width: 576px) {
        gap: 4px;

        .pagination-btn {
          padding: 6px 8px;
          font-size: 12px;
        }
      }
    }

    .page-info {
      text-align: center;
      color: #666;
      font-size: 14px;
      font-weight: 500;
    }
  }
}
