<div class="document-categories">
  <app-breadcrumb  [breadcrumbs]="breadcrumbItems"
    [size]="breadcrumbSizeEnum.Medium" divider=">"></app-breadcrumb>
  <div class="mb-5">
    <app-page-header
      [showCreateButton]="tokenService.hasPermission('DocumentCategory.Create')"
      createButtonText="Document_Categories.ADD_Document_Categories"
      (create)="onCreateNewCategory()" [showSearch]="false" [showFilter]="false"></app-page-header>
  </div>

  <div class="table-container"  *ngIf="tableDataSource">

    <app-table [columns]="tableColumns"
      [displayedColumns]="displayedColumns"
      [dataSource]="tableDataSource"
      (onClickAction)="onClickAction($event)"
      (switchToggleEvent)="onSwitchToggle($event)"
      (textLinkClick)="onTextLinkClick($event)"
      (toggleAllRows)="toggleAllRows()"
      (toggleRow)="toggleRow($event)"
      [sortingType]="sortingType"
      (sortChanged)="onSortChanged($event)"
      [paginationType]=""
      (pageChange)="onPageChange($event)"
      [totalItems]="totalCount"></app-table>
  </div>
