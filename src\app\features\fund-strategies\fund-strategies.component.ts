import { Component, OnInit } from '@angular/core';
import { ColumnTypeEnum, DataHandlingType } from '@core/enums/column-type';
import {
  ActionDisplayMode,
  ITableColumn,
  SwitchToggleEvent,
  TableActionEvent,
  TextLinkClickEvent,
} from '@core/gl-interfaces/I-table/i-table';
import { TableComponent } from '../../shared/components/table/table.component';
import { MatTableDataSource } from '@angular/material/table';
import { SelectionModel } from '@angular/cdk/collections';
import { PageHeaderComponent } from '../../shared/components/page-header/page-header.component';
import { StrategyDialogComponent } from '../strategy/strategy-dialog.component';
import { MatDialog } from '@angular/material/dialog';
import { StrategiesServiceProxy } from '@core/api/api.generated';
import Swal from 'sweetalert2';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { StrategyService } from '@core/services/strategy.service';
import { DatePipe } from '@angular/common';
import { CommonModule } from '@angular/common';
import { BreadcrumbComponent } from "../../shared/components/breadcrumb/breadcrumb.component";
import { IBreadcrumbItem } from '@core/gl-interfaces/IBreadcrumbItem/ibreadcrumb-item';
import { SizeEnum } from '@shared/enum/size-enum';
import { TokenService } from '../auth/services/token.service';

export interface PeriodicElement {
  id:number;
  nameAr: string;
  nameEn: string;
  updatedAt: string;
}

@Component({
  selector: 'app-fund-strategies',
  standalone: true,
  imports: [CommonModule, TableComponent, PageHeaderComponent, TranslateModule, BreadcrumbComponent],
  templateUrl: './fund-strategies.component.html',
  styleUrl: './fund-strategies.component.scss',
  providers: [DatePipe]

})
export class FundStrategiesComponent implements OnInit {
  ELEMENT_DATA: PeriodicElement[] = [];
  isDialogOpen = false;

  selection = new SelectionModel<any>(true, []);
  sortingType: DataHandlingType = DataHandlingType.Frontend;
  paginationType: DataHandlingType = DataHandlingType.Frontend;
  tableColumns: ITableColumn[] = [];
  displayedColumns = this.tableColumns.map((c) => c.columnDef);

  tableDataSource = new MatTableDataSource<PeriodicElement>(this.ELEMENT_DATA);
  totalCount: any;
  breadcrumbSizeEnum = SizeEnum;
  breadcrumbItems: IBreadcrumbItem[] = [
    {
      label: 'sidebar.settings',
      url: '',
      icon: 'fas fa-home',
      disabled: false
    },
    {
      label: 'sidebar.fund_strategies',
      url: '/admin/fund-strategies',
     disabled: true

    },
  ];

  constructor(
    private dialog: MatDialog,
    private strategyService: StrategyService,
    private translateService: TranslateService,
    private datePipe: DatePipe,
    public tokenService:TokenService
  ) {}

  ngOnInit() {
    this.initializeTableColumns();
    this.getStrategyList(0,0,'','UpdatedAt desc');
  }

  private initializeTableColumns(): void {
    this.tableColumns = [
      {
        columnDef: 'nameAr',
        header: 'FUND_STRATEGIES.STRATEGY_NAME_AR',
        columnType: ColumnTypeEnum.TextLink,
        cell: (element: PeriodicElement) => `${element.nameAr}`,
        class: 'text-underline',
        isSortingBy: true,
      },
      {
        columnDef: 'nameEn',
        header: 'FUND_STRATEGIES.STRATEGY_NAME_EN',
        columnType: ColumnTypeEnum.TextLink,
        cell: (element: PeriodicElement) => `${element.nameEn}`,
        class: 'text-underline',
        isSortingBy: true,
      },
      {
        columnDef: 'updatedAt',
        header: 'FUND_STRATEGIES.UPDATED_DATE',
        columnType: ColumnTypeEnum.Text,
        cell: (element: PeriodicElement) =>
          this.datePipe.transform(element.updatedAt, 'yyyy / MM / dd') ?? '',
        isSortingBy: true,
      },
      {
        columnDef: 'ddlActions',
        header: 'FUND_STRATEGIES.ACTIONS',
        columnType: ColumnTypeEnum.Actions,
        displayMode: ActionDisplayMode.Flex,
        cell: (element: PeriodicElement) => ({
          buttons: [{
            label: 'FUND_STRATEGIES.EDIT',
            action: 'edit',
            iconSrc: 'assets/images/edit.png',
          }],
        }),
      },
    ];
    this.displayedColumns = this.tableColumns.map((c) => c.columnDef);
  }

  getStrategyList(pageNo:number,pageSize:number,search:string,orderBy:string) {
    this.strategyService.strategyList(pageNo, pageSize, search,orderBy).subscribe((res: any) => {
      this.ELEMENT_DATA = res.data;
      this.tableDataSource.data=res.data
      this.totalCount = res.totalCount
    });
  }

  onClickAction(actionData: TableActionEvent) {
    this.edit(actionData.row);
  }

  edit(row: any) {
    this.strategyService.getStrategyById(row.id).subscribe((res: any) => {
      const strategy = res.data;
      const dialogRef = this.dialog.open(StrategyDialogComponent, {
        width: '500px',
        data: {
          isEdit: true,
          arabicName: strategy.nameAr,
          strategyName: strategy.nameEn,
          id: strategy.id
          ,strategysNames :this.tableDataSource
        },
      });

      dialogRef.afterClosed().subscribe((result: any) => {
        if (result) {
          let obj: any = {
            id: strategy.id,
            nameAr: result.arabicName,
            nameEn: result.strategyName,
          };
          this.strategyService.updateStrategy(obj).subscribe((res: any) => {
            if (res.successed) {
              Swal.fire({
                icon: 'success',
                title: this.translateService.instant('FUND_STRATEGIES.DATA_MODIFIED_SUCCESSFULLY'),
                showConfirmButton: false,
                timer: 1500,
              });
              this.getStrategyList(0, 0, '', 'UpdatedAt desc');
            }
          });
        }
      });
    });
  }


  onSwitchToggle(event: SwitchToggleEvent) {
    console.log('Switch toggled:', event);
  }
  onTextLinkClick(event: TextLinkClickEvent) {
    console.log('TextLink Clicked:', event);
  }
  isAllSelected() {
    const numSelected = this.selection.selected.length;
    const numRows = this.tableDataSource.data.length;
    return numSelected === numRows;
  }
  toggleAllRows() {
    if (this.isAllSelected()) {
      this.selection.clear();
    } else {
      this.selection.select(...this.tableDataSource.data);
    }
  }

  /** Toggle selection of a single row */
  toggleRow(row: any) {
    this.selection.toggle(row);
  }
  handleAction(row: any) {
    console.log('Action triggered for row:', row);
  }

  onSortChanged(event: { active: string; direction: string }): void {
    const { active, direction } = event;
    console.log('Sort column:', active);
    console.log('Sort direction:', direction);
    /*
  get data from api call
  */
  }
  onPageChange(event: any): void {
    console.log('Page index:', event.pageIndex);
    console.log('Page size:', event.pageSize);
    /*
  get data from api call
  */
  }

  customAction(element: any) {
    console.log('Custom action executed for:', element);
  }
  onCreateNewFund() {
    if (this.isDialogOpen) return;
    this.isDialogOpen = true;

    const dialogRef = this.dialog.open(StrategyDialogComponent, {
      width: '500px',
      data: { isEdit: false,strategysNames :this.tableDataSource },
    });

    dialogRef.afterClosed().subscribe((result: any) => {
      this.isDialogOpen = false;

      if (result) {
        let obj: any = {
          id: 0,
          nameAr: result.arabicName,
          nameEn: result.strategyName,
        };
        this.strategyService.createStrategy(obj).subscribe((res: any) => {
          if (res.successed) {
            Swal.fire({
              icon: 'success',
              title: this.translateService.instant(
                'FUND_STRATEGIES.RECORD_SAVED_SUCCESSFULLY'
              ),
              showConfirmButton: false,
              timer: 1500,
            });
          }
          this.getStrategyList(0,0,'','UpdatedAt desc');
        });
      }
    });
  }
}
