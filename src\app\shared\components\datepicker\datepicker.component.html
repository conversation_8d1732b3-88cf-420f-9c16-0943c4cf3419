<div class="position-relative">

  <div class="input-group">
    <input class="form-control"
           readonly
           (focusout)="onBlurOutside($event)"
           [value]="getFormattedDate()"
           [placeholder]="placeholder"
           (click)="openPickers()" />
    <span class="input-group-text" (click)="openPickers()">
      <img src="assets/images/calender.png" alt="icon" width="16" height="16" style="z-index: 999;">
    </span>
  </div>

  <div class="date-picker-container d-flex flex-wrap flex-md-nowrap gap-3 calenders" *ngIf="showPickers">


    <!-- Hijri Calendar -->
    <div class="calendar-wrapper mb-2">
      <!-- <label class="calendar-label">{{ hijriLabel || 'التقويم الهجري' }}</label> -->
      <arabdt-hijri-datepicker (dateChange)="onHijriSelected($event)" [model]="selectedDate.hijri"
        [placeholder]="placeholder" [minDate]="minHijri" [maxDate]="maxHijri" [isInvalid]="isInvalid"
        [ngClass]="{ 'is-invalid': isInvalid }"></arabdt-hijri-datepicker>
    </div>

    <!-- Gregorian Calendar -->
    <div class="calendar-wrapper">
      <!-- <label class="calendar-label">{{ GregLabel || 'Gregorian Calendar' }}</label> -->
      <arabdt-gregorian-datepicker (dateChange)="onGregorianSelected($event)" [placeholder]="placeholder"
        [model]="selectedDate.gregorian" [minDate]="minGreg" [maxDate]="maxGreg"
        [isInvalid]="isInvalid"></arabdt-gregorian-datepicker>
    </div>
  </div>

</div>
