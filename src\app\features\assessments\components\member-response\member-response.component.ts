import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatTooltipModule } from '@angular/material/tooltip';

// Shared Components
import { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';
import { CustomButtonComponent } from '@shared/components/custom-button/custom-button.component';
import { FormBuilderComponent } from '@shared/components/form-builder/form-builder.component';

// Core Services and Interfaces
import { IBreadcrumbItem } from '@core/gl-interfaces/IBreadcrumbItem/ibreadcrumb-item';
import { ButtonTypeEnum, IconEnum } from '@core/enums/icon-enum';
import { SizeEnum } from '@shared/enum/size-enum';
import { ToastrService } from 'ngx-toastr';
// API Generated Types
import {
  AssessmentServiceProxy,
  AssessmentDetailsDto,
  AssessmentResponseDto,
  AssessmentResultsDtoBaseResponse,
  QuestionType,
  StringBaseResponse,
  SubmitAssessmentResponseCommand,
  SubmitAssessmentAnswerDto,
  AssessmentStatus,
  ResponseStatus,
  AssessmentType,
  CreateAssessmentOptionDto,
  AttachmentDecision
} from '@core/api/api.generated';
import { AttachmentCardComponent } from 'src/app/features/resolutions/components/attachment-card/attachment-card.component';
import { TokenService } from 'src/app/features/auth/services/token.service';
import { ErrorModalService } from '@core/services/error-modal.service';

@Component({
  selector: 'app-member-response',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    TranslateModule,
    MatTooltipModule,
    BreadcrumbComponent,
    CustomButtonComponent,
    FormBuilderComponent,
    AttachmentCardComponent
  ],
  templateUrl: './member-response.component.html',
  styleUrls: ['./member-response.component.scss']
})
export class MemberResponseComponent implements OnInit, OnDestroy {
  // Component State
  isLoading = false;
  isSaving = false;
  isSubmitting = false;
  
  // Data Properties
  assessment: AssessmentDetailsDto | null = null;
  assessmentResponse: AssessmentResponseDto | null = null;
  currentAssessmentId: number = 0;
  currentBoardMemberId: number = 0;
  currentFundId: number = 0;
  currentFundName: string = '';
  
  // Form Properties
  responseForm: FormGroup;
  questionForms: FormGroup[] = [];

  // Attachment Decision Options
  decisionOptions = [
    { id: AttachmentDecision._1, name: 'APPROVE' },
    { id: AttachmentDecision._2, name: 'REJECT' }
  ];
  AttachmentDecision = AttachmentDecision;
  
  // UI Properties
  breadcrumbItems: IBreadcrumbItem[] = [];
  breadcrumbSizeEnum = SizeEnum;
  buttonEnum = ButtonTypeEnum;
  IconEnum = IconEnum;
  QuestionType = QuestionType;
  assessmentType = AssessmentType;

  // Expand/Collapse States
  isExpanded = true;
  isQuestionsExpanded = true;

  // Role-Based Access Control Properties
  userRole = '';
  isFundManager = false;
  isBoardMember = false;
  isReadOnlyMode = false;
  canResponse = false;

  // Lifecycle Management
  private destroy$ = new Subject<void>();

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private formBuilder: FormBuilder,
    private assessmentServiceProxy: AssessmentServiceProxy,
    private translateService: TranslateService,
    private toastr: ToastrService,
    public tokenService: TokenService,
    private errorModalService: ErrorModalService
  ) {
    this.responseForm = this.formBuilder.group({});
  }

  ngOnInit(): void {
    this.initializeRouteParams();
    this.decisionOptions = [
       { id: AttachmentDecision._1, name:this.translateService.instant('ASSESSMENTS.Identical') },
       { id: AttachmentDecision._2, name:this.translateService.instant('ASSESSMENTS.NotIdentical')}
    ];
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // Initialization Methods
  private initializeRouteParams(): void {
    // First try to get params from route snapshot (synchronous)
    const snapshot = this.route.snapshot;
    if (snapshot.queryParams) {
      this.currentAssessmentId = +snapshot.queryParams['assessmentId'] || 0;
      this.currentBoardMemberId = +snapshot.queryParams['memberId'] || 0;
      this.currentFundId = +snapshot.queryParams['fundId'] || 0;

      // If we have valid params from snapshot, proceed immediately
      if (this.currentAssessmentId > 0 && this.currentBoardMemberId > 0) {
        this.updateBreadcrumbWithFallback();
        this.loadAssessmentData();
        return;
      }
    }

    // Fallback to observable for dynamic route changes
    this.route.queryParams.pipe(takeUntil(this.destroy$)).subscribe(queryParams => {

      this.currentAssessmentId = +queryParams['assessmentId'] || 0;
      this.currentBoardMemberId = +queryParams['memberId'] || 0;

      // Initialize breadcrumbs and load data after route params are set
      this.loadAssessmentData();
    });
  }

  // Breadcrumb Management
  private updateBreadcrumbWithFallback(): void {
    this.breadcrumbItems = [
      { label: 'BREADCRUMB.HOME', url: '/admin/dashboard' },
      { label: 'BREADCRUMB.INVESTMENT_FUNDS', url: '/admin/investment-funds' },
      {
        label: this.currentFundName || 'BREADCRUMB.FUND_DETAILS',
        url: `/admin/investment-funds/fund-details?id=${this.currentFundId}`
      },
      {
        label: 'BREADCRUMB.ASSESSMENTS',
        url: `/admin/investment-funds/assessments?fundId=${this.currentFundId}`
      },
      { label: 'BREADCRUMB.RESPONSE' }
    ];
  }

  // Data Loading Methods
  private loadAssessmentData(): void {
    // Enhanced validation with detailed logging
    if (!this.currentAssessmentId || this.currentAssessmentId <= 0) {
      console.error('Invalid assessment ID:', this.currentAssessmentId);
      this.showError('ASSESSMENTS.MEMBER_RESPONSE.INVALID_ASSESSMENT_ID');
      this.navigateBack();
      return;
    }

    if (!this.currentBoardMemberId || this.currentBoardMemberId <= 0) {
      console.error('Invalid member ID:', this.currentBoardMemberId);
      this.showError('ASSESSMENTS.MEMBER_RESPONSE.INVALID_MEMBER_ID');
      this.navigateBack();
      return;
    }

    this.isLoading = true;

    // Use single API call to get assessment response with questions and member responses
    this.assessmentServiceProxy.getAssessmentResponse(this.currentAssessmentId, this.currentBoardMemberId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: AssessmentResultsDtoBaseResponse) => {

          if (response?.successed && response?.data) {
            // The API returns AssessmentResultsDto which contains both assessment details and response
            this.assessmentResponse = response.data.response;
            this.currentFundId = response.data.fundId;
            this.currentFundName = response.data.fundName || 'BREADCRUMB.FUND_DETAILS';
            // Extract assessment structure from the full response data (not just the response part)
            this.extractAssessmentFromFullResponse(response.data);
            this.canResponse=response.data.canResponse;
            this.isReadOnlyMode=!this.canResponse;
            this.updateBreadcrumbWithFallback();

            // Validate that we have questions (except for attachment-type assessments)
            if ((!this.assessment?.questions || this.assessment.questions.length === 0) && !this.isAttachmentType()) {
              this.showError('ASSESSMENTS.MEMBER_RESPONSE.NO_QUESTIONS_AVAILABLE');
              this.isLoading = false;
              return;
            }

            // Initialize forms with existing responses
            this.initializeQuestionForms();

            // Update UI state based on API response and role
            this.updateFormStateBasedOnPermissions();

            this.updateBreadcrumbWithFallback();
            this.updateBreadcrumbWithAssessmentTitle();
            this.isLoading = false;
          } else {
            this.isLoading = false;
            const errorMessage = response?.message || 'Failed to load assessment response data';
            console.error('Assessment response loading failed:', errorMessage, response);
            this.showError('ASSESSMENTS.MEMBER_RESPONSE.LOAD_ERROR');
          }
        },
        error: (error: any) => {
          this.isLoading = false;
          console.error('Error loading assessment response:', error);

          // Provide more specific error messages based on error type
          if (error.status === 400) {
            console.error('Bad request - possibly invalid parameters:', error);
            this.showError('ASSESSMENTS.MEMBER_RESPONSE.INVALID_PARAMETERS');
          } else if (error.status === 404) {
            this.showError('ASSESSMENTS.MEMBER_RESPONSE.ASSESSMENT_NOT_FOUND');
          } else if (error.status === 403) {
            this.showError('ASSESSMENTS.MEMBER_RESPONSE.ACCESS_DENIED');
          } else if (error.status === 0) {
            this.showError('ASSESSMENTS.MEMBER_RESPONSE.NETWORK_ERROR');
          } else {
            this.showError('ASSESSMENTS.MEMBER_RESPONSE.LOAD_ERROR');
          }
        }
      });
  }

  private extractAssessmentFromFullResponse(assessmentData: any): void {

    // Create assessment structure from the full assessment data (not just response)
    this.assessment = new AssessmentDetailsDto({
      id: assessmentData.id || 0,
      title: assessmentData.title,
      description: assessmentData.description,
      type: assessmentData.type || AssessmentType._1,
      status: assessmentData.status || AssessmentStatus._5,
      fundId: assessmentData.fundId || this.currentFundId,
      fundName: assessmentData.fundName,
      instructions: assessmentData.instructions,
      allowAnonymousResponses: assessmentData.allowAnonymousResponses || false,
      allowResponseEditing: assessmentData.allowResponseEditing || false,
      statusDisplayName: assessmentData.statusDisplayName,
      createdByName: assessmentData.createdByName,
      createdAt: assessmentData.createdAt,
      updatedAt: assessmentData.updatedAt,
      questions: this.convertQuestionsWithAnswers(assessmentData.questions, this.assessmentResponse?.answers),
      attachment: assessmentData.attachment,
      statusHistory: assessmentData.statusHistory,
      responses: undefined,
      assignedBoardMembers: undefined,
      statistics: assessmentData.statistics,
      allowedTransitions: undefined,
      validationMessages: undefined,
      canEdit: this.assessmentResponse?.canEdit || false,
      canDelete: false,
      canApprove: false,
      canReject: false,
      canDistribute: false,
      rejectionReason: undefined,
      distributionDate: undefined,
      completionDate: undefined,
      lastResponseDate: undefined,
      averageResponseTime: undefined
    });
  }

  private convertQuestionsWithAnswers(questions: any[] | undefined, answers: any[] | undefined): any[] {

    if (!questions || questions.length === 0) {
      return [];
    }

    // Convert each question and merge with corresponding answer data
    const convertedQuestions = questions.map((question: any) => {

      // Find the corresponding answer for this question
      const answerData = answers?.find(answer => answer.questionId === question.id);
      const isAnswered = answerData?.isComplete || false;

      const convertedQuestion = {
        id: question.id,
        questionText: question.questionText,
        type: question.type,
        isRequired: question.isRequired || true,
        options:  this.convertOptionsWithAnswers(question.options, answerData), // Use the options from the question data
        isAnswered: isAnswered,
        displayOrder: question.displayOrder || 0,
        questionAnswer: answerData?.textAnswer
      };
      
      return convertedQuestion;
    });

    return convertedQuestions;
  }
    private convertOptionsWithAnswers(options: any[] | undefined, answerData: any | undefined): any[] 
{
    if (!options || options.length === 0) {
      return [];
    }
    // Convert each question and merge with corresponding answer data
    const convertedOptions = options.map((option: CreateAssessmentOptionDto) => {
      const convertedOption = {
        id: option.id,
        optionText: option.optionText,
        order:option.order,
        isSelected: answerData?.selectedOptions?.some((selectedOption: any) => selectedOption.optionId === option.id) || false
      };

      return convertedOption;
    });
        return convertedOptions;

}


  private initializeQuestionForms(): void {

    this.questionForms = [];
    const formControls: { [key: string]: any } = {};
    let answeredCount = 0;
    let unansweredCount = 0;

    // Process questions if they exist
    if (this.assessment?.questions && this.assessment.questions.length > 0) {

    this.assessment.questions.forEach((question, index) => {

      const controlName = `question_${question.id || index}`;
      let defaultValue: any = '';

      // Use the already-processed question data instead of searching raw response
      // Check if question is answered based on the processed data
      let isAnswered = false;
      if (question.type === QuestionType._1 || question.type === QuestionType._2) {
        // For choice questions, check if any option is selected
        isAnswered = question.options?.some((option: any) => option.isSelected) || false;
      } else if (question.type === QuestionType._3) {
        // For text questions, check if questionAnswer exists
        isAnswered = !!(question as any).questionAnswer;
      }

      if (isAnswered) {
        answeredCount++;
      } else {
        unansweredCount++;
      }

      if (question.type === QuestionType._1) {
        // Single choice - radio button
        // Find the selected option from the processed question options
        const selectedOption = question.options?.find(option => option.isSelected);
        if (selectedOption) {
          defaultValue = selectedOption.id;
        } else {
          defaultValue = null; // Use null instead of empty string for better form control handling
        }
        formControls[controlName] = [defaultValue, question.isRequired ? Validators.required : null];
      } else if (question.type === QuestionType._2) {
        // Multiple choice - checkbox array
        // Find all selected options from the processed question options
        const selectedOptions = question.options?.filter(option => option.isSelected) || [];
        if (selectedOptions.length > 0) {
          defaultValue = selectedOptions.map(option => option.id);
        } else {
          defaultValue = [];
        }
        formControls[controlName] = [defaultValue, question.isRequired ? Validators.required : null];
      } else if (question.type === QuestionType._3) {
        // Text answer - use the processed question answer
        const questionAnswer = (question as any).questionAnswer;
        if (questionAnswer) {
          defaultValue = questionAnswer;
        } else {
          defaultValue = '';
        }
        formControls[controlName] = [defaultValue, question.isRequired ? Validators.required : null];
      }

    });
    }

    // Add attachment decision and comment fields for attachment-type assessments
    if (this.isAttachmentType()) {
      // Get existing decision and comment values from the assessment response
      const existingDecision = this.assessmentResponse?.decision || null;
      const existingComment = this.assessmentResponse?.comments || '';



      // Add decision field (required for attachment-type assessments)
      formControls['attachment_decision'] = [existingDecision, Validators.required];

      // Add comment field (optional but recommended)
      formControls['attachment_comment'] = [existingComment];
    }
    this.responseForm = this.formBuilder.group(formControls);

    // Ensure form is enabled if user can respond
    if (this.canResponse && !this.isReadOnlyMode) {
      this.responseForm.enable();
    } else {
      this.responseForm.disable();
    }
  }



  private updateBreadcrumbWithAssessmentTitle(): void {
    if (this.assessment?.title) {
      this.breadcrumbItems[this.breadcrumbItems.length - 1].label =
        `${this.translateService.instant('BREADCRUMB.RESPONSE')}: ${this.assessment.title}`;
    }
  }

  // Form Submission Methods
  onSaveDraft(): void {
    // Check if user has permission to save
    if (this.isReadOnlyMode || !this.canResponse) {
      this.showError('ASSESSMENTS.MEMBER_RESPONSE.CANNOT_EDIT_READ_ONLY');
      return;
    }

    if (this.isSaving || this.isSubmitting) {
      return; // Prevent multiple simultaneous requests
    }
    this.saveResponse(true);
  }

  onSubmitResponse(): void {
    // Check if user has permission to submit
    if (this.isReadOnlyMode || !this.canResponse) {
      this.showError('ASSESSMENTS.MEMBER_RESPONSE.CANNOT_EDIT_READ_ONLY');
      return;
    }

    if (this.isSaving || this.isSubmitting) {
      return; // Prevent multiple simultaneous requests
    }

    if (this.responseForm.valid) {
      this.saveResponse(false);
    } else {
      this.markFormGroupTouched(this.responseForm);
      this.showError('ASSESSMENTS.MEMBER_RESPONSE.REQUIRED_FIELD');
    }
  }

  private saveResponse(isDraft: boolean): void {
    if (isDraft) {
      this.isSaving = true;
    } else {
      this.isSubmitting = true;
    }

    const answers = this.buildAnswersFromForm();

    // Get attachment decision and comment for attachment-type assessments
    let decision: AttachmentDecision = AttachmentDecision._1; // Default to approve for non-attachment types
    let comments: string | undefined = undefined;

    if (this.isAttachmentType()) {
      decision = this.responseForm.get('attachment_decision')?.value || AttachmentDecision._1;
      comments = this.responseForm.get('attachment_comment')?.value || undefined;
    }

    const command = new SubmitAssessmentResponseCommand({
      assessmentId: this.currentAssessmentId,
      answers: answers,
      saveAsDraft: isDraft,
      assessmentType: this.assessment?.type || AssessmentType._1,
      comments: comments,
      decision: decision
    });

    this.assessmentServiceProxy.response(command)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: StringBaseResponse) => {
          this.isSaving = false;
          this.isSubmitting = false;
          if (response && response.successed && response.data) {
            this.errorModalService.showSuccess(
              response.data
            );
            if (!isDraft) {
              this.navigateBack();
            }
          } else {
            console.error('Response failed or undefined:', response); // Debug log
            const errorKey = isDraft ? 'SAVE_ERROR' : 'SUBMIT_ERROR';
            this.showError(`ASSESSMENTS.MEMBER_RESPONSE.${errorKey}`);
          }
        },
        error: (error: any) => {
          this.isSaving = false;
          this.isSubmitting = false;
          console.error('Error saving response:', error);

          // Provide specific error messages based on error type
          let errorKey: string;
          if (error.status === 400) {
            errorKey = isDraft ? 'VALIDATION_ERROR_DRAFT' : 'VALIDATION_ERROR_SUBMIT';
          } else if (error.status === 403) {
            errorKey = 'ACCESS_DENIED';
          } else if (error.status === 404) {
            errorKey = 'ASSESSMENT_NOT_FOUND';
          } else if (error.status === 409) {
            errorKey = isDraft ? 'CONFLICT_ERROR_DRAFT' : 'CONFLICT_ERROR_SUBMIT';
          } else if (error.status === 0) {
            errorKey = 'NETWORK_ERROR';
          } else {
            errorKey = isDraft ? 'SAVE_ERROR' : 'SUBMIT_ERROR';
          }

          this.showError(`ASSESSMENTS.MEMBER_RESPONSE.${errorKey}`);
        }
      });
  }

  private buildAnswersFromForm(): SubmitAssessmentAnswerDto[] {
    const answers: SubmitAssessmentAnswerDto[] = [];

    if (!this.assessment?.questions) return answers;

    this.assessment.questions.forEach((question, index) => {
      const controlName = `question_${question.id || index}`;
      const value = this.responseForm.get(controlName)?.value;

      if (value !== null && value !== undefined && value !== '') {
        const answer = new SubmitAssessmentAnswerDto({
          questionId: question.id || 0,
          textAnswer: question.type === QuestionType._3 ? value : undefined,
          selectedOptionIds: question.type === QuestionType._1 ? [value] :
                           question.type === QuestionType._2 ? value : undefined
        });
        answers.push(answer);
      }
    });

    return answers;
  }

  onBreadcrumbClicked(event: any): void {
    if (event?.url) {
      this.router.navigateByUrl(event.url);
    }
  }

  onCancel(): void {
    this.navigateBack();
  }

  private navigateBack(): void {
    this.router.navigate(['/admin/investment-funds/assessments'], {
      queryParams: { fundId: this.currentFundId }
    });
  }

  // Utility Methods
  private markFormGroupTouched(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      control?.markAsTouched();
    });
  }

  private showSuccess(messageKey: string): void {
    const message = this.translateService.instant(messageKey);
    this.toastr.success(message);
  }

  private showError(messageKey: string): void {
    const message = this.translateService.instant(messageKey);
    this.toastr.error(message);
  }

  // Question Type Helpers
  isSingleChoice(questionType: QuestionType): boolean {
    return questionType === QuestionType._1;
  }

  isMultipleChoice(questionType: QuestionType): boolean {
    return questionType === QuestionType._2;
  }

  isTextAnswer(questionType: QuestionType): boolean {
    return questionType === QuestionType._3;
  }

  // Multiple Choice Handler - Improved with better error handling and logging
  onMultipleChoiceChange(event: any, questionId: number, optionId: number): void {
    if (this.shouldDisableFormControls()) {
      return;
    }

    const controlName = `question_${questionId}`;
    const control = this.responseForm.get(controlName);

    if (!control) {
      console.error(`Form control not found for question ${questionId}`);
      return;
    }

    let currentValues: number[] = Array.isArray(control.value) ? control.value : [];

    if (event.target.checked) {
      // Add option if checked and not already present
      if (!currentValues.includes(optionId)) {
        currentValues = [...currentValues, optionId];
      }
    } else {
      // Remove option if unchecked
      currentValues = currentValues.filter(id => id !== optionId);
    }

    control.setValue(currentValues);
    control.markAsTouched(); // Mark as touched for validation
  }


  // UI Control Methods
  toggleExpand(): void {
    this.isExpanded = !this.isExpanded;
  }

  toggleQuestionsExpand(): void {
    this.isQuestionsExpanded = !this.isQuestionsExpanded;
  }

  // Assessment Type Helpers
  isQuestionnaireType(): boolean {
    return this.assessment?.type === AssessmentType._1;
  }

  isAttachmentType(): boolean {
    return this.assessment?.type === AssessmentType._2;
  }

  // Status Display Methods
  getAssessmentStatusClass(status: AssessmentStatus | undefined): string {
    if (!status) return 'status-grey';

    switch (status) {
      case AssessmentStatus._1: // Draft
        return 'status-grey';
      case AssessmentStatus._2: // Under Review
        return 'status-blue';
      case AssessmentStatus._3: // Approved
        return 'status-green';
      case AssessmentStatus._4: // Rejected
        return 'status-red';
      case AssessmentStatus._5: // Distributed
      return 'status-green';
      case AssessmentStatus._6: // Completed
      return 'status-purple';
      default:
        return 'status-grey';
    }
  }

  getResponseStatusClass(status: ResponseStatus | undefined): string {
    if (!status) return 'status-grey';

    switch (status) {
      case ResponseStatus._1: // pending
        return 'status-blue';
      case ResponseStatus._2: // Submitted
        return 'status-green';
      default:
        return 'status-grey';
    }
  }

  getResponseStatusDisplayName(status: ResponseStatus | undefined): string {
    if (!status) return this.translateService.instant('ASSESSMENTS.RESPONSE_STATUS.UNKNOWN');

    switch (status) {
      case ResponseStatus._1: // pending
        return this.translateService.instant('ASSESSMENTS.RESPONSE_STATUS.PENDING');
      case ResponseStatus._2: // Submitted
        return this.translateService.instant('ASSESSMENTS.RESPONSE_STATUS.SUBMITTED');
      default:
        return this.translateService.instant('ASSESSMENTS.RESPONSE_STATUS.UNKNOWN');
    }
  }

  getAssessmentStatusDisplayName(status: AssessmentStatus | undefined, statusDisplayName: string | undefined): string {
    // If we have a display name from the API, use it
    if (statusDisplayName) {
      return statusDisplayName;
    }

    // Fallback to translation keys based on status
    if (!status) return this.translateService.instant('ASSESSMENTS.ASSESSMENT_STATUS.UNKNOWN');

     switch (status) {
      case AssessmentStatus._1:
        return this.translateService.instant('ASSESSMENTS.STATUS.DRAFT');
      case AssessmentStatus._2:
        return this.translateService.instant('ASSESSMENTS.STATUS.WAITINGFORAPPROVAL');
      case AssessmentStatus._3:
        return this.translateService.instant('ASSESSMENTS.STATUS.APPROVED');
      case AssessmentStatus._4:
        return this.translateService.instant('ASSESSMENTS.STATUS.REJECTED');
      case AssessmentStatus._5:
        return this.translateService.instant('ASSESSMENTS.STATUS.ACTIVE');
      case AssessmentStatus._6:
        return this.translateService.instant('ASSESSMENTS.STATUS.COMPLETED');
      default:
        return this.translateService.instant('ASSESSMENTS.ASSESSMENT_STATUS.UNKNOWN');
    }
  }

  // Navigation to member response (for sidebar links)
  navigateToMemberResponse(memberId: number): void {
    if (this.currentAssessmentId && memberId) {
      this.router.navigate([
        '/admin/investment-funds/assessments/member-response'
      ], {
        queryParams: { assessmentId: this.currentAssessmentId, memberId: memberId }
      });
    }
  }
  private updateFormStateBasedOnPermissions(): void {
    if (!this.responseForm) {
      return;
    }


    if (this.isReadOnlyMode || !this.canResponse) {
      // Disable all form controls for read-only mode or no response permission
      this.responseForm.disable();
    } else {
      // Enable form controls for interactive mode
      this.responseForm.enable();
    }
  }

  // Helper methods for template
  hasQuestions(): boolean {
    return !!(this.assessment?.questions && this.assessment.questions.length > 0);
  }

  getQuestionsCount(): number {
    return this.assessment?.questions?.length || 0;
  }

  getFirstQuestionText(): string {
    return this.assessment?.questions?.[0]?.questionText || '';
  }

  // Role-based UI helper methods
  shouldShowSaveButton(): boolean {
    return !this.isReadOnlyMode && this.canResponse;
  }

  shouldShowSubmitButton(): boolean {
    return !this.isReadOnlyMode && this.canResponse;
  }

  shouldDisableFormControls(): boolean {
    const shouldDisable = this.isReadOnlyMode || !this.canResponse || this.isLoading;
    return shouldDisable;
  }

  getReadOnlyModeMessage(): string {
    if (this.assessmentResponse?.memberName != this.tokenService.getFullName()) {
      return 'ASSESSMENTS.MEMBER_RESPONSE.FUND_MANAGER_READ_ONLY';
    } else if (!this.canResponse) {
      return 'ASSESSMENTS.MEMBER_RESPONSE.RESPONSE_COMPLETED';
    }
    return 'ASSESSMENTS.MEMBER_RESPONSE.READ_ONLY_MODE';
  }



  // Helper method to determine if a text answer has content in readonly mode
  hasTextAnswerContent(questionId: number, questionIndex: number): boolean {
    if (!this.shouldDisableFormControls()) {
      return false; // Not in readonly mode
    }

    const controlName = `question_${questionId || questionIndex}`;
    const control = this.responseForm.get(controlName);

    if (!control) {
      return false;
    }

    const value = control.value;
    return !!(value && value.toString().trim().length > 0);
  }

  // Helper method to determine if attachment comment has content in readonly mode
  hasAttachmentCommentContent(): boolean {
    if (!this.shouldDisableFormControls()) {
      return false; // Not in readonly mode
    }

    const control = this.responseForm.get('attachment_comment');
    if (!control) {
      return false;
    }

    const value = control.value;
    return !!(value && value.toString().trim().length > 0);
  }

  // Helper method to get CSS classes for form-check containers in readonly mode
  getFormCheckClasses(questionId: number, optionId: number, questionIndex: number): string {
    const baseClasses = 'form-check';

    if (!this.shouldDisableFormControls()) {
      return baseClasses; // Not in readonly mode
    }

    const controlName = `question_${questionId || questionIndex}`;
    const control = this.responseForm.get(controlName);

    if (!control) {
      return baseClasses;
    }

    const value = control.value;
    let isSelected = false;

    // For single choice questions, check if this option's ID matches the selected value
    if (Array.isArray(value)) {
      // Multiple choice - check if option ID is in the array
      isSelected = value.includes(optionId);
    } else {
      // Single choice - check if option ID matches the value
      isSelected = value === optionId;
    }

    if (isSelected) {
      return `${baseClasses} readonly-selected`;
    }

    return baseClasses;
  }

}
