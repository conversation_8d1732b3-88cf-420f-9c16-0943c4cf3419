@import "../../../../../assets/scss/variables";

.create-assessment-page {
  padding: 20px;
  background-color: #f8f9fa;
  min-height: 100vh;

  .breadcrumb-section {
    margin-bottom: 20px;
  }

  .page-header-section {
    margin-bottom: 30px;
  }

  .main-content {
    .create-form-container {
      background: #F8FAFC;
      border-radius: 16px;
      border: 0.5px solid #DCE0E3;
      padding: 16px;

      .form-section {
        margin-bottom: 20px;

        .section-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;

          .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;

            h6 {
              font-size: 16px;
              font-weight: 600;
              color: #00205a;
              margin: 0;
            }

            .items-num {
              font-size: 14px;
              color: #6B7280;
              margin-left: 8px;
            }

            .add-item-btn {
              display: flex;
              align-items: center;
              gap: 8px;
              padding: 8px 16px;
              background: #00205a;
              color: white;
              border: none;
              border-radius: 8px;
              font-size: 14px;
              font-weight: 500;
              cursor: pointer;
              transition: background-color 0.2s;

              &:hover {
                background: #001a4d;
              }

              svg {
                width: 16px;
                height: 16px;
              }
            }
          }
        }

        // Form builder header styling
        .header {
          font-size: 16px;
          font-weight: 600;
          color: #00205a;
          margin-bottom: 10px;
        }

      }
    }
  }

  // Assessment Questions Styles - Following resolution items pattern
  .items-container {
    margin-bottom: 20px;

    .assessment-question-card {
      padding: 16px 10px;
      border-radius: 8px;
      background-color: white;
      margin-bottom: 16px;

      .item-header {
        .item-info {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .item-number {
            font-size: 20px;
            font-weight: 500;
            line-height: 32px;
            color: #00205a;
          }

          .item-actions {
            display: flex;
            gap: 8px;

            .btn {
              padding: 8px;
              &:hover{
                background: unset;
                border: unset;
                color: unset;
              }
            }
          }
        }
      }

      .item-body {
        margin-top: 12px;

        .item-description {
          font-size: 16px;
          font-weight: 400;
          line-height: 24px;
          color: #374151;
          margin-bottom: 8px;
        }

        .question-type-info {
          display: flex;
          align-items: center;
          gap: 12px;
          flex-wrap: wrap;

          .question-type-badge {
            padding: 4px 8px;
            background: #E5E7EB;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            color: #374151;
          }

          .question-options {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
            color: #6B7280;

            .options-count {
              font-weight: 500;
            }

            .correct-answers-count {
              color: #059669;
              font-weight: 500;
            }
          }
        }
      }
    }
  }

  .empty-items {
    padding: 40px 20px;
    text-align: center;

    .add-item-btn {
      display: inline-flex;
      align-items: center;
      gap: 8px;
      padding: 12px 24px;
      background: #00205a;
      color: white;
      border: none;
      border-radius: 8px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: background-color 0.2s;

      &:hover {
        background: #001a4d;
      }

      svg {
        width: 16px;
        height: 16px;
      }
    }
  }

      // Form Actions
      .form-actions {
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid #e0e0e0;

        .actions-container {
          display: flex;
          justify-content: flex-end;
          gap: 12px;
        }
      }
    }
  


// Responsive Design
@media (max-width: 768px) {
  .create-assessment-page {
    padding: 15px;

    .main-content {
      .create-form-container {
        padding: 20px;

        .form-actions {
          .actions-container {
            flex-direction: column;

            app-custom-button {
              width: 100%;
            }
          }
        }
      }
    }
  }
}
