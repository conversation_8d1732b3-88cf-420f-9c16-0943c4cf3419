@import "../../../../assets/scss/variables";

::ng-deep.mat-mdc-table thead{
background-color: #EBF3FC !important;
}

::ng-deep.mat-sort-header-content{
  font-weight: 700;
  color: #4F4F4F;
  font-size: 14px;
}

::ng-deep.mdc-data-table__cell {
  color:  #333;
font-size: 14px;
font-weight: 500;
}

  ::ng-deep .mat-mdc-table thead tr th.mat-mdc-header-cell:nth-child(n+3) .mat-sort-header-arrow {
    opacity: 0.54 !important;
    transform: translateY(0%) !important;
  }

.flex-buttons{
  display: flex;
  gap: 8px;
  align-items: end;
  justify-content: end;


  .action-button {
    background-color: transparent;
    border: 1px solid transparent;
    padding: 6px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      background-color: #f5f5f5;
      border-color: #e0e0e0;
    }

    &:focus {
      outline: none;
      box-shadow: 0 0 0 2px rgba(15, 108, 189, 0.2);
    }

    span {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

// Action menu styles
.action-menu-item {
  width: 100%;

  .action-menu-button {
    width: 100%;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: transparent;
    border: none;
    cursor: pointer;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: #f5f5f5;
    }

    .action-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
    }

    .action-label {
      font-size: 14px;
      color: #333;
      font-weight: 500;
    }
  }
}
::ng-deep.mat-sort-header-arrow{
color: #0F6CBD !important;
}

table td  tr:nth-child(odd):nth-child(n+1) {
  background-color: #FFFFFF;
}

table  tr:nth-child(even):nth-child(n+1) {
  background-color: #FAFAFA;
}

table {
  box-shadow: none;
}

::ng-deep {
   .mat-mdc-header-cell {
    text-align: end !important;
}
  .mat-mdc-slide-toggle {
    .mdc-switch {
      .mdc-switch__track {
        &::before {
          background-color: $grey !important;
        }

        &::after {
          background-color: #5675ad  !important;
        }
      }

      .mdc-switch__handle-track {
        .mdc-switch--selected {
          .mdc-switch__handle {
            &::before {
              background-color: #FFFFFF !important;
            }

            &::after{
              background-color: #00205a !important;
            }
          }
        }
      }

    }
  }

  .mdc-switch--selected:enabled:hover:not(:focus) .mdc-switch__ripple::after{
    background-color: #0F6CBD !important;
  }

  .mdc-switch--selected:enabled .mdc-switch__handle::after {
    background: $navy-blue !important;
  }

  .mdc-switch--selected:enabled:hover:not(:focus):not(:active) .mdc-switch__handle::after {
    background: $dark-blue !important;
  }

  .mdc-switch--selected:enabled:active .mdc-switch__ripple::after {
    background-color: #0F6CBD !important;
  }
}


.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 500;

  .dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin:0 6px;
  }

  &.status-green {
    background-color: #f1faf1;
    color: #27ae60;

    .dot {
      background-color: #27ae60;
    }
  }

  &.status-blue {
    background-color: #e5eefb;
    color: #2f80ed;

    .dot {
      background-color: #2f80ed;
    }
  }

  &.status-orange {
    background-color: #fdf1eb;
    color: #ff5f3d;

    .dot {
      background-color: #ff5f3d;
    }
  }

  &.status-grey {
    background-color: #e0e0e0;
    color: #828282;

    .dot {
      background-color: #828282;
    }
  }
}
