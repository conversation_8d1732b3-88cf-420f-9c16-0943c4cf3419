import { ComponentFixture, TestBed } from '@angular/core/testing';

import { VoteProposedMeetingComponent } from './vote-proposed-meeting.component';

describe('VoteProposedMeetingComponent', () => {
  let component: VoteProposedMeetingComponent;
  let fixture: ComponentFixture<VoteProposedMeetingComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [VoteProposedMeetingComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(VoteProposedMeetingComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
