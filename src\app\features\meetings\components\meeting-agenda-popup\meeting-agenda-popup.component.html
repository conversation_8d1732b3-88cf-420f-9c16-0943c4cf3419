<div class="resolution-item-dialog">
  <div class="dialog-header">
    <h2 class="dialog-title">
      {{ (isEdit ? 'MEETINGS.EDIT_AGENDA_ITEM' : 'MEETINGS.ADD_AGENDA_ITEM') | translate }}
    </h2>
    <!-- <button type="button" class="close-btn" (click)="onCancel()" aria-label="Close">
      <i class="fas fa-times" title="Close"></i>
    </button> -->
  </div>

  <!-- Loading State -->
  <!-- <div class="loading-container" *ngIf="isLoading">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">{{ 'COMMON.LOADING' | translate }}</span>
    </div>
    <p class="loading-text">{{ 'INVESTMENT_FUNDS.MEMBERS.LOADING' | translate }}</p>
  </div> -->

  <!-- Form Content -->
  <div class="dialog-content" *ngIf="!isLoading">
 <form [formGroup]="formGroup" (ngSubmit)="onSubmit()">

      <!-- Item Title Display -->
      <div class="item-title-section">
        <div class="form-group mb-3">
          <!-- <label class="form-label">
            {{ 'INVESTMENT_FUNDS.RESOLUTIONS.ITEM_TITLE' | translate }}
          </label> -->
          <div class="item-title-display mb-5">
            {{ itemTitle }}
          </div>
        </div>
      </div>

      <!-- Form Fields -->
      <div class="form-section">
        <app-form-builder
          [formControls]="formControls"
          [formGroup]="formGroup"
          [isFormSubmitted]="isValidationFire">
        </app-form-builder>
      </div>

      <!-- Form Actions -->
      <div class="dialog-actions">
        <button type="button"
                class="btn btn-secondary cancel-btn"
                (click)="onCancel()">
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M2.0062 2.7529C2.22033 2.53883 2.51072 2.41857 2.8135 2.41857C3.11629 2.41857 3.40667 2.53883 3.62081 2.7529L9.66474 8.79683L15.7087 2.7529C15.814 2.64384 15.94 2.55685 16.0793 2.497C16.2186 2.43716 16.3685 2.40566 16.5201 2.40434C16.6717 2.40302 16.8221 2.43191 16.9624 2.48933C17.1027 2.54674 17.2302 2.63153 17.3374 2.73874C17.4446 2.84596 17.5294 2.97345 17.5868 3.11378C17.6443 3.25411 17.6731 3.40447 17.6718 3.55609C17.6705 3.70771 17.639 3.85754 17.5792 3.99686C17.5193 4.13617 17.4323 4.26217 17.3233 4.3675L11.2793 10.4114L17.3233 16.4554C17.5313 16.6707 17.6464 16.9592 17.6438 17.2586C17.6412 17.558 17.5211 17.8443 17.3094 18.0561C17.0977 18.2678 16.8113 18.3879 16.5119 18.3905C16.2125 18.3931 15.924 18.278 15.7087 18.07L9.66474 12.026L3.62081 18.07C3.40545 18.278 3.11701 18.3931 2.81761 18.3905C2.51822 18.3879 2.23182 18.2678 2.02011 18.0561C1.8084 17.8443 1.68831 17.558 1.68571 17.2586C1.6831 16.9592 1.7982 16.6707 2.0062 16.4554L8.05013 10.4114L2.0062 4.3675C1.79213 4.15337 1.67188 3.86298 1.67188 3.5602C1.67188 3.25742 1.79213 2.96703 2.0062 2.7529Z" fill="#4F4F4F"/>
          </svg>
          {{ 'COMMON.CANCEL' | translate }}
        </button>

        <button type="submit"
                class="btn btn-primary submit-btn"
                [disabled]="isSubmitting">
          <svg width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M3.74121 9.48291C3.41817 9.48382 3.10198 9.57617 2.82923 9.74928C2.55648 9.92239 2.33831 10.1692 2.19997 10.4611C2.06163 10.753 2.00877 11.0782 2.0475 11.3989C2.08623 11.7196 2.21497 12.0228 2.41883 12.2734L6.76462 17.597C6.91957 17.7894 7.11819 17.942 7.34398 18.0423C7.56977 18.1425 7.81622 18.1874 8.06285 18.1732C8.59033 18.1449 9.06656 17.8627 9.37019 17.3988L18.3975 2.86031C18.399 2.85788 18.4005 2.85548 18.4021 2.85311C18.4868 2.72306 18.4593 2.46532 18.2845 2.30342C18.2365 2.25895 18.1799 2.22479 18.1181 2.20304C18.0564 2.18129 17.9909 2.1724 17.9256 2.17693C17.8603 2.18146 17.7966 2.19931 17.7385 2.22937C17.6803 2.25944 17.629 2.30109 17.5876 2.35176C17.5843 2.35574 17.581 2.35966 17.5775 2.36353L8.47338 12.6499C8.43874 12.689 8.39667 12.7209 8.3496 12.7436C8.30254 12.7664 8.25143 12.7795 8.19923 12.7824C8.14704 12.7852 8.0948 12.7776 8.04556 12.7601C7.99632 12.7425 7.95105 12.7154 7.91239 12.6802L4.89089 9.93063C4.57708 9.64296 4.16692 9.48324 3.74121 9.48291Z" fill="white"/>
          </svg>
          {{ (isEdit ? 'COMMON.UPDATE' : 'COMMON.ADD') | translate }}
        </button>
      </div>
 </form>
  </div>
</div>
