<div class="dialog-container">
  <h2 class=" header">
    {{ data.isResolution ? ('INVESTMENT_FUNDS.VOTING.DECISION_COMMENTS' | translate) :
    ('INVESTMENT_FUNDS.VOTING.ITEM_COMMENTS' | translate) }}
  </h2>
<hr>
  <div class="form-fields">
    <div class="form-container">
      <label class="required mb-3 ">{{'INVESTMENT_FUNDS.VOTING.VOTING_COMMENTS' | translate}} </label>
      <textarea
        class="form-control form-control-solid"
        type="text"
        [(ngModel)]="body"
        name="body"  maxlength="500"
        #note="ngModel"
        placeholder="{{'INVESTMENT_FUNDS.VOTING.VOTING_COMMENTS' | translate}}"
        required
        ></textarea>
      <div *ngIf="note.invalid && note.touched"
        class="text-danger">
        <div *ngIf="note.errors?.['required']">{{'FORM.ERROR_REQUIRED' | translate}}</div>
      </div>
      <div *ngIf="note.errors?.['maxlength'] && note.touched" class="text-danger">
         {{ 'INVESTMENT_FUNDS.RESOLUTIONS.MAX_LENGTH_ERROR'| translate: { max: 500 } }}
</div>
    </div>
  </div>
<hr>
  <div class="dialog-actions">
      <app-custom-button [btnName]="'COMMON.CANCEL' | translate" (click)="onCancel()" [buttonType]="buttonEnum.Secondary"
        [iconName]="IconEnum.cancel">
      </app-custom-button>

    <app-custom-button [btnName]="'INVESTMENT_FUNDS.VOTING.ADD_COMMENT' | translate" (click)="addNote()" [buttonType]="buttonEnum.Primary"
        [iconName]="IconEnum.verify">
      </app-custom-button>
  </div>
</div>


