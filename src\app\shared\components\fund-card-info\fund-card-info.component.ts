import { Component, EventEmitter, Input, NO_ERRORS_SCHEMA, Output, output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { Router } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'app-fund-card-info',
  standalone: true,
  imports: [CommonModule, RouterModule,TranslateModule],
  templateUrl: './fund-card-info.component.html',
  styleUrls: ['./fund-card-info.component.scss'],
    schemas: [NO_ERRORS_SCHEMA],

})
export class FundCardInfoComponent {
  @Input() notificationCount: number = 0;
  @Input() fundCount: number = 0;
  @Input() title: string = 'القرارات';
  @Input() icon: string = 'fas fa-file-alt';
  @Input() routerLink: string = '/';
  @Input() disabled:boolean = false;
  @Input() queryParams: any;
  @Input() moduleId: number = 0;

  @Output() filterNotification = new EventEmitter<number>();

  constructor(private router: Router) {}

  navigate() {

    if (!this.disabled && this.routerLink) {
      this.router.navigate([this.routerLink], {queryParams: this.queryParams });
    }
  }
filterNotificationHandle() {
  this.filterNotification.emit(this.moduleId);
}
}
