// Member Response Component Styles
// Following JadwaUI design patterns and RTL/LTR support
@import "../../../../../assets/scss/variables";

.breadcrumb-section {
  margin-bottom: 24px;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 500;
  line-height: 1.2;

  .dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    margin: 0px;
    margin-inline-end: 6px;
  }

  &.status-green {
    background-color: #f1faf1;
    color: #27ae60;

    .dot {
      background-color: #27ae60;
    }
  }

  &.status-blue {
    background-color: #e5eefb;
    color: #2f80ed;

    .dot {
      background-color: #2f80ed;
    }
  }

  &.status-orange {
    background-color: #fdf1eb;
    color: #ff5f3d;

    .dot {
      background-color: #ff5f3d;
    }
  }

  &.status-red {
    background-color: #FFEBED;
    color: #C50F1F;

    .dot {
      background-color: #C50F1F;
    }
  }

  &.status-grey {
    background-color: #e0e0e0;
    color: #828282;

    .dot {
      background-color: #828282;
    }
  }

  &.status-purple {
    background-color: #f3f0ff;
    color: #7c3aed;

    .dot {
      background-color: #7c3aed;
    }
  }
}

// Text truncation with tooltip support
.truncated-text {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.4;
  max-height: calc(1.4em * 3); // 3 lines
  cursor: pointer;
  word-wrap: break-word;
  word-break: break-word;

  // RTL support
  :root[dir="rtl"] & {
    text-align: right;
    direction: rtl;
  }

  :root[dir="ltr"] & {
    text-align: left;
    direction: ltr;
  }

  // Responsive behavior
  @media (max-width: 768px) {
    -webkit-line-clamp: 2;
    max-height: calc(1.4em * 2); // 2 lines on mobile
  }

  @media (max-width: 480px) {
    -webkit-line-clamp: 1;
    max-height: calc(1.4em * 1); // 1 line on small mobile
  }

  // Hover effect for better UX
  &:hover {
    color: #2f80ed;
    transition: color 0.2s ease;
  }
}

.header-container {
  display: flex;

  .title-container {
    .title {
      color: $navy-blue;
      font-size: 24px;
      font-style: normal;
      font-weight: 700;
      line-height: 20px;
      // margin-bottom: 17px;
    }
    .sub-title {
      color: #4f4f4f;

      font-size: 12px;
      font-weight: 400;
      line-height: 24px;
      margin-bottom: 33px;
      span {
        color: $navy-blue;
        font-size: 14px;
        font-weight: 500;
        line-height: 20px;
        margin-right: 8px;
      }
    }
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;

  .spinner-border {
    width: 3rem;
    height: 3rem;
  }

  p {
    color: #666;
    margin-top: 16px;
  }
}

// Resolution Details Container (matching assessment-details structure)
.resolution-details-container {
  background: #FFFFFF;
  border-radius: 12px;
  border: 1px solid #E5E7EB;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  .resolution-details-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;

    .header-actions {
      display: flex;
      gap: 12px;

      button {
        background: none;
        border: none;
        cursor: pointer;
        padding: 8px;
        color: #666;
        transition: color 0.2s ease;

        &:hover {
          color: #333;
        }
        .fa-pencil {
          color: #eaa300;
        }
        .expand {
          color: #00205A;
        }
        i {
          font-size: 16px;
        }
      }
    }

    .section-title {
      font-size: 18px;
      font-weight: 700;
      margin: 0;
      line-height: 22px;
      display: flex;
      align-items: center;

      &.navy-color {
        color: #00205A;
      }

      span {
        border-radius: 14px;
        background: rgba(38, 86, 135, 0.12);
        color: var(--Color---Black-1, #000);
        font-size: 16px;
        font-weight: 400;
        line-height: 18px;
        display: flex;
        padding: 8px;
        justify-content: center;
        align-items: center;
        gap: 10px;
        margin-right: 10px;
        margin-left: 10px;
      }
    }
  }

  hr {
    border: none;
    border-top: 1px solid #E5E7EB;
    margin: 20px 0;
  }

  .resolution-details-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;

    &.expanded {
      max-height: fit-content;
    }

    .info-item {
      margin-bottom: 24px;

     .info-label {
      color: $text-grey;
      font-size: 14px;
      font-weight: 700;
      line-height: 20px;
    }
    .description-text {
      max-width: 100%; // or set a fixed width like 250px
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      display: block;
    }

 .info-value {
      color: $navy-blue;
      font-size: 16px;
      font-weight: 500;
      line-height: 16px;
    }
      

      &.full-width {
        width: 100%;
      }
    }
  }
}

// Response Instructions
.response-instructions-container {
  margin-bottom: 24px;

  .alert {
    border-radius: 12px;
    border: 1px solid #3B82F6;
    background: linear-gradient(135deg, #EFF6FF 0%, #DBEAFE 100%);
    color: #1E40AF;
    padding: 20px;
    font-size: 14px;
    font-weight: 500;
    line-height: 1.5;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);

    i {
      color: #3B82F6;
      font-size: 16px;
      margin-right: 8px;
    }
  }
}

// Questions Response Container (now using resolution-details-container)
// The questions container now inherits from .resolution-details-container above

.resolution-details-container .response-form {
    .question-response-card {
      background: #FFFFFF;
      border: 2px solid #E5E7EB;
      border-radius: 12px;
      padding: 24px;
      margin-bottom: 24px;
      transition: all 0.3s ease;
      position: relative;

      &:hover {
        border-color: #3B82F6;
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
        transform: translateY(-2px);
      }

      &.answered {
        border-color: #10B981;
        background: linear-gradient(135deg, #FEFFFE 0%, #F0FDF4 100%);

        &:hover {
          border-color: #059669;
          box-shadow: 0 4px 12px rgba(16, 185, 129, 0.15);
        }
      }

      &:last-child {
        margin-bottom: 0;
      }

      .question-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 20px;
        gap: 16px;

        .question-title {
          font-size: 17px;
          font-weight: 700;
          color: #111827;
          margin: 0;
          line-height: 1.4;
          flex: 1;
          display: flex;
          align-items: center;
          gap: 12px;
          flex-wrap: wrap;

          .question-number {
            font-weight: 700;
          }

          .text-danger {
            color: #DC2626;
            margin-left: 6px;
            font-size: 18px;
          }

          .answered-indicator {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            background: linear-gradient(135deg, #ECFDF5 0%, #D1FAE5 100%);
            color: #059669;
            padding: 4px 10px;
            border-radius: 16px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border: 1px solid #10B981;

            i {
              font-size: 12px;
            }
          }
        }

        .question-type-badge {
          background: linear-gradient(135deg, #EFF6FF 0%, #DBEAFE 100%);
          color: #1E40AF;
          padding: 6px 14px;
          border-radius: 20px;
          font-size: 11px;
          font-weight: 600;
          text-transform: uppercase;
          letter-spacing: 0.5px;
          border: 1px solid #3B82F6;
          white-space: nowrap;
        }
      }

      .question-body {
        .question-text {
          font-size: 15px;
          color: #374151;
          margin-bottom: 24px;
          line-height: 1.6;
          font-weight: 500;
          background: #F9FAFB;
          padding: 16px;
          border-radius: 8px;
          border-left: 4px solid #3B82F6;
        }

        // Single Choice Options
        .single-choice-options {
          .form-check {
            margin-bottom: 16px;
            padding: 12px 16px;
            background: #F9FAFB;
            border-radius: 8px;
            border: 1px solid #E5E7EB;
            transition: all 0.2s ease;

            &:hover {
              background: #F3F4F6;
              border-color: #3B82F6;
            }

            .form-check-input {
              margin-right: 12px;
              margin-left: 0;
              width: 18px;
              height: 18px;
              border: 2px solid #D1D5DB;
              cursor: pointer;

              &:checked {
                background-color: #3B82F6;
                border-color: #3B82F6;
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
              }

              &:focus {
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
              }

              &:disabled {
                background-color: #F3F4F6;
                border-color: #D1D5DB;
                cursor: not-allowed;
                opacity: 0.6;
              }

              &:hover:not(:disabled) {
                border-color: #9CA3AF;
              }
            }

            .form-check-label {
              font-size: 14px;
              color: #374151;
              cursor: pointer;
              line-height: 1.5;
              font-weight: 500;
              flex: 1;
              transition: color 0.2s ease;

              &:hover {
                color: #111827;
              }
            }

            &:has(.form-check-input:disabled) .form-check-label {
              color: #9CA3AF;
              cursor: not-allowed;
            }
          }
        }

        // Multiple Choice Options
        .multiple-choice-options {
          .form-check {
            margin-bottom: 16px;
            padding: 12px 16px;
            background: #F9FAFB;
            border-radius: 8px;
            border: 1px solid #E5E7EB;
            transition: all 0.2s ease;

            &:hover {
              background: #F3F4F6;
              border-color: #3B82F6;
            }

            .form-check-input {
              margin-right: 12px;
              margin-left: 0;
              width: 18px;
              height: 18px;
              border: 2px solid #D1D5DB;
              cursor: pointer;

              &:checked {
                background-color: #3B82F6;
                border-color: #3B82F6;
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
              }

              &:focus {
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
              }

              &:disabled {
                background-color: #F3F4F6;
                border-color: #D1D5DB;
                cursor: not-allowed;
                opacity: 0.6;
              }

              &:hover:not(:disabled) {
                border-color: #9CA3AF;
              }
            }

            .form-check-label {
              font-size: 14px;
              color: #374151;
              cursor: pointer;
              line-height: 1.5;
              font-weight: 500;
              flex: 1;
              transition: color 0.2s ease;

              &:hover {
                color: #111827;
              }
            }

            &:has(.form-check-input:disabled) .form-check-label {
              color: #9CA3AF;
              cursor: not-allowed;
            }
          }
        }

        // Readonly Mode - Selected Options Highlighting
        // Enhanced visual indication for selected options in readonly mode
        .single-choice-options,
        .multiple-choice-options {
          .form-check {
            // Selected option styling for readonly mode
            &:has(.form-check-input:disabled:checked) {
              background: linear-gradient(135deg, #EFF6FF 0%, #DBEAFE 100%);
              border: 1px solid #3B82F6;
              box-shadow: none;

              .form-check-label {
                color: #1E40AF;
                font-weight: 600;
              }
            }

            // Fallback for browsers that don't support :has()
            .form-check-input:disabled:checked {
              background-color: #3B82F6 !important;
              border-color: #1E40AF !important;
              opacity: 1 !important;
              box-shadow: none !important;
            }

            // Enhanced styling for the parent container when input is selected and disabled
            .form-check-input:disabled:checked + .form-check-label {
              color: #1E40AF !important;
              font-weight: 600 !important;
            }
          }

          // Alternative approach using JavaScript-added classes (if needed)
          .form-check.readonly-selected {
            background: linear-gradient(135deg, #EFF6FF 0%, #DBEAFE 100%);
            border: 1px solid #3B82F6;
            box-shadow: none;

            .form-check-label {
              color: #1E40AF;
              font-weight: 600;


            }
          }
        }



        // Text Answer Input
        .text-answer-input {
          .form-control {
            border: 2px solid #D1D5DB;
            border-radius: 8px;
            padding: 16px;
            font-size: 14px;
            resize: vertical;
            min-height: 120px;
            font-family: inherit;
            line-height: 1.5;
            transition: all 0.2s ease;

            &:focus {
              border-color: #3B82F6;
              box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
              outline: none;
              background: #FEFEFE;
            }

            &::placeholder {
              color: #9CA3AF;
              font-style: italic;
            }

            &:hover {
              border-color: #9CA3AF;
            }

            // Enhanced styling for readonly text answers with content
            &:disabled:not(:placeholder-shown) {
              background: linear-gradient(135deg, #EFF6FF 0%, #DBEAFE 100%);
              border: 1px solid #3B82F6;
              color: #1E40AF;
              font-weight: 500;
              box-shadow: none;
              opacity: 1;
            }

            // Alternative approach for text areas with content
            &:disabled[data-has-content="true"] {
              background: linear-gradient(135deg, #EFF6FF 0%, #DBEAFE 100%);
              border: 1px solid #3B82F6;
              color: #1E40AF;
              font-weight: 500;
              box-shadow: none;
              opacity: 1;
            }
          }

          // Readonly text answer indicator
          .readonly-text-indicator {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            background: linear-gradient(135deg, #ECFDF5 0%, #D1FAE5 100%);
            border: 1px solid #10B981;
            border-radius: 8px;
            font-size: 12px;
            animation: fadeInUp 0.3s ease-out;

            i {
              font-size: 14px;
              filter: drop-shadow(0 1px 2px rgba(16, 185, 129, 0.3));
            }

            .text-primary {
              color: #059669 !important;
            }

            .fw-semibold {
              font-weight: 600;
            }
          }
        }

        // Validation Error
        .text-danger {
          font-size: 12px;
          margin-top: 12px;
          font-weight: 600;
          color: #DC2626;
          display: flex;
          align-items: center;
          gap: 6px;

          &::before {
            content: "⚠";
            font-size: 14px;
          }
        }
      }
    }
  }


// Sidebar Styles
.attachment-section,
.members-section {
  background: #fff;
  border-radius: 8px;
  border: 1px solid #E5E7EB;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  height: fit-content;

  .title {
    font-size: 16px;
    font-weight: 600;
    color: #00205A;
    margin: 0 0 16px 0;
    display: flex;
    align-items: center;
    gap: 8px;

    .member-count {
      color: #666;
      font-size: 16px;
      font-weight: 500;
    }

    span {
      border-radius: 14px;
      background: rgba(38, 86, 135, 0.12);
      color: var(--Color---Black-1, #000);
      font-size: 16px;
      font-weight: 400;
      padding: 5px 8px;
      margin-right: 10px;
      margin-left: 10px;
    }
  }

  .sub-title {
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    margin: 0 0 12px 0;
    padding: 8px 0;

    .attachment-number {
      background: #EFF6FF;
      color: #1E40AF;
      padding: 2px 8px;
      border-radius: 12px;
      font-size: 12px;
      margin-left: 8px;
    }
  }

  hr {
    border-color: #E5E7EB;
    margin: 16px 0;
  }
}

// Members Section Styling
.members-section {
  .members-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin: 12px 0px;
  }

  .member-card {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: 8px;
    transition: all 0.2s ease;

    .member-info {
      display: flex;
      align-items: center;
      gap: 12px;

      .member-avatar {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        overflow: hidden;
        background: #f5f5f5;
        display: flex;
        align-items: center;
        justify-content: center;

        .avatar-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .member-details {
        .member-name {
          font-size: 14px;
          font-weight: 400;
          color: #00205A;
          margin-bottom: 4px;
          line-height: 22px;
        }

        .member-role {
          font-size: 12px;
          color: #666;
          margin: 0;
          line-height: 1.2;
        }
      }
    }

    .voting-status {
      display: flex;
      flex-direction: column;
      .update-link {
        background: none;
        border: none;
        color: #0F6CBD;
        text-decoration: underline;
        font-size: 12px;
        cursor: pointer;
        padding: 0;

        &:hover {
          color: #0a5299;
        }
      }
    }
  }

  .response-statistics {
    display: flex;
    gap: 8px;
    justify-content: space-between;
    margin-top: 16px;

    .summary-card {
      display: flex;
      flex-direction: column;
      padding: 12px;
      padding-top: 8px;
      border-radius: 12px;
      width: 33%;

      .summary-count {
        font-size: 18px;
        font-weight: 500;
      }

      .summary-label {
        font-size: 12px;
        font-weight: 500;
        line-height: 20px;
        margin-bottom: 5px;
      }

      &.pending-summary {
        background: #EAA3002E;
        color: #CC910B;
      }

      &.submitted-summary {
        background: #27AE602E;
        color: #27AE60;
      }

      &.expected-summary {
        background: #3B82F62E;
        color: #2563EB;
      }
    }
  }
}

// RTL Support
[dir="rtl"] {
  .header-container {
    .rotate-icon {
      transform: scaleX(-1);
      margin-right: 0;
      margin-left: 16px;
    }
  }

  .response-instructions-container {
    .alert {
      i {
        margin-right: 0;
        margin-left: 8px;
      }
    }
  }

  .response-form {
    .question-response-card {
      .question-header {
        .question-title {
          .text-danger {
            margin-left: 0;
            margin-right: 6px;
          }
        }
      }

      .question-body {
        .single-choice-options,
        .multiple-choice-options {
          .form-check {
            .form-check-input {
              margin-right: 0;
              margin-left: 12px;
            }

            // RTL support for readonly selected options
            &:has(.form-check-input:disabled:checked),
            &.readonly-selected {
              // Maintain the same visual styling in RTL
              background: linear-gradient(135deg, #EFF6FF 0%, #DBEAFE 100%);
              border: 1px solid #3B82F6;
              box-shadow: none;
            }
          }
        }
      }
    }
  }

  .attachment-section {
    .sub-title {
      .attachment-number {
        margin-left: 0;
        margin-right: 8px;
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .breadcrumb-section {
    margin-bottom: 16px;
  }

  .header-container {
    flex-direction: column;
    align-items: flex-start !important;
    padding: 20px;

    .rotate-icon {
      margin-right: 12px;
      width: 36px;
      height: 36px;
    }

    .title-container {
      .title {
        font-size: 18px;
      }

      .sub-title {
        font-size: 13px;
      }
    }

    .dialog-actions {
      width: 100%;
      justify-content: flex-start !important;
      margin-top: 20px;
      flex-wrap: wrap;
      gap: 8px;
    }
  }

  .resolution-details-container {
    padding: 20px;
    margin-bottom: 20px;
  }

  .resolution-details-content {
    .info-item {
      margin-bottom: 20px;

      .info-label {
        font-size: 11px;
      }

      .info-value {
        font-size: 14px;
      }
    }
  }

  .response-instructions-container {
    margin-bottom: 20px;

    .alert {
      padding: 16px;
      font-size: 13px;
    }
  }

  .resolution-details-container .response-form {
    .question-response-card {
      padding: 20px;
      margin-bottom: 20px;

      .question-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;

        .question-title {
          font-size: 16px;
        }

        .question-type-badge {
          font-size: 10px;
          padding: 5px 12px;
        }
      }

      .question-body {
        .question-text {
          font-size: 14px;
          padding: 14px;
          margin-bottom: 20px;
        }

        .single-choice-options,
        .multiple-choice-options {
          .form-check {
            padding: 10px 14px;
            margin-bottom: 12px;

            .form-check-label {
              font-size: 13px;
            }
          }
        }

        .text-answer-input {
          .form-control {
            padding: 14px;
            min-height: 100px;
            font-size: 13px;
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .header-container {
    padding: 16px;

    .title-container {
      .title {
        font-size: 16px;
      }
    }

    .dialog-actions {
      flex-direction: column;
      width: 100%;

      app-custom-button {
        width: 100%;
      }
    }
  }

  .resolution-details-container {
    padding: 16px;
  }

  .resolution-details-container .response-form {
    .question-response-card {
      padding: 16px;

      .question-header {
        .question-title {
          font-size: 15px;
        }
      }
    }
  }
}

// Animation and Transitions
.question-response-card {
  animation: fadeInUp 0.4s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Loading Animation
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  background: #FFFFFF;
  border-radius: 12px;
  border: 1px solid #E5E7EB;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  .spinner-border {
    width: 3rem;
    height: 3rem;
    border-width: 3px;
    color: #3B82F6;
  }

  p {
    color: #6B7280;
    margin-top: 20px;
    font-size: 15px;
    font-weight: 500;
  }
}

// Focus States
.form-check-input:focus,
.form-control:focus {
  outline: none;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
}

// Hover Effects
.question-response-card:hover {
  .question-type-badge {
    background: linear-gradient(135deg, #DBEAFE 0%, #BFDBFE 100%);
    transform: scale(1.05);
  }
}

// Custom Scrollbar
.text-answer-input .form-control {
  scrollbar-width: thin;
  scrollbar-color: #CBD5E1 #F1F5F9;

  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: #F1F5F9;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: #CBD5E1;
    border-radius: 4px;

    &:hover {
      background: #94A3B8;
    }
  }
}
