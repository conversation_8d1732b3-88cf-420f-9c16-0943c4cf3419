import { Component, Input, Output, EventEmitter, HostListener } from '@angular/core';
import { RouterModule, Router } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { AuthService } from '../../../../../features/auth/services/auth-service/auth.service';
import { ErrorModalService } from '@core/services/error-modal.service';
import { TokenService } from 'src/app/features/auth/services/token.service';
import { CommonModule } from '@angular/common';
import { AuthenticationServiceProxy, SignOutCommand, NotificationServiceProxy } from '@core/api/api.generated';

@Component({
  selector: 'app-admin-layout-side-nav',
  standalone: true,
  imports: [RouterModule, TranslateModule,CommonModule],
  providers: [AuthService],
  templateUrl: './admin-layout-side-nav.component.html',
  styleUrls: ['./admin-layout-side-nav.component.scss']
})
export class AdminLayoutSideNavComponent {
  @Input() isMobileOpen = false;
  @Output() mobileClose = new EventEmitter<void>();

  isLoading = false;
  hasError = false;
  requestsCount = 0;

  constructor(
    private authService: AuthService,
    private router: Router,
    private errorModalService: ErrorModalService,
    private translateService: TranslateService,
    public tokenService: TokenService,
    private apiClient: AuthenticationServiceProxy,
    private notificationService: NotificationServiceProxy
  ) {
    this.loadRequestsCount();
  }

  // Close mobile sidebar
  closeMobileSidebar(): void {
    this.mobileClose.emit();
  }

  // Close sidebar when clicking on navigation links on mobile
  onNavLinkClick(): void {
    if (this.isMobile()) {
      this.closeMobileSidebar();
    }
  }

  // Check if current screen is mobile
  private isMobile(): boolean {
    return window.innerWidth <= 768;
  }

  // Listen for window resize to handle responsive behavior
  @HostListener('window:resize', ['$event'])
  onResize(event: any): void {
    // Close mobile sidebar if screen becomes desktop size
    if (!this.isMobile() && this.isMobileOpen) {
      this.closeMobileSidebar();
    }
  }

  loadRequestsCount(): void {
    this.isLoading = true;
    this.hasError = false;

    // Get unread notifications count
    // this.notificationService.notitficationList().subscribe({
    //   next: (response) => {
    //     this.isLoading = false;
    //     if (response && response.successed) {
    //       this.requestsCount = response.data || 0;
    //     } else {
    //       this.hasError = true;
    //       this.handleError('COMMON.ERROR_LOADING_DATA');
    //     }
    //   },
    //   error: (error) => {
    //     this.isLoading = false;
    //     this.hasError = true;
    //     this.handleError('COMMON.ERROR_LOADING_DATA');
    //     console.error('Error loading requests count:', error);
    //   }
    // });
  }

  private handleError(messageKey: string): void {
    this.translateService.get(messageKey).subscribe(message => {
      this.errorModalService.showError(message);
    });
  }

  onLogout(): void {
  this.apiClient.signOut(new SignOutCommand()).subscribe({
    next: () => {
      this.authService.logout();
      this.router.navigate(['/auth/login']).then(() => {
        this.errorModalService.showSuccess(this.translateService.instant('LOGIN_PAGE.LOGOUT_SUCCESS'));
      });
    },
    error: () => {
      this.errorModalService.showError(this.translateService.instant('LOGIN_PAGE.LOGOUT_FAILED'));
    }
  });
}

}
