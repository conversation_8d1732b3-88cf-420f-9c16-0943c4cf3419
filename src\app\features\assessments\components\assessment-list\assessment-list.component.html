<div class="assessments-page">
  <!-- Breadcrumb Section -->
  <div class="breadcrumb-section">
    <app-breadcrumb
      (onClickEvent)="onBreadcrumbClicked($event)"
      [breadcrumbs]="breadcrumbItems"
      [size]="breadcrumbSizeEnum.Medium"
      divider=">">
    </app-breadcrumb>
  </div>

  <!-- Page Header Section -->
  <div class="page-header-section">
    <app-page-header
      [title]="'ASSESSMENTS.TITLE' | translate"
      [showSearch]="true"
      [showFilter]="true"
      [showCreateButton]="canCreate()"
      [searchPlaceholder]="'ASSESSMENTS.SEARCH_PLACEHOLDER' | translate"
      [createButtonText]="'ASSESSMENTS.CREATE_NEW' | translate"
      (search)="onSearch($event)"
      (filter)="onFilter()"
      (create)="addNewAssessment()">
    </app-page-header>
  </div>

  <!-- Content Section -->
  <div class="content-section">
    <!-- Loading State (using global loader) -->
    <div class="loading-state" *ngIf="isLoading">
      <!-- Global loading mechanism handles this -->
    </div>

    <!-- Assessments Grid -->
    <div class="assessments-grid" *ngIf="!isLoading && !hasError">
      <!-- Assessment Card -->
      <div class="assessment-card" *ngFor="let assessment of assessments">
        <div class="card-header">
          <h3 class="assessment-title">
            <span class="title">{{ 'ASSESSMENTS.ASSESSMENT_TITLE' | translate }}:</span> {{ assessment.title }}
          </h3>
          <div class="card-actions">
            <!-- View Details Button -->
            <button class="action-btn details-btn"
                    *ngIf="canViewDetails(assessment)"
                    (click)="viewAssessmentDetails(assessment.id)"
                    [title]="'ASSESSMENTS.VIEW_DETAILS' | translate">
              <img src="assets/images/eye.png" alt="details" />
            </button>

           <!-- Edit Button -->
            <button class="action-btn"
                    *ngIf="canEdit(assessment)"
                    (click)="edit(assessment.id)"
                    [title]="'ASSESSMENTS.Edit' | translate">
              <img src="assets/images/edit.png" alt="edit" />
            </button>

            
           <!-- Delete Button -->
            <button class="action-btn"
                    *ngIf="canDelete(assessment)"
                    (click)="delete(assessment.id)"
                    [title]="'ASSESSMENTS.Delete' | translate">
              <img src="assets/images/trash.png" alt="delete" />
            </button>
          </div>
        </div>

        <div class="card-content">
          <p class="title">{{ 'ASSESSMENTS.COLUMNS.TYPE' | translate }}</p>
          <p class="assessment-type">{{ getTypeDisplayName(assessment.type) }}</p>

          <p class="title">{{ 'ASSESSMENTS.COLUMNS.CREATED_BY' | translate }}</p>
          <p class="assessment-created-by">{{ assessment.createdByName || '-' }}</p>

          <div class="assessment-meta">
            <div class="meta-item">
              <span class="meta-label">{{ 'ASSESSMENTS.COLUMNS.CREATED_DATE' | translate }}:</span>
              <p class="gregorian mb-0">{{ assessment.createdAt ? (assessment.createdAt.toString() | date: 'd/M/y') : '-' }}</p>
              <p class="hijri title">{{ assessment.createdAt ? (assessment.createdAt.toString() | dateHijriConverter) : '-' }}</p>
            </div>
            <div class="status">
              <span class="status" [ngClass]="getStatusClass(assessment.status)">
                {{ getStatusTranslationKey(assessment.status) | translate }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Pagination Section -->
    <div class="pagination-section" *ngIf="totalCount > 0">
      <!-- Pagination Controls -->
      <div class="pagination-controls">
       
        <!-- Previous Page Button -->
        <button class="pagination-btn prev-btn"
                [disabled]="!canGoPrevious()"
                (click)="onPreviousPage()"
                [title]="'PAGINATION.PREVIOUS' | translate">
          <!-- <i class="fas fa-angle-left"></i> -->
          <img [src]="isEnglish() ? 'assets/images/arrow-left2.png' : 'assets/images/arrow-right.png'" class="mx-2" alt="previous">

          <span class="btn-text">{{ 'PAGINATION.PREVIOUS' | translate }}</span>
        </button>

        <!-- Page Numbers -->
        <div class="page-numbers">
          <button *ngFor="let page of getPageNumbers()"
                  class="pagination-btn page-number-btn"
                  [class.active]="page === currentPage"
                  (click)="onPageChange(page)">
            {{page}}
          </button>
        </div>

        <!-- Next Page Button -->
        <button class="pagination-btn next-btn"
                [disabled]="!canGoNext()"
                (click)="onNextPage()"
                [title]="'PAGINATION.NEXT' | translate">
          <span class="btn-text">{{ 'PAGINATION.NEXT' | translate }}</span>
          <!-- <i class="fas fa-angle-right"></i> -->
          <img [src]="isEnglish() ? 'assets/images/arrow-right.png' : 'assets/images/arrow-left2.png'" class="mx-2" alt="next">

        </button>
      </div>

    </div>

    <!-- No Data State -->
    <div *ngIf="totalCount == 0 || hasError && !isLoading"
         class="d-flex flex-column gap-4 justify-content-center mt-5 align-items-center">
      <img src="assets/images/nodata.png" width="350" alt="No data">

      <ng-container>
        <p class="text-center mt-3 header fs-20">{{ 'ASSESSMENTS.NO_DATA' | translate }}</p>
        <app-custom-button
          *ngIf="canCreate()"
          class="mt-3"
          [btnName]="'ASSESSMENTS.CREATE_FIRST' | translate"
          [iconName]="createButtonIcon.plus"
          (click)="addNewAssessment()">
        </app-custom-button>
      </ng-container>
    </div>

  </div>
</div>
