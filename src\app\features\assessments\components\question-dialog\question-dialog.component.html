<div class="question-dialog">
  <!-- Dialog Header -->
  <div class="dialog-header">
    <h2 class="dialog-title">{{ getDialogTitle() | translate }}</h2>
    <button
      type="button"
      class="close-btn"
      (click)="onCancel()"
      [attr.aria-label]="'COMMON.CLOSE' | translate">
      <i class="fas fa-times"></i>
    </button>
  </div>

  <!-- Dialog Content -->
  <div class="dialog-content">
    <!-- Basic Question Form -->
    <div class="question-form-section">
      <app-form-builder 
        [formControls]="formControls" 
        [formGroup]="formGroup" 
        [isFormSubmitted]="isValidationFire">
      </app-form-builder>
    </div>

    <!-- Options Section (for Choice questions) -->
    <div class="options-section" *ngIf="isChoiceType()">
      <div class="options-header">
        <h4 class="options-title">{{ 'ASSESSMENTS.OPTIONS' | translate }}</h4>
        <p class="options-description">
          {{ 'ASSESSMENTS.OPTIONS_DESCRIPTION' | translate }}
        </p>
      </div>

      <div class="options-list">
        <div class="option-item"
             *ngFor="let optionGroup of optionsFormArray.controls; let i = index"
             [formGroup]="$any(optionGroup)">
          <div class="option-input-group">
            <div class="option-number">{{ i + 1 }}</div>

            <div class="option-input-container">
              <input
                type="text"
                class="form-control option-input"
                [class.is-invalid]="isValidationFire && optionGroup.get('text')?.invalid"
                formControlName="text"
                [placeholder]="('ASSESSMENTS.OPTION_PLACEHOLDER' | translate) + ' ' + (i + 1)"
                maxlength="200">
              <div class="invalid-feedback" *ngIf="isValidationFire && optionGroup.get('text')?.invalid">
                {{ 'ASSESSMENTS.VALIDATION.OPTION_REQUIRED' | translate }}
              </div>
            </div>
            <button
              type="button"
              class="btn btn-sm btn-outline-danger remove-option-btn"
              (click)="removeOption(i)"
              [title]="'ASSESSMENTS.REMOVE_OPTION' | translate">
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </div>
      </div>

      <!-- Add Option Button -->
      <div class="add-option-section" *ngIf="canAddOption()">
        <button 
          type="button"
          class="btn btn-outline-primary btn-sm add-option-btn"
          (click)="addOption()"
          [title]="'ASSESSMENTS.ADD_OPTION' | translate">
          <i class="fas fa-plus"></i>
          {{ 'ASSESSMENTS.ADD_OPTION' | translate }}
        </button>
      </div>

      <!-- Options Limit Info -->
      <div class="options-info" *ngIf="optionsFormArray.length >= maxOptions">
        <small class="text-muted">
          <i class="fas fa-info-circle"></i>
          {{ 'ASSESSMENTS.MAX_OPTIONS_REACHED' | translate: {max: maxOptions} }}
        </small>
      </div>
    </div>
  </div>

  <!-- Dialog Actions -->
  <div class="dialog-actions">
    <app-custom-button 
      [btnName]="'COMMON.CANCEL' | translate" 
      [buttonType]="buttonEnum.Secondary"
      [iconName]="IconEnum.cancel"
      (click)="onCancel()">
    </app-custom-button>

    <app-custom-button
      [btnName]="getSubmitButtonText() | translate"
      [buttonType]="buttonEnum.Primary"
      [iconName]="data.isEdit ? IconEnum.verify : IconEnum.plus"
      (click)="onSubmit()">
    </app-custom-button>
  </div>
</div>
