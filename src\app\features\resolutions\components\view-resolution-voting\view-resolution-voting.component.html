 <div class="breadcrumb-section">
  <app-breadcrumb
    (onClickEvent)="onBreadcrumbClicked($event)"
    [breadcrumbs]="breadcrumbItems"
    [size]="breadcrumbSizeEnum.Medium"
    divider=">">
  </app-breadcrumb>
</div>
<div class="d-flex justify-content-between">
  <div
    class="header-container w-100 d-flex align-items-center justify-content-between mb-3">
    <div class="d-flex align-items-center">
      <span class="rotate-icon mx-2" (click)="goBack()">
        <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22"
          viewBox="0 0 22 22" fill="none">
          <path fill-rule="evenodd" clip-rule="evenodd"
            d="M11.0572 0.75H10.9428C8.75212 0.749987 7.03144 0.749976 5.68802 0.930594C4.31137 1.11568 3.21911 1.50272 2.36091 2.36091C1.50271 3.21911 1.11568 4.31137 0.930593 5.68802C0.749975 7.03144 0.749987 8.75212 0.75 10.9428V11.0572C0.749987 13.2479 0.749975 14.9686 0.930593 16.312C1.11568 17.6886 1.50271 18.7809 2.36091 19.6391C3.21911 20.4973 4.31137 20.8843 5.68802 21.0694C7.03144 21.25 8.75212 21.25 10.9428 21.25H11.0572C13.2479 21.25 14.9686 21.25 16.312 21.0694C17.6886 20.8843 18.7809 20.4973 19.6391 19.6391C20.4973 18.7809 20.8843 17.6886 21.0694 16.312C21.25 14.9686 21.25 13.2479 21.25 11.0572V10.9428C21.25 8.75212 21.25 7.03144 21.0694 5.68802C20.8843 4.31137 20.4973 3.21911 19.6391 2.36091C18.7809 1.50272 17.6886 1.11568 16.312 0.930594C14.9686 0.749976 13.2479 0.749987 11.0572 0.75ZM16.1121 2.41722C17.3224 2.57994 18.0454 2.88853 18.5784 3.42157C19.1115 3.95462 19.4201 4.67757 19.5828 5.8879C19.7484 7.11979 19.75 8.73963 19.75 11C19.75 13.2604 19.7484 14.8802 19.5828 16.1121C19.4201 17.3224 19.1115 18.0454 18.5784 18.5784C18.0454 19.1115 17.3224 19.4201 16.1121 19.5828C14.8802 19.7484 13.2604 19.75 11 19.75C8.73963 19.75 7.11979 19.7484 5.88789 19.5828C4.67757 19.4201 3.95462 19.1115 3.42157 18.5784C2.88853 18.0454 2.57994 17.3224 2.41722 16.1121C2.25159 14.8802 2.25 13.2604 2.25 11C2.25 8.73963 2.25159 7.11979 2.41722 5.8879C2.57994 4.67757 2.88853 3.95462 3.42157 3.42157C3.95462 2.88853 4.67757 2.57994 5.88789 2.41722C7.11979 2.25159 8.73963 2.25 11 2.25C13.2604 2.25 14.8802 2.25159 16.1121 2.41722Z"
            fill="#00205A" />
          <path fill-rule="evenodd" clip-rule="evenodd"
            d="M11.9622 7.97726C11.6735 8.27428 11.6802 8.74911 11.9773 9.03781C12.1388 9.19487 12.396 9.3971 12.6407 9.58933C12.6596 9.60416 12.6786 9.61906 12.6976 9.63405C12.9434 9.82696 13.2061 10.0333 13.4548 10.2439C13.4572 10.246 13.4595 10.248 13.4619 10.25L7 10.25C6.58579 10.25 6.25 10.5858 6.25 11C6.25 11.4142 6.58579 11.75 7 11.75L13.4619 11.75C13.4595 11.752 13.4572 11.754 13.4548 11.7561C13.2061 11.9667 12.9434 12.173 12.6976 12.3659C12.6786 12.3809 12.6596 12.3958 12.6407 12.4107C12.396 12.6029 12.1388 12.8051 11.9773 12.9622C11.6802 13.2509 11.6735 13.7257 11.9622 14.0227C12.2509 14.3198 12.7257 14.3265 13.0227 14.0378C13.114 13.9491 13.2958 13.8035 13.5672 13.5903C13.5869 13.5748 13.6069 13.5592 13.6272 13.5432C13.8693 13.3532 14.1534 13.1302 14.4245 12.9005C14.715 12.6543 15.0168 12.3787 15.2515 12.1032C15.369 11.9652 15.485 11.8096 15.5746 11.6422C15.661 11.4807 15.75 11.2583 15.75 11C15.75 10.7417 15.661 10.5193 15.5746 10.3578C15.485 10.1904 15.369 10.0348 15.2515 9.89679C15.0168 9.62131 14.715 9.34574 14.4245 9.09954C14.1534 8.8698 13.8693 8.64683 13.6272 8.45676C13.6069 8.44084 13.5869 8.42515 13.5672 8.40971C13.2958 8.19651 13.114 8.05089 13.0227 7.96219C12.7257 7.67349 12.2509 7.68023 11.9622 7.97726Z"
            fill="#00205A" />
        </svg>
      </span>
      <div class="title-container">
        <p class="title">
          {{'INVESTMENT_FUNDS.VOTING.MEMBER_VOTING_DETAILS' | translate }}
        </p>
        <p class="sub-title"
          *ngIf="resolution!= null && resolution.parentResolutionId != null">
          {{'INVESTMENT_FUNDS.RESOLUTIONS.CODE_RELATED_DECISION' | translate }}:
          <span class="referral-code-link" (click)="onReferralCodeClick()" title="Click to view related resolution">
            {{resolution?.parentResolutionCode}}
          </span>

        </p>
      </div>

    </div>
    <!-- Dialog Actions -->
    <!-- Dialog Actions -->
    <div class="dialog-actions d-flex justify-content-end mt-4">
      <app-custom-button *ngIf="resolution?.isBoardMember && !resolution.resolutionMemberVote.hasRevoteRequest && resolution.statusId == 7"
        [btnName]="'INVESTMENT_FUNDS.VOTING.REQUEST_REVOTE' | translate"
        (click)="requestRevote()"
        class="mx-2" [buttonType]="buttonEnum.Primary"
        [iconName]="IconEnum.reload">
      </app-custom-button>
    </div>
  </div>
</div>

<!-- Main Content -->

<div class="row">
  <div class=" col-lg-8">
     <div  *ngIf="!resolution?.isBoardMember && resolution?.resolutionMemberVote?.hasRevoteRequest"  class="dialog-top-actions d-flex mb-3">
        <p class="title mb-0">{{'INVESTMENT_FUNDS.VOTING.REQUEST_REVOTE' | translate}}</p>
      <!-- Confirm/Reject buttons for Fund Manager -->
       <div class="btn-container">
       <app-custom-button
        [btnName]="'INVESTMENT_FUNDS.VOTING.REJECT' | translate"
        (click)="approveRejectReVote(false)" class="mx-2"
        [buttonType]="buttonEnum.Danger" [iconName]="IconEnum.cancelWhite">
      </app-custom-button>
      <app-custom-button  [btnName]="'INVESTMENT_FUNDS.VOTING.AGREE' | translate"
        (click)="approveRejectReVote(true)" class="mx-2"
        [buttonType]="buttonEnum.Success" [iconName]="IconEnum.verify">
      </app-custom-button>
      </div>
    </div>
  <div class="members-section d-flex mb-3">
   <div class="member-note-header">
    <div class="img-container">
      <img src="assets/images/avatar-member.png" alt="Member Avatar"
        class="avatar-img">
    </div>
    <div class="d-flex justify-content-between align-items-center">
    <div class="member-container">
      <div class="member-note-user-info">
        <span class="user-name">
          {{resolution?.resolutionMemberVote?.boardMemberName}}
        </span>
      </div>
      <div class="member-note-role d-flex justify-content-between">
        <span class="user-role">
        {{resolution?.resolutionMemberVote?.boardMemberType}}
     </span>
      </div>
    </div>


</div>
  </div>
  <div class="confirmation-section">
      <p class="title d-flex align-items-center justify-content-center mb-0">
          <span class="status-badge mx-3" [ngClass]="getStatusVoteClass(resolution?.resolutionMemberVote?.voteResult)">
          <span class="dot"></span>
            {{ (resolution?.resolutionMemberVote?.voteResult === 2 ? 'INVESTMENT_FUNDS.VOTING.ItemVotingApproved' : resolution?.resolutionMemberVote?.voteResult === 3 ? 'INVESTMENT_FUNDS.VOTING.ItemVotingUnApproved' :  'INVESTMENT_FUNDS.VOTING.ItemVotingNotVotedYet') | translate }}
          </span>
      </p>
   </div>
    </div>

    <div class=" resolution-details-container">
      <div class="resolution-details-header">
        <p class="section-title navy-color">
          {{ 'FUND_DETAILS.BASIC_INFO' | translate }}
        </p>
        <div class="header-actions">
           <button class="expand-button" (click)="toggleExpand()">
            <span *ngIf="isExpandedAction">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="10" viewBox="0 0 16 10" fill="none">
                <path d="M13.5961 8.67437C13.4697 8.50224 13.0923 7.98838 12.8676 7.69215C12.4174 7.09886 11.8023 6.3105 11.1388 5.52448C10.472 4.73448 9.77144 3.9651 9.14909 3.39951C8.83703 3.1159 8.5629 2.9004 8.33669 2.76014C8.12394 2.62822 7.99827 2.60079 7.99827 2.60079C7.99827 2.60079 7.87628 2.62822 7.66354 2.76013C7.43733 2.9004 7.1632 3.1159 6.85114 3.3995C6.2288 3.9651 5.52826 4.73449 4.8614 5.52449C4.19789 6.31052 3.58281 7.09889 3.13265 7.69219C2.9079 7.98842 2.53103 8.50155 2.40463 8.67369C2.1499 9.02944 1.66248 9.10618 1.3166 8.84417C0.970723 8.58216 0.896834 8.08136 1.15157 7.7256L1.15347 7.72301C1.28596 7.54259 1.67694 7.01014 1.90632 6.70782C2.36664 6.10112 2.99969 5.28949 3.68679 4.47551C4.37053 3.66551 5.12309 2.83489 5.82114 2.20049C6.16928 1.8841 6.52218 1.5996 6.86042 1.38986C7.17732 1.19337 7.57855 0.999996 8.00012 1C8.42169 1 8.82291 1.19337 9.13981 1.38987C9.47806 1.5996 9.83095 1.8841 10.1791 2.20049C10.8771 2.83489 11.6297 3.6655 12.3134 4.4755C13.0005 5.28946 13.6336 6.10109 14.0939 6.70778C14.3234 7.0102 14.7142 7.54242 14.8465 7.72265L14.8483 7.72504C15.103 8.0808 15.0295 8.5821 14.6836 8.84411C14.3378 9.10612 13.8509 9.0301 13.5961 8.67437Z" fill="#00205A" stroke="#00205A" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </span>
            <span *ngIf="!isExpandedAction">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="10" viewBox="0 0 16 10" fill="none">
                <path d="M2.40387 1.32563C2.53027 1.49776 2.90766 2.01162 3.13242 2.30785C3.58257 2.90114 4.19765 3.6895 4.86116 4.47552C5.52803 5.26552 6.22856 6.0349 6.85091 6.60049C7.16297 6.8841 7.4371 7.0996 7.66331 7.23986C7.87606 7.37178 8.00173 7.39922 8.00173 7.39922C8.00173 7.39922 8.12372 7.37178 8.33646 7.23987C8.56267 7.09961 8.8368 6.8841 9.14886 6.6005C9.7712 6.0349 10.4717 5.26552 11.1386 4.47551C11.8021 3.68949 12.4172 2.90111 12.8673 2.30781C13.0921 2.01158 13.469 1.49845 13.5954 1.32632C13.8501 0.970558 14.3375 0.893825 14.6834 1.15583C15.0293 1.41784 15.1032 1.91864 14.8484 2.2744L14.8465 2.27699C14.714 2.45741 14.3231 2.98986 14.0937 3.29218C13.6334 3.89888 13.0003 4.71051 12.3132 5.52449C11.6295 6.33449 10.8769 7.16511 10.1789 7.79951C9.83072 8.1159 9.47782 8.4004 9.13958 8.61014C8.82268 8.80664 8.42145 9 7.99988 9C7.57831 9 7.17709 8.80663 6.86019 8.61013C6.52194 8.4004 6.16905 8.1159 5.82091 7.79951C5.12285 7.16511 4.3703 6.3345 3.68655 5.52451C2.99945 4.71054 2.36641 3.89891 1.90609 3.29222C1.67663 2.9898 1.2858 2.45758 1.15345 2.27735L1.1517 2.27496C0.896965 1.9192 0.970487 1.4179 1.31636 1.15589C1.66223 0.893884 2.14913 0.969901 2.40387 1.32563Z" fill="#00205A" stroke="#00205A" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </span>
            <!-- <img [src]="isExpandedAction ?'assets/images/accrdion_up.png'
                :'assets/images/accrdion_down.png'" alt="edit" style="width:
                14px;height: 8px;" /> -->
          </button>

          <!-- <button class="expand-button" (click)="toggleExpand()">
            <img [src]="isExpanded ?'assets/images/accrdion_up.png':'assets/images/accrdion_down.png'" alt="edit" style="width:14px;height: 8px;" />
          </button> -->

        </div>
      </div>
      <hr *ngIf="isExpanded" style="margin-bottom: 16PX;">
      <div class="resolution-details-content" [class.expanded]="isExpanded">
        <div class="row" style="margin-bottom: 28px;">
          <div class="col-md-3 info-item">
            <p class="info-label">{{ 'INVESTMENT_FUNDS.RESOLUTIONS.RESOLUTION_CODE' | translate }}</p>
            <p class="info-value">{{ resolution?.code }}</p>
          </div>

          <div class="col-md-3 info-item" *ngIf="resolution?.oldResolutionCode">
            <p class="info-label">{{ 'INVESTMENT_FUNDS.RESOLUTIONS.RESOLUTION_OLD_CODE' | translate }}</p>
            <p class="info-value">{{ resolution?.oldResolutionCode }}</p>
          </div>

          <!-- Note: Parent Resolution and Old Resolution Code properties are not available in current API model -->
          <!-- Resolution Date -->
          <div class="col-md-3 info-item">
            <p class="info-label">{{ 'INVESTMENT_FUNDS.RESOLUTIONS.DATE' | translate }}</p>
            <span class="gregorian">{{resolution?.resolutionDate | date:'d/M/y' }}</span>-
            <span class="hijri">{{resolution?.resolutionDate| dateHijriConverter}}</span>
          </div>

          <!-- Status -->
          <div class="col-md-3 info-item"
            *ngIf="resolution && resolution.statusId">
            <p class="info-label">{{ 'INVESTMENT_FUNDS.RESOLUTIONS.STATUS' | translate }}</p>
            <span class="status" [ngClass]="getStatusClass(resolution?.statusId)">
              {{resolution?.resolutionStatus?.localizedName | translate }}
            </span>
          </div>

          <!-- Resolution Type -->
          <div class="col-md-3 info-item">
            <p class="info-label">{{'INVESTMENT_FUNDS.RESOLUTIONS.TYPE' | translate }}</p>
            <p class="info-value">{{ resolution?.resolutionType?.localizedName}}</p>
          </div>

        </div>
        <div class="row" style="margin-bottom: 28px;">

          <div class="col-md-3 info-item"
            *ngIf="resolution?.resolutionType?.isOther">
            <p class="info-label">{{ 'INVESTMENT_FUNDS.RESOLUTIONS.TYPE_DECISION_ADDED' | translate }}</p>
            <p class="info-value">{{ resolution?.newType || 'N/A' }}</p>
          </div>

          <div class="col-md-3 info-item">
            <p class="info-label">{{'INVESTMENT_FUNDS.RESOLUTIONS.VOTING_METHODOLOGY' | translate}}</p>
            <p class="info-value">{{ resolution?.votingTypeDisplay }}</p>
          </div>

          <div class="col-md-3 info-item">
            <p class="info-label">{{'INVESTMENT_FUNDS.RESOLUTIONS.VOTING_RESULT' | translate}}</p>
            <p class="info-value">{{ resolution?.memberVotingResultDisplay}}</p>
          </div>
        </div>

        <!-- Description -->
        <div class="col-md-3 info-item full-width"
          *ngIf="resolution?.description">
          <p class="info-label">{{ 'INVESTMENT_FUNDS.RESOLUTIONS.DESCRIPTION' |
            translate }}</p>
          <!-- <p class="info-value">{{ resolution?.description }}</p> -->
          <p class="info-value description-text"
            [title]="resolution.description"> {{ resolution.description }}</p>

        </div>
      </div>
    </div>
    <div class=" resolution-details-container mt-3">
      <div class="resolution-details-header">
        <p class="section-title navy-color">
          {{ 'INVESTMENT_FUNDS.RESOLUTIONS.ITEMS' | translate }}
          <span> {{resolution?.resolutionMemberVote?.itemsVote.length }}{{
            'INVESTMENT_FUNDS.RESOLUTIONS.ITEMS_WITHOUT' | translate}}</span>
        </p>
        <div class="header-actions">
             <button class="expand-button" (click)="toggleExpandItems()">
            <span *ngIf="isExpandedItem">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="10" viewBox="0 0 16 10" fill="none">
                <path d="M13.5961 8.67437C13.4697 8.50224 13.0923 7.98838 12.8676 7.69215C12.4174 7.09886 11.8023 6.3105 11.1388 5.52448C10.472 4.73448 9.77144 3.9651 9.14909 3.39951C8.83703 3.1159 8.5629 2.9004 8.33669 2.76014C8.12394 2.62822 7.99827 2.60079 7.99827 2.60079C7.99827 2.60079 7.87628 2.62822 7.66354 2.76013C7.43733 2.9004 7.1632 3.1159 6.85114 3.3995C6.2288 3.9651 5.52826 4.73449 4.8614 5.52449C4.19789 6.31052 3.58281 7.09889 3.13265 7.69219C2.9079 7.98842 2.53103 8.50155 2.40463 8.67369C2.1499 9.02944 1.66248 9.10618 1.3166 8.84417C0.970723 8.58216 0.896834 8.08136 1.15157 7.7256L1.15347 7.72301C1.28596 7.54259 1.67694 7.01014 1.90632 6.70782C2.36664 6.10112 2.99969 5.28949 3.68679 4.47551C4.37053 3.66551 5.12309 2.83489 5.82114 2.20049C6.16928 1.8841 6.52218 1.5996 6.86042 1.38986C7.17732 1.19337 7.57855 0.999996 8.00012 1C8.42169 1 8.82291 1.19337 9.13981 1.38987C9.47806 1.5996 9.83095 1.8841 10.1791 2.20049C10.8771 2.83489 11.6297 3.6655 12.3134 4.4755C13.0005 5.28946 13.6336 6.10109 14.0939 6.70778C14.3234 7.0102 14.7142 7.54242 14.8465 7.72265L14.8483 7.72504C15.103 8.0808 15.0295 8.5821 14.6836 8.84411C14.3378 9.10612 13.8509 9.0301 13.5961 8.67437Z" fill="#00205A" stroke="#00205A" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </span>
            <span *ngIf="!isExpandedItem">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="10" viewBox="0 0 16 10" fill="none">
                <path d="M2.40387 1.32563C2.53027 1.49776 2.90766 2.01162 3.13242 2.30785C3.58257 2.90114 4.19765 3.6895 4.86116 4.47552C5.52803 5.26552 6.22856 6.0349 6.85091 6.60049C7.16297 6.8841 7.4371 7.0996 7.66331 7.23986C7.87606 7.37178 8.00173 7.39922 8.00173 7.39922C8.00173 7.39922 8.12372 7.37178 8.33646 7.23987C8.56267 7.09961 8.8368 6.8841 9.14886 6.6005C9.7712 6.0349 10.4717 5.26552 11.1386 4.47551C11.8021 3.68949 12.4172 2.90111 12.8673 2.30781C13.0921 2.01158 13.469 1.49845 13.5954 1.32632C13.8501 0.970558 14.3375 0.893825 14.6834 1.15583C15.0293 1.41784 15.1032 1.91864 14.8484 2.2744L14.8465 2.27699C14.714 2.45741 14.3231 2.98986 14.0937 3.29218C13.6334 3.89888 13.0003 4.71051 12.3132 5.52449C11.6295 6.33449 10.8769 7.16511 10.1789 7.79951C9.83072 8.1159 9.47782 8.4004 9.13958 8.61014C8.82268 8.80664 8.42145 9 7.99988 9C7.57831 9 7.17709 8.80663 6.86019 8.61013C6.52194 8.4004 6.16905 8.1159 5.82091 7.79951C5.12285 7.16511 4.3703 6.3345 3.68655 5.52451C2.99945 4.71054 2.36641 3.89891 1.90609 3.29222C1.67663 2.9898 1.2858 2.45758 1.15345 2.27735L1.1517 2.27496C0.896965 1.9192 0.970487 1.4179 1.31636 1.15589C1.66223 0.893884 2.14913 0.969901 2.40387 1.32563Z" fill="#00205A" stroke="#00205A" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </span>
            <!-- <img [src]="isExpandedAction ?'assets/images/accrdion_up.png'
                :'assets/images/accrdion_down.png'" alt="edit" style="width:
                14px;height: 8px;" /> -->
          </button>

          <!-- <button class="expand-button" (click)="toggleExpandItems()">
            <img [src]="isExpandedItem ?'assets/images/accrdion_up.png':'assets/images/accrdion_down.png'" alt="edit" style="width:14px;height: 8px;" />
          </button> -->

        </div>
      </div>
      <hr *ngIf="isExpandedItem" style="margin-bottom: 16PX;">
      <div class="resolution-details-content" [class.expanded]="isExpandedItem">
        <div class="row" style="margin-bottom: 14px;" *ngFor="let item of resolution?.resolutionMemberVote?.itemsVote;let i=index;">
          <div class="item-container col-12"  [ngStyle]="{background:item.voteResult == 0 ?'#FEFCF6':'#fff'}">
            <div class="top-section d-flex justify-content-between w-100 align-items-center">
                <p class="title d-flex align-items-center justify-content-center">
                {{'INVESTMENT_FUNDS.RESOLUTIONS.ITEM' | translate}} {{i+1}}

              <span class="status-badge mx-3"  *ngIf="item.voteResult != 0" [ngClass]="getStatusVoteClass(item.voteResult)">
                <span class="dot"></span>
                {{ (item.voteResult === 2 ? 'INVESTMENT_FUNDS.VOTING.ItemVotingApproved' : 'INVESTMENT_FUNDS.VOTING.ItemVotingUnApproved') | translate }}
              </span>
              </p>
               <div class="d-flex justify-content-between align-items-center">
                 <p class="item-conflict mb-0 mx-3"  *ngIf="item.voteResult == 0 ">
                 {{'INVESTMENT_FUNDS.VOTING.ITEM_CONFLICT' | translate }}
                 </p>
              </div>
            </div>
            <p class="sub-title">
              {{item?.description}}
            </p>
            <div class="btn-items-container" *ngIf="item.voteResult != 0 ">
               <button class="btn-action notes-btn" (click)="openNotes(item)" *ngIf="item.itemComments.length">
                <img src="assets/images/notes.png" alt="notes" />
                {{ 'INVESTMENT_FUNDS.VOTING.COMMENTS' | translate }}
              </button>

              <button class="btn-action add-note-btn" *ngIf="!item.itemComments.length" (click)="addNote(false,item)">
                <img src="assets/images/add.png" alt="add-note" />
                {{ 'INVESTMENT_FUNDS.VOTING.ADD_COMMENT' | translate }}
              </button>

            </div>
          </div>
        </div>
      </div>
    </div>
    <div class=" resolution-details-container mt-3" >
      <div class="resolution-details-header">
        <p class="section-title navy-color">
          {{ 'INVESTMENT_FUNDS.VOTING.COMMENTS' | translate }}
        </p>
        <div class="header-actions">
          <button class="expand-button" (click)="toggleExpandNotes()">
            <span *ngIf="isExpandedNotes">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="10" viewBox="0 0 16 10" fill="none">
                <path d="M13.5961 8.67437C13.4697 8.50224 13.0923 7.98838 12.8676 7.69215C12.4174 7.09886 11.8023 6.3105 11.1388 5.52448C10.472 4.73448 9.77144 3.9651 9.14909 3.39951C8.83703 3.1159 8.5629 2.9004 8.33669 2.76014C8.12394 2.62822 7.99827 2.60079 7.99827 2.60079C7.99827 2.60079 7.87628 2.62822 7.66354 2.76013C7.43733 2.9004 7.1632 3.1159 6.85114 3.3995C6.2288 3.9651 5.52826 4.73449 4.8614 5.52449C4.19789 6.31052 3.58281 7.09889 3.13265 7.69219C2.9079 7.98842 2.53103 8.50155 2.40463 8.67369C2.1499 9.02944 1.66248 9.10618 1.3166 8.84417C0.970723 8.58216 0.896834 8.08136 1.15157 7.7256L1.15347 7.72301C1.28596 7.54259 1.67694 7.01014 1.90632 6.70782C2.36664 6.10112 2.99969 5.28949 3.68679 4.47551C4.37053 3.66551 5.12309 2.83489 5.82114 2.20049C6.16928 1.8841 6.52218 1.5996 6.86042 1.38986C7.17732 1.19337 7.57855 0.999996 8.00012 1C8.42169 1 8.82291 1.19337 9.13981 1.38987C9.47806 1.5996 9.83095 1.8841 10.1791 2.20049C10.8771 2.83489 11.6297 3.6655 12.3134 4.4755C13.0005 5.28946 13.6336 6.10109 14.0939 6.70778C14.3234 7.0102 14.7142 7.54242 14.8465 7.72265L14.8483 7.72504C15.103 8.0808 15.0295 8.5821 14.6836 8.84411C14.3378 9.10612 13.8509 9.0301 13.5961 8.67437Z" fill="#00205A" stroke="#00205A" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </span>
            <span *ngIf="!isExpandedNotes">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="10" viewBox="0 0 16 10" fill="none">
                <path d="M2.40387 1.32563C2.53027 1.49776 2.90766 2.01162 3.13242 2.30785C3.58257 2.90114 4.19765 3.6895 4.86116 4.47552C5.52803 5.26552 6.22856 6.0349 6.85091 6.60049C7.16297 6.8841 7.4371 7.0996 7.66331 7.23986C7.87606 7.37178 8.00173 7.39922 8.00173 7.39922C8.00173 7.39922 8.12372 7.37178 8.33646 7.23987C8.56267 7.09961 8.8368 6.8841 9.14886 6.6005C9.7712 6.0349 10.4717 5.26552 11.1386 4.47551C11.8021 3.68949 12.4172 2.90111 12.8673 2.30781C13.0921 2.01158 13.469 1.49845 13.5954 1.32632C13.8501 0.970558 14.3375 0.893825 14.6834 1.15583C15.0293 1.41784 15.1032 1.91864 14.8484 2.2744L14.8465 2.27699C14.714 2.45741 14.3231 2.98986 14.0937 3.29218C13.6334 3.89888 13.0003 4.71051 12.3132 5.52449C11.6295 6.33449 10.8769 7.16511 10.1789 7.79951C9.83072 8.1159 9.47782 8.4004 9.13958 8.61014C8.82268 8.80664 8.42145 9 7.99988 9C7.57831 9 7.17709 8.80663 6.86019 8.61013C6.52194 8.4004 6.16905 8.1159 5.82091 7.79951C5.12285 7.16511 4.3703 6.3345 3.68655 5.52451C2.99945 4.71054 2.36641 3.89891 1.90609 3.29222C1.67663 2.9898 1.2858 2.45758 1.15345 2.27735L1.1517 2.27496C0.896965 1.9192 0.970487 1.4179 1.31636 1.15589C1.66223 0.893884 2.14913 0.969901 2.40387 1.32563Z" fill="#00205A" stroke="#00205A" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </span>
            <!-- <img [src]="isExpandedAction ?'assets/images/accrdion_up.png'
                :'assets/images/accrdion_down.png'" alt="edit" style="width:
                14px;height: 8px;" /> -->
          </button>


          <!-- <button class="expand-button" (click)="toggleExpandNotes()">
            <img [src]="isExpandedNotes ?'assets/images/accrdion_up.png':'assets/images/accrdion_down.png'" alt="edit" style="width:14px;height: 8px;" />
          </button> -->
        </div>
      </div>
      <hr *ngIf="isExpandedNotes" style="margin-bottom: 16PX;">
       <div class="resolution-details-content" *ngIf="resolution?.resolutionMemberVote?.voteComments" [class.expanded]="isExpandedNotes">
        <app-member-note *ngFor="let comment of resolution?.resolutionMemberVote?.voteComments;let i=index;"
          userName={{comment?.createdByName}}
         userRole={{comment?.userRoleOrBoardMemberType}}
          [timeAgo]="getNotificationTime(comment.createdAt)"
          commentText={{comment?.comment}}>
        </app-member-note>
      </div>
    <hr>
      <div class="d-flex justify-content-end align-items-center pt-2 ">
        <app-custom-button
          [btnName]="'INVESTMENT_FUNDS.VOTING.ADD_COMMENT' | translate"
          (click)="addNote(true)" [buttonType]="buttonEnum.OutLine"
          [iconName]="IconEnum.add">
        </app-custom-button>
      </div>
    </div>
  </div>
  <div class="attachment-section col-lg-4">
    <p class="title">
      {{ 'INVESTMENT_FUNDS.RESOLUTIONS.FILES' | translate }}
    </p>
    <hr>
    <p class="sub-title">
      {{ 'INVESTMENT_FUNDS.RESOLUTIONS.DECISION_FILE' | translate }}
    </p>
    <div class="mb-3">
      <app-attachment-card
        [attachment]="resolution?.attachment"></app-attachment-card>

    </div>

    <hr>
    <div>
      <p class="sub-title">
        {{ 'INVESTMENT_FUNDS.RESOLUTIONS.ATTACHMENTS' | translate }}
        <span
          class="attachment-number">{{resolution?.otherAttachments?.length}}</span>
      </p>
      <app-attachment-card
        [attachment]="resolution?.otherAttachments"></app-attachment-card>
    </div>
  </div>
</div>
