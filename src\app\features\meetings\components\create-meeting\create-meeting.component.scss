@import "../../../../../assets/scss/variables.scss";

.create-resolution-page {
  // Align with fund components styling
  .form-container {
    padding: 2rem;
    background: #f8fafc;
    border-radius: 8px;
    border: 0.5px solid #dce0e3;
    .header {
      color:  #00205a;
      font-size: 16px;
      font-weight: 700;
      line-height: 22px;
    }
    .hr-first{
      margin: 24px 0 5px;
      border: 1px solid #E5E7EB;
    }
    .hr-last{
      border: 1px solid #E5E7EB;
      margin: 32px 0 16px;
    }
    .form-group {
      margin-bottom: 1.5rem;

      &.col-12 {
        width: 100%;
      }

      &.col-md-6 {
        width: 48%;
        margin-inline-end: 2%;
        &:nth-child(2n) {
          margin-inline-end: 0;
        }
      }
    }
  }

  .actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 2rem;
  }

  // RTL support
  [dir="rtl"] & {
    .actions {
      justify-content: flex-start;
    }
  }

  // Responsive design
  @media (max-width: 768px) {
    .form-container {
      padding: 1rem;
    }

    .actions {
      flex-direction: column;
      gap: 0.5rem;

      ::ng-deep app-custom-button {
        width: 100%;
      }
    }
  }

  @media (max-width: 480px) {
    .form-container {
      padding: 0.75rem;
    }
  }
}

.form-section {
margin-bottom: 40px;

&:last-child {
    margin-bottom: 0;
}

.section-header {
    font-size: 16px;
    font-weight: 600;
    color: #00205a;
    margin-bottom: 20px;
    margin-top: 20px;  
    padding-bottom: 10px;
    // border-bottom: 2px solid #e0e0e0;

    h6 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #00205a;

    }
    .items-num{
    display: inline-flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    padding: 8px;
    border-radius: 14px;
    background: rgba(38, 86, 135, 0.12);
    color: #000;
    font-size: 16px;
    font-weight: 400;
    line-height: 18px;
    }
}

// Status display styling removed - now handled by form-builder component

// Form builder header styling
.header {
    font-size: 16px;
    font-weight: 600;
    color: #00205a;
    margin-bottom: 10px;
}

.hr-first{
    margin: 24px 0 15px;
    border: 1px solid #b6b6b6;
}
.hr-last{
    border: 1px solid #b6b6b6;
    margin: 32px 0 16px;
}

.section-divider {
    border: none;
    height: 1px;
    background: linear-gradient(to right, #e0e0e0, transparent);
    margin: 20px 0;
}



.empty-items {
    text-align: center;
    padding: 60px 20px;
    background: #fafbfc;
    border: 2px dashed #e0e0e0;
    border-radius: 12px;
    margin-bottom: 20px;

    .empty-icon {
    margin-bottom: 20px;

    i {
        font-size: 64px;
        color: #d0d7de;
    }
    }

    .empty-message {
    font-size: 16px;
    color: #656d76;
    margin-bottom: 24px;
    font-weight: 500;
    }

    .add-first-item-btn {
    background: #007bff;
    border-color: #007bff;
    color: white;
    padding: 12px 24px;
    font-weight: 600;
    border-radius: 8px;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s ease;

    &:hover {
        background: #0056b3;
        border-color: #004085;
        transform: translateY(-1px);
    }
    }
}

// Attachments Styles
.section-header {
    .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;

    .attachments-info {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;

        .attachments-count {
        color: #666;

        strong {
            color: #007bff;
        }
        }

        .remaining-count {
        color: #28a745;
        font-size: 12px;
        }
    }
    }
}

.attachments-container {
    margin-bottom: 20px;

    .attachment-card {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px;
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    margin-bottom: 12px;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        transform: translateY(-1px);
    }

    .attachment-icon {
        flex-shrink: 0;
        width: 48px;
        height: 48px;
        background: #fff5f5;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #dc3545;
        font-size: 20px;
    }

    .attachment-info {
        flex: 1;

        .attachment-name {
        font-size: 15px;
        font-weight: 600;
        color: #00205a;
        margin-bottom: 6px;
        word-break: break-word;
        line-height: 1.4;
        }

        .attachment-meta {
        display: flex;
        align-items: center;
        gap: 16px;
        font-size: 13px;
        color: #666;

        .file-size {
            font-weight: 500;
            color: #007bff;
        }

        .upload-date {
            color: #999;
        }
        }
    }

    .attachment-actions {
        display: flex;
        gap: 8px;

        .btn {
        padding: 6px 10px;
        font-size: 12px;
        border-radius: 6px;
        transition: all 0.2s ease;

        &.download-btn {
            border-color: #007bff;
            color: #007bff;

            &:hover {
            background: #007bff;
            color: white;
            }
        }

        &.delete-btn {
            border-color: #dc3545;
            color: #dc3545;

            &:hover {
            background: #dc3545;
            color: white;
            }
        }
        }
    }
    }
}

.empty-attachments {
    text-align: center;
    padding: 50px 20px;
    background: #fafbfc;
    border: 2px dashed #e0e0e0;
    border-radius: 12px;
    margin-bottom: 20px;

    .empty-icon {
    margin-bottom: 16px;

    i {
        font-size: 56px;
        color: #d0d7de;
    }
    }

    .empty-message {
    font-size: 15px;
    color: #656d76;
    margin: 0;
    font-weight: 500;
    }
}

.upload-section {
    text-align: center;
    padding: 20px;
    border: 2px dashed #007bff;
    border-radius: 8px;
    background: #f8f9ff;

    .upload-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    border-color: #007bff;
    color: #007bff;
    background: white;
    border-radius: 6px;
    transition: all 0.2s ease;

    &:hover {
        background: #007bff;
        color: white;
    }
    }

    .upload-info {
    margin-top: 10px;

        small {
        font-size: 11px;
        color: #666;
        }
    }
    }

    .max-reached-message {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    background: #fff3cd;
    color: #856404;
    border-radius: 6px;
    font-size: 14px;

    i {
        font-size: 16px;
    }
    }
}

@import "../../../../../../assets/scss/variables";

.members-section {
  // padding: 1rem;
  // max-width: 1200px;
  // margin: 0 auto;

  background-color: $card-background;
  border: 1px solid $border-color;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .info-item {
    .info-label {
      color: $text-grey;
      font-size: 14px;
      font-weight: 700;
      line-height: 20px;
    }
    .description-text {
      max-width: 100%; // or set a fixed width like 250px
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      display: block;
    }
    .info-value {
      color: $navy-blue;
      font-size: 16px;
      font-weight: 500;
      line-height: 16px;
    }
  }
}

.members-section {
    padding: 18px;
    height: fit-content;

  .title {
    color: $navy-blue;
    font-size: 18px;
    font-weight: 700;
    // margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;

    .member-count {
        border-radius: 14px;
        color: #000;
        background: rgba(38, 86, 135, 0.12);
        font-size: 14px;
        line-height: 18px;
        font-weight: 400;
        padding: 5px 8px;
    }
  }

  .members-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin: 12px 0px;
  }

  .member-card {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: 8px;
    transition: all 0.2s ease;

    .member-info {
      display: flex;
      align-items: center;
      gap: 12px;

      .member-avatar {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        overflow: hidden;
        background: #f5f5f5;
        display: flex;
        align-items: center;
        justify-content: center;

        .avatar-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .member-details {
        .member-name {
          font-size: 14px;
          font-weight: 400;
          color: $navy-blue;
          margin-bottom: 4px;
          line-height: 22px;

          &:hover {
            text-decoration: underline;
            cursor: pointer;
          }
        }

        .member-role {
          font-size: 12px;
          color: #666;
          margin: 0;
          line-height: 1.2;
        }
      }
    }

    .voting-status {
      display: flex;
      flex-direction: column;
      .action-link {
        background: none;
        border: none;
        color: #0F6CBD;
        text-decoration: underline;
        font-size: 12px;
        cursor: pointer;
        padding: 0;

        &:hover {
          color: #0a5299;
        }
      }

      // Status-specific styling
      &.approved {
        background: #e8f5e8;
        color: #27ae60;

        .status-indicator {
          background: #27ae60;
        }
      }

      &.rejected {
        background: #ffeaea;
        color: #e74c3c;

        .status-indicator {
          background: #e74c3c;
        }
      }

      &.conflicted {
        background: #fff3e0;
        color: #f39c12;

        .status-indicator {
          background: #f39c12;
        }
      }

      &.update-request {
        background: #e3f2fd;
        color: #2196f3;

        .status-indicator {
          background: #2196f3;
        }
      }

      &.not-voted {
        background: #f5f5f5;
        color: #828282;

        .status-indicator {
          background: #828282;
        }
      }
    }
  }

}

.voting-summary {
  display: flex;
  gap: 8px;
  justify-content: space-between;
  margin-top: 16px;

  .summary-card {
    // display: flex;
    // flex-direction: column;
    padding: 12px;
    padding-top: 8px;
    border-radius: 12px;
    width: 33%;

    .summary-count {
      font-size: 18px;
      font-weight: 500;
    }

    .summary-label {
      font-size: 12px;
      font-weight: 500;
      line-height: 20px;
      margin-bottom: 5px;
    }

    .view-summary{
      cursor: pointer;
      position: relative;
      top: 4px;
    }
  }
  .approved-summary {
    background: #27AE602E;
    color: #27AE60;
  }

  .rejected-summary {
    background: #C50F1F2E;
    color: #C50F1F;
  }

  .not-voted-summary {
    background: #EAA3002E;
    color: #CC910B;
  }
}

.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 400;
  line-height: 16px;

  .dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin: 0px;
    margin-inline-end: 6px;
  }

  &.status-green {
    background-color: #f1faf1;
    color: #27ae60;

    .dot {
      background-color: #27ae60;
    }
  }

  &.status-blue {
    background-color: #e5eefb;
    color: #2f80ed;

    .dot {
      background-color: #2f80ed;
    }
  }

  &.status-orange {
    background-color: #fdf1eb;
    color: #ff5f3d;

    .dot {
      background-color: #ff5f3d;
    }
  }

  &.status-red {
    background-color: #FFEBED;
    color: #C50F1F;

    .dot {
      background-color: #C50F1F;
    }
  }

  &.status-grey {
    background-color: #e0e0e0;
    color: #828282;

    .dot {
      background-color: #828282;
    }
  }
}


@media (max-width: 768px) {
  .members-section {
    .voting-summary {
      gap: 12px;

      .summary-card {
        flex-direction: row;
        justify-content: space-between;
        padding: 12px 16px;
        text-align: center;

        .summary-count {
          margin-bottom: 0;
        }
      }
    }
  }
}

::ng-deep{
  .swal2-popup{
    border-radius: 8px;
    .swal2-title{
      color: $navy-blue;
      font-size: 20px;
      font-weight: 400;
      line-height: 32px;
      padding: 0px;
      margin: 0px; 
    }
    .swal2-html-container{
      color: $dark;
      font-size: 16px;
      font-weight: 400;
      line-height: 24px; 
      padding: 0px;
      margin-top: 10px;
    }
  }
}
