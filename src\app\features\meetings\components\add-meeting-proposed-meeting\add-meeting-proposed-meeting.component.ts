import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { ButtonTypeEnum, IconEnum } from '@core/enums/icon-enum';
import { SizeEnum } from '@core/enums/size';
import { IBreadcrumbItem } from '@core/gl-interfaces/IBreadcrumbItem/ibreadcrumb-item';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';
import { CustomButtonComponent } from '@shared/components/custom-button/custom-button.component';
import { FormBuilderComponent } from '@shared/components/form-builder/form-builder.component';
import { InputType } from '@shared/enum/input-type.enum';
import { IControlOption } from '@shared/interfaces/i-control-option';
import { FileUploadComponent } from "@shared/components/file-upload/file-upload.component";
import { AppearanceEnum } from '@shared/enum/appearance-enum';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { CalendarModeEnum } from '@shared/enum/calender-mode';
import { DatepickerComponent } from '@shared/components/datepicker/datepicker.component';

@Component({
  selector: 'app-add-meeting-proposed-meeting',
  standalone: true,
  imports: [
    BreadcrumbComponent,
    CustomButtonComponent,
    TranslateModule,
    FormBuilderComponent,
    FormsModule,
    ReactiveFormsModule,
    CommonModule,
    FileUploadComponent,
    MatCheckboxModule,
   DatepickerComponent

],
  templateUrl: './add-meeting-proposed-meeting.component.html',
  styleUrl: './add-meeting-proposed-meeting.component.scss',
})
export class AddMeetingProposedMeetingComponent {
  breadcrumbSizeEnum = SizeEnum;
  breadcrumbItems: IBreadcrumbItem[] = [];
  fundId = 0;
  buttonEnum = ButtonTypeEnum;
  IconEnum = IconEnum;

  // Form properties
  formGroup!: FormGroup;
  formControls: IControlOption[] = [];
  isValidationFire = false;
  isFormSubmitted = false;
  slotsArray = [1, 2, 3, 4];

   appearance = AppearanceEnum;
   controlSize = SizeEnum;
  calendarMode= CalendarModeEnum;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    public translateService: TranslateService,
    private fb: FormBuilder
  ) {}

  ngOnInit(): void {
    this.initializeBreadcrumbs();
    this.route.queryParams.subscribe((queryParams) => {
      this.fundId = +queryParams['fundId'] || 0;
    });
   this.formGroup = this.fb.group({
      subject: ['', [Validators.required, Validators.maxLength(255)]],
      description: ['', [Validators.maxLength(1000)]],
      attachmentId: [null],
      slots: this.fb.group({
        slot1: this.createSlotGroup(),
        slot2: this.createSlotGroup(),
        slot3: this.createSlotGroup(),
        slot4: this.createSlotGroup(),
      }),
    });



  }

   private createSlotGroup(): FormGroup {
    return this.fb.group({
      checked: [false],
      date: [''],
      time: [''],
    });
  }


  onSubmit(): void {
    if (this.formGroup.valid) {
      console.log('Form Submitted:', this.formGroup.value);
    } else {
      console.warn('Form is invalid!');
    }
  }

  onBreadcrumbClicked(event: IBreadcrumbItem): void {
    if (!event.disabled && event.url) {
      this.router.navigateByUrl(event.url);
    }
  }

  private initializeBreadcrumbs(): void {
    this.breadcrumbItems = [
      { label: 'BREADCRUMB.HOME', url: '/admin/dashboard' },
      { label: 'BREADCRUMB.FUNDS', url: '/admin/investment-funds' },
      // {
      //   label: 'BREADCRUMB.FUND_DETAILS',
      //   url: `/admin/investment-funds/fund-details?id=${this.fundId}`,
      // },
      {
        label: '...',
        url: '', disabled: true
      },
      { label: 'INVESTMENT_FUNDS.MEETING.ADD', url: '', disabled: true },
    ];
  }
  cancel() {}
  sendToVoting() {}

  onFileUpload(file: any) {}

  handleFileUpload(file: File | File[] | null): void {
    console.log('Uploaded file(s):', file);
  }

  onDateSelected(date:any){}

}
