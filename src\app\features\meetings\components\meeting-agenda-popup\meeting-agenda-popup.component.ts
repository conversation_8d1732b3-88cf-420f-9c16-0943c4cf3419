import { CommonModule } from '@angular/common';
import { Component, Inject } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { FormBuilderComponent } from '@shared/components/form-builder/form-builder.component';
import { InputType } from '@shared/enum/input-type.enum';
import { IControlOption } from '@shared/interfaces/i-control-option';

export interface MeetingItemData {
  fundId: number;
  resolutionId: number;
  // item?: ResolutionItemDto; // For editing existing item
  // existingItems: ResolutionItemDto[];
  isEdit: boolean;
}

@Component({
  selector: 'app-meeting-agenda-popup',
  standalone: true,
  imports: [CommonModule, TranslateModule, FormBuilderComponent, ReactiveFormsModule],
  templateUrl: './meeting-agenda-popup.component.html',
  styleUrl: './meeting-agenda-popup.component.scss'
})
export class MeetingAgendaPopupComponent {
  isLoading: any;
  isSubmitting: any;
  isEdit: boolean = false;
  currentItem: any;
  existingItems: any = [];
  formGroup!: FormGroup;
  formControls: IControlOption[] = [
    {
      type: InputType.Text,
      formControlName: 'itemsubject',
      id: 'itemsubject',
      name: 'itemsubject',
      label: 'MEETINGS.AGENDA_ITEM',
      placeholder: 'MEETINGS.AGENDA_ITEM_PLACEHOLDER',
      isRequired: false,
      showOptional: false,
      class: 'col-md-12 pt-0',
    },
    {
      type: InputType.Text,
      formControlName: 'description',
      id: 'description',
      name: 'description',
      label: 'MEETINGS.AGENDA_ITEM_DESCRIPTION',
      placeholder: 'MEETINGS.AGENDA_ITEM_DESCRIPTION_PLACEHOLDER',
      isRequired: false,
      showOptional: false,
      class: 'col-md-12',
    }
  ];
  isValidationFire: boolean | undefined;
  isFormSubmitted: boolean | undefined;

  constructor(
    private formBuilder: FormBuilder,
    private translateService: TranslateService,
    public dialogRef: MatDialogRef<MeetingAgendaPopupComponent>,
    @Inject(MAT_DIALOG_DATA) public data: MeetingItemData
  ) {
    this.currentItem = {title: ""};
  }
  
  ngOnInit(): void {
    this.initForm();
  }

  initForm(): void {
    this.formGroup = this.formBuilder.group({
      title: [''],
      description: [''],
    });
  }

  get itemTitle(): string {
    if (this.isEdit && this.currentItem) {
      return this.currentItem.title || '';
    } else {
      const nextItemNumber = this.existingItems.length + 1;
      return this.translateService.instant('MEETINGS.ITEM') + ' ' + nextItemNumber;
    }
  }

  private isFormValid(): boolean {
    // Basic form validation
    if (this.formGroup.get('hasConflict')?.value &&
        (!this.formGroup.get('conflictMembers')?.value ||
         this.formGroup.get('conflictMembers')?.value.length === 0)) {
      return false;
    }
    return true;
  }

  onSubmit(): void {
      console.log('Form submitted');
      if (this.isSubmitting) return;
      this.isSubmitting = true;
      this.isFormSubmitted = true;
      this.isValidationFire = true;
  
      if (!this.isFormValid()) {
        this.isSubmitting = false;
        this.formGroup.markAllAsTouched();
        return;
      }
  
      const formValue = this.formGroup.value;
  
       this.dialogRef.close();
    }
  onCancel(): void {
    this.dialogRef.close();
  }
}
