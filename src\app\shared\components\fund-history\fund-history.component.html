<div class="fund-history-container" >
  <div class="tabs">

    <button class="tab-button" [class.active]="activeTab === 'history'"  (click)="setActiveTab('history')" >
      <img src="assets/images/calendar.png" alt="Fund Details" />
      {{ 'FUND_DETAILS.NOTIFICATIONS_LIST' | translate }}
    </button>
    <button class="tab-button" [class.active]="activeTab === 'details'"   (click)="setActiveTab('details')">
      <img src="assets/images/history.png" alt="Fund History" />
      {{ 'FUND_DETAILS.STATUS_HISTORY' | translate }}

     </button>
  </div>

  <div class="timeline-container">
    <div *ngIf="activeTab === 'history' && filteredItems.length ===0" >
      {{ 'FUND_DETAILS.NO_RECORDS' | translate }}
    </div>
    <div class="timeline-item" *ngFor="let item of filteredItems; let last = last">
      <!-- <div
        class="timeline-icon"
        [ngStyle]="{ backgroundColor: item.type === 'fundNotifications' ? '#27AE6029' : '#FEEDE8' }"
      > -->
        <img [src]="getIconForType(item.itemtype)" class="timeline-icon" [alt]="item.type" />
      <!-- </div> -->

      <div class="timeline-content" [ngClass]="{ active: item.status === 'active' }">
        <div class="d-flex justify-content-between">
          <div *ngIf="item.type === 'fundNotifications'">

          <h3 class="title">{{ item.title  }}</h3>
          <p class="description">{{ item.description }}</p>
        </div>
        <div *ngIf="item.type !== 'fundNotifications'">

          <h3 class="title">{{ item.displayedAction  }}</h3>
          <p class="description">{{ item.displayedUserRole + ": " + item.fullName }}</p>
        </div>
        <p  [ngClass]="getStatusClass(item.statusId)" *ngIf="item.type !== 'fundNotifications'" class="status">
        <span class="circle"></span>
          {{ item.status }}</p>
       </div>
        <div class="timestamp">
            <div class="time_formate">
          <span class="time"> {{ getPeriod(item) }} </span>
          <span class="time mx-1">
            {{ getTime(item) }} {{ formatTimetoOtherSide(item.time) }}
          </span>
          </div>
          <span class="date">
           {{item.createdAt | dateHijriConverter }}

           <span class="mx-2">
            {{ item.date }}
           </span>
          </span>

        </div>
      </div>
      <hr *ngIf="!last" />

    </div>

    <div class="btns_container">
      <button class="loadmore-btn" *ngIf="activeTab === 'history' && notificationTotalCount >10"  (click)="loadMoreNotifications()" >
        {{ 'COMMON.LoadMore' | translate }}
      </button>
      <button class="reset-btn" *ngIf="activeTab === 'history'"  (click)="resetNotificationFilter()" >
        <img src="assets/images/reset.png" alt="Fund History" />
        {{ 'COMMON.RESET' | translate }}
      </button>
    </div>

  </div>

</div>
