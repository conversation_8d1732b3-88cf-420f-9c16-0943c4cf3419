import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { ButtonTypeEnum, IconEnum } from '@core/enums/icon-enum';
import { SizeEnum } from '@core/enums/size';
import { IBreadcrumbItem } from '@core/gl-interfaces/IBreadcrumbItem/ibreadcrumb-item';
import { TranslateModule } from '@ngx-translate/core';
import { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';
import { CustomButtonComponent } from '@shared/components/custom-button/custom-button.component';
import { FormBuilderComponent } from '@shared/components/form-builder/form-builder.component';
import { PageHeaderComponent } from '@shared/components/page-header/page-header.component';
import { AttachmentModule } from '@shared/enum/AttachmentModule';
import { InputType } from '@shared/enum/input-type.enum';
import { MeetingPlaceEnum } from '@shared/enum/meeting-enums';
import { IControlOption } from '@shared/interfaces/i-control-option';

@Component({
  selector: 'app-create-meeting',
  standalone: true,
  imports: [
    CommonModule,
    BreadcrumbComponent,
    CustomButtonComponent,
    PageHeaderComponent,
    TranslateModule,
    FormBuilderComponent
  ],
  templateUrl: './create-meeting.component.html',
  styleUrl: './create-meeting.component.scss'
})
export class CreateMeetingComponent {
  breadcrumbItems: IBreadcrumbItem[] = [
    { label: 'BREADCRUMB.HOME', url: '/admin/dashboard' },
    { label: 'BREADCRUMB.FUNDS', url: '/admin/investment-funds' },
    { label: 'RESOLUTIONS.TITLE', url: '' },
  ];
  breadcrumbSizeEnum = SizeEnum;
  buttonEnum = ButtonTypeEnum;
  iconEnum = IconEnum;
  isValidationFire: boolean = false;
  isMeetingRoom: boolean = true;

  formGroup!: FormGroup;
  formControls: IControlOption[] = [
    {
      type: InputType.Text,
      formControlName: '',
      id: '',
      name: '',
      label: 'MEETINGS.MEETING_SUBJECT',
      placeholder: 'MEETINGS.ENTER_MEETING_SUBJECT',
      isRequired: true,
      class: 'col-md-6',
    },
    {
      type: InputType.Dropdown,
      formControlName: 'meetingTypeId',
      id: 'meetingTypeId',
      name: 'meetingTypeId',
      label: 'MEETINGS.MEETING_TYPE',
      placeholder: 'MEETINGS.MEETING_TYPE_PLACEHOLDER',
      isRequired: true,
      class: 'col-md-6',
      options: [], // Will be populated from API
      // onChange: (value: any) => this.dropdownChanged(value),
    },
    {
      type: InputType.Date,
      formControlName: 'fromDate',
      id: 'fromDate',
      name: 'fromDate',
      label: 'MEETINGS.MEETING_DATE',
      placeholder: 'MEETINGS.MEETING_DATE',
      isRequired: true,
      class: 'col-md-4',
    },
    {
      type: InputType.Time,
      formControlName: 'votingMethodologyId',
      id: 'votingMethodologyId',
      name: 'votingMethodologyId',
      label: 'MEETINGS.START_TIME',
      placeholder: 'MEETINGS.START_TIME',
      isRequired: true,
      class: 'col-md-4',
    },
    {
      type: InputType.Time,
      formControlName: 'votingMethodologyId',
      id: 'votingMethodologyId',
      name: 'votingMethodologyId',
      label: 'MEETINGS.END_TIME',
      placeholder: 'MEETINGS.END_TIME',
      isRequired: true,
      class: 'col-md-4',
    },

    {
      type: InputType.Radio,
      formControlName: 'votingResultCalculationId', /////////
      id: 'votingResultCalculationId',
      name: 'votingResultCalculationId',
      label: 'MEETINGS.PLACE',
      isRequired: true,
      class: 'col-md-12',
      options: [
        { name: 'MEETINGS.MEETING_ROOM', id: MeetingPlaceEnum.MeetingRoom },
        { name: 'MEETINGS.VIA_INTERNET', id: MeetingPlaceEnum.ViaInternet},
      ],
      onChange: (value: any) => this.meetingPlaceChanged(value),
    },
    {
      type: InputType.Text,
      formControlName: 'description',
      id: 'description',
      name: 'description',
      label: 'MEETINGS.THE_MEETING_ROOM',
      placeholder: 'MEETINGS.MEETING_ROOM_PLACEHOLDER',
      isRequired: this.isMeetingRoom,
      class: 'col-md-12',
      isVisible: () => this.isMeetingRoom,
    },
    {
      type: InputType.Text,
      formControlName: 'description',
      id: 'description',
      name: 'description',
      label: 'MEETINGS.MEETING_LINK',
      placeholder: 'MEETINGS.MEETING_LINK',
      isRequired: true,
      class: 'col-md-12',
    },
    {
      type: InputType.Textarea,
      formControlName: 'description',
      id: 'description',
      name: 'description',
      label: 'MEETINGS.MEETING_DESCRIPTION',
      placeholder: 'MEETINGS.MEETING_DESCRIPTION_PLACEHOLDER',
      isRequired: false,
      class: 'col-md-12',
      max: 500,
    },
    {
      type: InputType.file,
      formControlName: 'attachmentId',
      id: 'attachmentId',
      name: 'attachmentId',
      label: '',
      placeholder: 'RESOLUTIONS.FILE_UPLOAD_TEXT',
      isRequired: true,
      class: 'col-md-12',
      allowedTypes: ['.pdf'],
      multiple: true,
      moduleId : AttachmentModule.Other
    },
  ];

  constructor(
    private router: Router,
    private formBuilder: FormBuilder,
  ) {}

  ngOnInit() {
    this.initializeForm();
  }

  onBreadcrumbClicked(item: IBreadcrumbItem): void {
    if (item.url && !item.disabled) {
      this.router.navigateByUrl(item.url);
    }
  }

  private initializeForm() {
    const formGroup: any = {};

    this.formControls.forEach((control) => {
      const validators = [];
      if (control.formControlName == '') return;
      if (control.isRequired) {
        validators.push(Validators.required);
      }
      if (control.minLength) {
        validators.push(Validators.minLength(control.minLength));
      }
      if (control.maxLength) {
        validators.push(Validators.maxLength(control.maxLength));
      }
      if (control.max) {
        validators.push(Validators.max(control.max));
      }
      if (control.min) {
        validators.push(Validators.min(control.min));
      }

      if (control.formControlName === 'votingResultCalculationId') {
        formGroup[control.formControlName] = [MeetingPlaceEnum.MeetingRoom, validators];
      }else{
        formGroup[control.formControlName] = [null, validators];
      }

    });
    this.formGroup = this.formBuilder.group(formGroup);
  }

  meetingPlaceChanged($event: any) {
    if($event.event.value == MeetingPlaceEnum.MeetingRoom){
      this.isMeetingRoom = true;
    }else{
      this.isMeetingRoom = false;
    }
  }

  onFileUpload($event: any) {}
  dateSelected($event: any) { }
  onValueChange($event: any ,arg1: IControlOption) {}
  onSubmit(arg0: boolean) {}
  onCancel() {}
}
