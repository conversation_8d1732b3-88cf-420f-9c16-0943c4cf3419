import {
  Component,
  EventEmitter,
  forwardRef,
  Input,
  OnChanges,
  Output,
  SimpleChanges,
  HostListener,
  ElementRef,
} from '@angular/core';
import { HijriDatepickerComponent } from './hijri-datepicker/hijri-datepicker.component';
import { CommonModule } from '@angular/common';
import {
  ControlValueAccessor,
  FormsModule,
  NG_VALUE_ACCESSOR,
  ReactiveFormsModule,
} from '@angular/forms';
import { GregorianDatepickerComponent } from './gregorian-datepicker/gregorian-datepicker.component';
import moment from 'moment';
import 'moment/locale/ar';
import momentHijri from 'moment-hijri';

import { NgbDateStruct } from '@ng-bootstrap/ng-bootstrap';
import { DateValues } from '@shared/gl-models/inputs/idate-model';
import { CalendarModeEnum } from '@shared/enum/calender-mode';
@Component({
  selector: 'arabdt-datepicker',
  standalone: true,
  imports: [
    HijriDatepickerComponent,
    CommonModule,
    FormsModule,
    GregorianDatepickerComponent,
    ReactiveFormsModule,
  ],
  templateUrl: './datepicker.component.html',
  styleUrls: ['./datepicker.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => DatepickerComponent),
      multi: true,
    },
  ],
})
export class DatepickerComponent implements ControlValueAccessor {
  constructor(private elementRef: ElementRef) {
    moment.locale('ar');
  }
  @Input() placeholder!: string;
  @Input() readonly: boolean | undefined;
  @Input() calendarMode!: CalendarModeEnum;
  @Input() value: NgbDateStruct | undefined;
  @Input() maxGreg: any;
  @Input() maxHijri: any;
  @Input() minGreg: any;
  @Input() minHijri: any;
  @Input() GregLabel: string | undefined;
  @Input() hijriLabel: string | undefined;

  @Input() isInvalid: boolean | undefined;

  @Output() onDateChange: EventEmitter<DateValues> =
    new EventEmitter<DateValues>();

  calendarType!: string;
  selectedDate: DateValues = new DateValues();

  calendarModeEnum = CalendarModeEnum;
  ngOnInit() {
    if (this.calendarMode == CalendarModeEnum.HIJRI) {
      this.calendarType = CalendarModeEnum.HIJRI;
    } else {
      this.calendarType = CalendarModeEnum.GREGORIAN;
    }
  }
  setCalendarTypeGregorian() {
    this.calendarType = CalendarModeEnum.GREGORIAN;
  }
  setCalendarTypeHijri() {
    this.calendarType = CalendarModeEnum.HIJRI;
  }
  onDateSelected(date: NgbDateStruct) {
    if (this.calendarType === CalendarModeEnum.GREGORIAN) {
      this.selectedDate.gregorian = date;
    } else {
      this.selectedDate.hijri = date;
    }
    this.value = date;
    this.onChange(this.value);
    this.setDateValue();
  }
  onBlur(event: Event) {
    this.onTouched();
  }

  setDateValue() {
    this.onDateChange.emit(this.selectedDate);
  }
  writeValue(value: NgbDateStruct): void {
    if (typeof value != 'string' && value !== null) {
      this.selectedDate.setGregorianDate(value);
      this.onGregorianSelected(value); // triggers conversion & emit
    }
   
  }
  validateDate() {
    this.isInvalid = !this.selectedDate.gregorian && !this.selectedDate.hijri;
  }
  registerOnChange(fn: any): void {
    this.onChange = fn;
  }
  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }
  setDisabledState(isDisabled: boolean): void {
    this.readonly = isDisabled;
  }
  onChange: any = () => {};
  onTouched: any = () => {};

  dateValues: DateValues = new DateValues();
  parseArabic(arabicnumstr: any): string {
    if (typeof arabicnumstr !== 'string') {
      arabicnumstr = String(arabicnumstr);
    }
    return arabicnumstr.replace(/[٠-٩]/g, (d: string) =>
      String(d.charCodeAt(0) - 1632)
    );
  }

  getCorrespondingMonth(monthOrder: any): number {
    return parseInt(this.parseArabic(monthOrder));
  }

  convertGregorianToHijri(gregorian: {
    day: number;
    month: number;
    year: number;
  }): { day: number; month: number; year: number } {
    momentHijri.locale('en'); // Ensure numbers are in English
    const formattedDate = `${gregorian.year}-${String(gregorian.month).padStart(
      2,
      '0'
    )}-${String(gregorian.day).padStart(2, '0')}`;
    const hijriDate = momentHijri(formattedDate, 'YYYY-MM-DD').format(
      'iYYYY-iMM-iDD'
    );
    const [year, month, day] = hijriDate
      .split('-')
      .map(this.getCorrespondingMonth.bind(this));
    return { day, month, year };
  }

  convertHijriToGregorian(hijri: {
    day: number;
    month: number;
    year: number;
  }): { day: number; month: number; year: number } {
    momentHijri.locale('en'); // Ensure numbers are in English
    const formattedDate = `${hijri.year}-${String(hijri.month).padStart(
      2,
      '0'
    )}-${String(hijri.day).padStart(2, '0')}`;
    const gregorianDate = momentHijri(formattedDate, 'iYYYY-iMM-iDD').format(
      'YYYY-MM-DD'
    );
    const [year, month, day] = gregorianDate
      .split('-')
      .map(this.getCorrespondingMonth.bind(this));
    return { day, month, year };
  }

  onGregorianSelected(gregDate: { day: number; month: number; year: number }) {
    this.calendarType = 'gregorian';
    this.selectedDate.gregorian = gregDate;
    this.selectedDate.hijri = this.convertGregorianToHijri(gregDate);
    this.selectedDate.updateFormattedDates();
    this.onDateChange.emit(this.selectedDate);
    this.closePickers();
  }

  onHijriSelected(hijriDate: { day: number; month: number; year: number }) {
    this.calendarType = 'hijri';
    this.selectedDate.hijri = hijriDate;
    this.selectedDate.gregorian = this.convertHijriToGregorian(hijriDate);
    this.selectedDate.updateFormattedDates();
    this.onDateChange.emit(this.selectedDate);
    this.closePickers();
  }

  onBlurOutside(event: FocusEvent) {
    const target = event.relatedTarget as HTMLElement;
    if (!target || !target.closest('.date-picker-container')) {
      this.closePickers();
    }
  }

  @HostListener('document:click', ['$event'])
  onClickOutside(event: Event) {
    const target = event.target as HTMLElement;
    // Check if the click is outside the datepicker component
    if (!this.elementRef.nativeElement.contains(target)) {
      this.closePickers();
    }
  }

  @HostListener('document:keydown.escape', ['$event'])
  onEscapeKey(event: KeyboardEvent) {
    if (this.showPickers) {
      this.closePickers();
      event.preventDefault();
    }
  }

  showPickers = false;

  togglePickers() {
    this.showPickers = !this.showPickers;
  }

  openPickers() {
    this.showPickers = true;
  }

  closePickers() {
    this.showPickers = false;
  }

  getFormattedDate(): string {
    if (!this.selectedDate?.gregorian || !this.selectedDate?.hijri) {
      return '';
    }

    const g = this.selectedDate.gregorian;
    const h = this.selectedDate.hijri;

    const gregorianFormatted = `${g.day}/${g.month}/${g.year} م`;
    const hijriFormatted = `${h.day}/${h.month}/${h.year} هـ`;

    return `${gregorianFormatted} - ${hijriFormatted}`;
  }

  convertToHijri(gregorianDate: string): string {
    momentHijri.locale('ar');

    const hijriDate = momentHijri(gregorianDate).format('iD-iMMMM-iYYYY');

    return hijriDate; // This will return the date in Arabic by default
  }

  resetDate() {
    this.selectedDate.hijri = undefined;
    this.selectedDate.gregorian = undefined;
  }
}
