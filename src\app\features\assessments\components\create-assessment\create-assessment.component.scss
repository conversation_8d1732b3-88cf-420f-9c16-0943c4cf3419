@import "../../../../../assets/scss/variables";

.create-assessment-page {
  padding: 20px;
  background-color: #f8f9fa;
  min-height: 100vh;

  .breadcrumb-section {
    margin-bottom: 20px;
  }

  .page-header-section {
    margin-bottom: 30px;
  }

  .main-content {
    .create-form-container {
      background: #F8FAFC;
      border-radius: 16px;
      border: 0.5px solid #DCE0E3;
      padding: 16px;

      .form-section {
        margin-bottom: 40px;

        &:last-child {
          margin-bottom: 0;
        }

        .section-header {
          font-size: 16px;
          font-weight: 600;
          color: #00205a;
          margin-bottom: 20px;
          margin-top: 20px;
          padding-bottom: 10px;

          .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 10px;

            // RTL support
            html[dir="rtl"] & {
              flex-direction: row-reverse;
            }
          }

          h6 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #00205a;
          }

          .items-num{
            display: inline-flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            padding: 8px;
            border-radius: 14px;
            background: rgba(38, 86, 135, 0.12);
            color: #000;
            font-size: 16px;
            font-weight: 400;
            line-height: 18px;
          }
        }

        // Form builder header styling
        .header {
          font-size: 16px;
          font-weight: 600;
          color: #00205a;
          margin-bottom: 10px;
        }

      }
    }
  }

  // Assessment Questions Styles - Following resolution items pattern
  .items-container {
    margin-bottom: 20px;

    .assessment-question-card {
      padding: 16px 10px;
      border-radius: 8px;
      background-color: white;
      margin-bottom: 16px;

      .item-header {
        .item-info {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .item-number {
            font-size: 20px;
            font-weight: 500;
            line-height: 32px;
            color: #00205a;
          }

          .item-actions {
            display: flex;
            gap: 8px;

            .btn {
              padding: 8px;
              &:hover{
                background: unset;
                border: unset;
                color: unset;
              }
            }
          }
        }
      }

      .item-body {
        .item-description {
          font-size: 14px;
          color: #333;
          margin-bottom: 8px;
          line-height: 1.4;
        }

        .question-type-info {
          display: flex;
          align-items: center;
          gap: 12px;

          .question-type-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            background: #e3f2fd;
            color: #1976d2;
          }

          .question-options {
            .options-count {
              font-size: 12px;
              color: #666;
            }
          }
        }
      }
    }
  }

  .empty-items {
    text-align: center;
    padding: 40px 20px;
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    margin-bottom: 20px;
  }

  .add-item-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    background: white;
    color: $navy-blue;
    border-radius: 8px;
    font-weight: 400;
    transition: all 0.2s ease;
    border: 1px solid #e0e0e0;

    &:hover {
      background: #f8f9fa;
      border-color: $navy-blue;
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 32, 90, 0.15);
    }

    &:disabled {
      opacity: 0.3;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }

    // RTL support
    html[dir="rtl"] & {
      flex-direction: row-reverse;
    }

    svg {
      flex-shrink: 0;
    }
  }

  .assessment-questions{
    max-height: 500px;
    overflow-y: auto;
  }

  // Form Actions
  .form-actions {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e0e0e0;

    .actions-container {
      display: flex;
      justify-content: flex-end;
      gap: 12px;

    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .create-assessment-page {
    padding: 15px;

    .main-content {
      .create-form-container {
        padding: 20px;

        .form-actions {
          .actions-container {
            flex-direction: column;

            app-custom-button {
              width: 100%;
            }
          }
        }
      }
    }
  }
}
