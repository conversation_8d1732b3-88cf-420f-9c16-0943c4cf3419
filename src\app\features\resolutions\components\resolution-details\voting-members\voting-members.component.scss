@import "../../../../../../assets/scss/variables";

.members-section {
  // padding: 1rem;
  // max-width: 1200px;
  // margin: 0 auto;

  background-color: $card-background;
  border: 1px solid $border-color;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .info-item {
    .info-label {
      color: $text-grey;
      font-size: 14px;
      font-weight: 700;
      line-height: 20px;
    }
    .description-text {
      max-width: 100%; // or set a fixed width like 250px
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      display: block;
    }
    .info-value {
      color: $navy-blue;
      font-size: 16px;
      font-weight: 500;
      line-height: 16px;
    }
  }
}

.members-section {
    padding: 18px;
    height: fit-content;

  .title {
    color: $navy-blue;
    font-size: 18px;
    font-weight: 700;
    // margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;

    .member-count {
        border-radius: 14px;
        color: #000;
        background: rgba(38, 86, 135, 0.12);
        font-size: 14px;
        line-height: 18px;
        font-weight: 400;
        padding: 5px 8px;
    }
  }

  .members-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin: 12px 0px;
  }

  .member-card {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: 8px;
    transition: all 0.2s ease;

    .member-info {
      display: flex;
      align-items: center;
      gap: 12px;

      .member-avatar {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        overflow: hidden;
        background: #f5f5f5;
        display: flex;
        align-items: center;
        justify-content: center;

        .avatar-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .member-details {
        .member-name {
          font-size: 14px;
          font-weight: 400;
          color: $navy-blue;
          margin-bottom: 4px;
          line-height: 22px;

          &:hover {
            text-decoration: underline;
            cursor: pointer;
          }
        }

        .member-role {
          font-size: 12px;
          color: #666;
          margin: 0;
          line-height: 1.2;
        }
      }
    }

    .voting-status {
      display: flex;
      flex-direction: column;
      .action-link {
        background: none;
        border: none;
        color: #0F6CBD;
        text-decoration: underline;
        font-size: 12px;
        cursor: pointer;
        padding: 0;

        &:hover {
          color: #0a5299;
        }
      }

      // Status-specific styling
      &.approved {
        background: #e8f5e8;
        color: #27ae60;

        .status-indicator {
          background: #27ae60;
        }
      }

      &.rejected {
        background: #ffeaea;
        color: #e74c3c;

        .status-indicator {
          background: #e74c3c;
        }
      }

      &.conflicted {
        background: #fff3e0;
        color: #f39c12;

        .status-indicator {
          background: #f39c12;
        }
      }

      &.update-request {
        background: #e3f2fd;
        color: #2196f3;

        .status-indicator {
          background: #2196f3;
        }
      }

      &.not-voted {
        background: #f5f5f5;
        color: #828282;

        .status-indicator {
          background: #828282;
        }
      }
    }
  }

}

.voting-summary {
  display: flex;
  gap: 8px;
  justify-content: space-between;
  margin-top: 16px;

  .summary-card {
    // display: flex;
    // flex-direction: column;
    padding: 12px;
    padding-top: 8px;
    border-radius: 12px;
    width: 33%;

    .summary-count {
      font-size: 18px;
      font-weight: 500;
    }

    .summary-label {
      font-size: 12px;
      font-weight: 500;
      line-height: 20px;
      margin-bottom: 5px;
    }

    .view-summary{
      cursor: pointer;
      position: relative;
      top: 4px;
    }
  }
  .approved-summary {
    background: #27AE602E;
    color: #27AE60;
  }

  .rejected-summary {
    background: #C50F1F2E;
    color: #C50F1F;
  }

  .not-voted-summary {
    background: #EAA3002E;
    color: #CC910B;
  }
}

.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 400;
  line-height: 16px;

  .dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin: 0px;
    margin-inline-end: 6px;
  }

  &.status-green {
    background-color: #f1faf1;
    color: #27ae60;

    .dot {
      background-color: #27ae60;
    }
  }

  &.status-blue {
    background-color: #e5eefb;
    color: #2f80ed;

    .dot {
      background-color: #2f80ed;
    }
  }

  &.status-orange {
    background-color: #fdf1eb;
    color: #ff5f3d;

    .dot {
      background-color: #ff5f3d;
    }
  }

  &.status-red {
    background-color: #FFEBED;
    color: #C50F1F;

    .dot {
      background-color: #C50F1F;
    }
  }

  &.status-grey {
    background-color: #e0e0e0;
    color: #828282;

    .dot {
      background-color: #828282;
    }
  }
}


@media (max-width: 768px) {
  .members-section {
    .voting-summary {
      gap: 12px;

      .summary-card {
        flex-direction: row;
        justify-content: space-between;
        padding: 12px 16px;
        text-align: center;

        .summary-count {
          margin-bottom: 0;
        }
      }
    }
  }
}

::ng-deep{
  .swal2-popup{
    border-radius: 8px;
    .swal2-title{
      color: $navy-blue;
      font-size: 20px;
      font-weight: 400;
      line-height: 32px;
      padding: 0px;
      margin: 0px; 
    }
    .swal2-html-container{
      color: $dark;
      font-size: 16px;
      font-weight: 400;
      line-height: 24px; 
      padding: 0px;
      margin-top: 10px;
    }
  }
}