import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDialog } from '@angular/material/dialog';
import Swal from 'sweetalert2';
// Shared Components
import { PageHeaderComponent } from '@shared/components/page-header/page-header.component';
import { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';
import { CustomButtonComponent } from '@shared/components/custom-button/custom-button.component';

// Core Services and Enums
import { TokenService } from '../../../auth/services/token.service';
import { IconEnum, ButtonTypeEnum } from '@core/enums/icon-enum';
import { SizeEnum } from '@shared/enum/size-enum';
import { IBreadcrumbItem } from '@core/gl-interfaces/IBreadcrumbItem/ibreadcrumb-item';
import { LanguageEnum } from '@core/enums/language-enum/language-enum';

import { ErrorModalService } from '@core/services/error-modal.service';
import { AssessmentAdvancedSearchDialogComponent, AssessmentSearchFilters } from '../assessment-advanced-search-dialog/assessment-advanced-search-dialog.component';

// NSwag-generated Assessment API
import {
  AssessmentServiceProxy,
  AssessmentStatus,
  AssessmentType,
  SingleAssessmentResponse,
  SingleAssessmentResponsePaginatedResult,
  FundAssessmentPermissionDto,
  FundAssessmentPermissionDtoBaseResponse
} from '@core/api/api.generated';

// Pipes
import { DateHijriConverterPipe } from '@shared/pipes/dateHijriConverter/dateHijriConverter.pipe';

// Date handling
import { DateTime } from 'luxon';

@Component({
  selector: 'app-assessment-list',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    PageHeaderComponent,
    BreadcrumbComponent,
    MatIconModule,
    MatButtonModule,
    TranslateModule,
    DateHijriConverterPipe,
    MatTooltipModule,
    CustomButtonComponent,
  ],
  templateUrl: './assessment-list.component.html',
  styleUrls: ['./assessment-list.component.scss']
})
export class AssessmentListComponent implements OnInit {
  // Data properties
  assessments: SingleAssessmentResponse[] = [];
  filteredAssessments: SingleAssessmentResponse[] = [];
  currentFundId = 0;
  currentFundName = '';
  assessmentStatus = AssessmentStatus;
  assessmentType = AssessmentType;



  // Pagination properties
  currentPage = 1;
  pageSize = 10;
  totalCount = 0;
  totalPages = 0;
  pageSizeOptions = [10, 25, 50, 100];

  // Loading and error states
  isLoading = false;
  hasError = false;
  errorMessage = '';
  createButtonIcon = IconEnum;
  buttonEnum = ButtonTypeEnum;

  // UI state
  breadcrumbSizeEnum = SizeEnum;
  Math = Math;

  // Breadcrumb configuration
  breadcrumbItems: IBreadcrumbItem[] = [];

  // Permission properties from new API
  fundPermissions: FundAssessmentPermissionDto | null = null;
  canCreateAssessment = false;
  isBoardMember = false;
  boardMemberId: number | undefined;
  allowedStatuses: AssessmentStatus[] = [];

  // Search and filter
  search = '';
  selectedStatus?: AssessmentStatus;
  currentSearchFilters: any = null;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private assessmentServiceProxy: AssessmentServiceProxy,
    public translateService: TranslateService,
    public tokenService: TokenService,
    private errorModalService: ErrorModalService,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.initializeComponent();
  }

  private initializeComponent(): void {
    this.getFundIdFromRoute();
  }

  private getFundIdFromRoute(): void {
    this.route.queryParams.subscribe(params => {
      this.currentFundId = +params['fundId'] || 0;
      if (this.currentFundId > 0) {
        this.loadFundPermissions();
        this.loadAssessments();
      } else {
        this.hasError = true;
        this.errorMessage = 'ASSESSMENTS.INVALID_FUND_ID';
      }
    });
  }

  private loadFundPermissions(): void {
    if (this.currentFundId <= 0) {
      console.warn('Invalid fund ID for loading permissions');
      return;
    }

    this.assessmentServiceProxy.fundPermission(this.currentFundId).subscribe({
      next: (response: FundAssessmentPermissionDtoBaseResponse) => {
        if (response.successed && response.data) {
          this.fundPermissions = response.data;
          this.canCreateAssessment = response.data.canAdd;
          this.isBoardMember = response.data.isBoardMember;
          this.boardMemberId = response.data.boardMemberId;

        } else {
          console.warn('Failed to load fund permissions:', response.message);
        }
      },
      error: (error) => {
        console.error('Error loading fund permissions:', error);
      }
    });
  }

  private updateBreadcrumb(): void {
    this.breadcrumbItems = [
      { label: 'BREADCRUMB.HOME', url: '/admin/dashboard' },
      { label: 'BREADCRUMB.FUNDS', url: '/admin/investment-funds' },
      {
        label: this.currentFundName || 'BREADCRUMB.FUND_DETAILS',
        url: `/admin/investment-funds/fund-details?id=${this.currentFundId}`,
      },
      { label: 'ASSESSMENTS.TITLE', url: '', disabled: true },
    ];
  }

  onBreadcrumbClicked(item: IBreadcrumbItem): void {
    if (!item) {
      return;
    }

    if (item.disabled) {
      return;
    }

    if (!item.url) {
      return;
    }

    this.router.navigateByUrl(item.url).catch(() => {
      // Navigation failed - error handling could be added here if needed
    });
  }

  loadAssessments(search: string = '', status?: AssessmentStatus): void {

    if (this.currentFundId <= 0) {
      this.hasError = true;
      this.errorMessage = 'ASSESSMENTS.INVALID_FUND_ID';
      return;
    }

    this.isLoading = true;
    this.hasError = false;

    // Use NSwag-generated AssessmentServiceProxy
        
    // Use NSwag-generated AssessmentServiceProxy with advanced filters
    this.assessmentServiceProxy.list(
      this.currentFundId,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      this.currentPage,
      this.pageSize,
      this.search || undefined,
      "",
      ).subscribe({
      next: (response: SingleAssessmentResponsePaginatedResult) => {
        this.isLoading = false;

        if (response.successed && response.data) {
          this.assessments = response.data;

          // Get pagination info from API response (similar to resolutions)
          // Try different possible property names for pagination data
          this.totalCount = response.totalCount;

          this.totalPages = response.totalPages;

          this.filteredAssessments = [...this.assessments];
          
          if (this.assessments.length === 0) {
            this.hasError = false;
            this.errorMessage = 'ASSESSMENTS.NO_ASSESSMENTS';
          }

          // Update fund name from first assessment if available
          if (this.assessments.length > 0 && this.assessments[0].fundName) {
            this.currentFundName = this.assessments[0].fundName;
            this.updateBreadcrumb();
            localStorage.setItem('currentFundName', this.currentFundName);
          }
        } else {
          this.hasError = true;
          this.errorMessage = response.message || 'ASSESSMENTS.LOAD_ERROR';
        }
      },
      error: () => {
        this.isLoading = false;
        this.hasError = true;
        this.errorMessage = 'ASSESSMENTS.LOAD_ERROR';
        this.errorModalService.showError(this.translateService.instant('ASSESSMENTS.LOAD_ERROR'));
      }
    });
  }

  // Event handlers
  onSearch(event: any): void {
    const searchTerm = typeof event === 'string' ? event.trim() : event?.search?.trim() || '';

    this.search = searchTerm;
    this.currentPage = 1;
    this.currentSearchFilters = null; // Clear advanced filters when doing basic search

    this.loadAssessments(this.search, this.selectedStatus);
  }

  onFilter(): void {
    const dialogRef = this.dialog.open(AssessmentAdvancedSearchDialogComponent, {
      width: '380px',
      data: this.currentSearchFilters || {
        search: this.search,
        status: this.selectedStatus,
        type: null,
        fromDate: '',
        toDate: '',
        createdBy: '',
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.applyFilters(result);
      }
    });
  }

  private applyFilters(filters: AssessmentSearchFilters): void {
    this.currentPage = 1; // Reset to first page when applying filters
    this.currentSearchFilters = filters; // Store current search filters

    // Update search term if provided in filters
    if (filters.search) {
      this.search = filters.search;
    }

    // Update selected status if provided in filters
    if (filters.status) {
      this.selectedStatus = filters.status as AssessmentStatus;
    }

    // Load assessments with advanced filters
    this.loadAssessmentsWithAdvancedFilters(filters);
  }

  private loadAssessmentsWithAdvancedFilters(filters: AssessmentSearchFilters): void {
    if (this.currentFundId <= 0) {
      this.hasError = true;
      this.errorMessage = 'ASSESSMENTS.INVALID_FUND_ID';
      return;
    }

    this.isLoading = true;
    this.hasError = false;

    // Convert filters to API parameters
    const statusEnum = filters.status ? filters.status as AssessmentStatus : undefined;
    const typeEnum = filters.type ? filters.type as AssessmentType : undefined;
    const createdByNumber = filters.createdBy ? parseInt(filters.createdBy, 10) : undefined;

    // Convert date strings to DateTime objects using Luxon (same as resolutions component)
    let fromDate: DateTime | undefined;
    let toDate: DateTime | undefined;

    if (filters.fromDate) {
      fromDate = DateTime.fromISO(filters.fromDate, { zone: 'utc' }).startOf("day");
      console.log('Converted fromDate:', filters.fromDate, '->', fromDate.toISO());
    }

    if (filters.toDate) {
      toDate = DateTime.fromISO(filters.toDate, { zone: 'utc' }).startOf("day");
      console.log('Converted toDate:', filters.toDate, '->', toDate.toISO());
    }
    
    // Use NSwag-generated AssessmentServiceProxy with advanced filters
    this.assessmentServiceProxy.list(
      this.currentFundId,
      statusEnum,
      typeEnum,
      fromDate,
      toDate,
      createdByNumber,
      this.currentPage,
      this.pageSize,
      filters.search || undefined,
      "",
    ).subscribe({
      next: (response: SingleAssessmentResponsePaginatedResult) => {
        this.isLoading = false;

        if (response.successed && response.data) {
          this.assessments = response.data;

          // Get pagination info from API response
          this.totalCount = response.totalCount;

          this.totalPages = response.totalPages;
          this.filteredAssessments = [...this.assessments];

          if (this.assessments.length === 0) {
            this.hasError = false;
            this.errorMessage = 'ASSESSMENTS.NO_ASSESSMENTS';
          }

          // Update fund name from first assessment if available
          if (this.assessments.length > 0 && this.assessments[0].fundName) {
            this.currentFundName = this.assessments[0].fundName;
          }
        } else {
          this.hasError = true;
          this.errorMessage = response.message || 'ASSESSMENTS.LOAD_ERROR';
        }
      },
      error: () => {
        this.isLoading = false;
        this.hasError = true;
        this.errorMessage = 'ASSESSMENTS.LOAD_ERROR';
        this.errorModalService.showError(this.translateService.instant('ASSESSMENTS.LOAD_ERROR'));
      }
    });
  }

  private reloadCurrentData(): void {
    // Check if we have active search filters
    if (this.hasActiveSearchFilters()) {
      // Reload with current search filters
      this.loadAssessmentsWithAdvancedFilters(this.currentSearchFilters);
    } else {
      // Reload with basic parameters
      this.loadAssessments(this.search, this.selectedStatus);
    }
  }

  private hasActiveSearchFilters(): boolean {
    return this.currentSearchFilters !== null;
  }

  // Pagination methods
  onPageChange(page: number): void {
    if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
      this.currentPage = page;
      this.reloadCurrentData();
    }
  }

  onPreviousPage(): void {
    if (this.canGoPrevious()) {
      this.onPageChange(this.currentPage - 1);
    }
  }

  onNextPage(): void {
    if (this.canGoNext()) {
      this.onPageChange(this.currentPage + 1);
    }
  }

  canGoPrevious(): boolean {
    return this.currentPage > 1;
  }

  canGoNext(): boolean {
    return this.currentPage < this.totalPages;
  }

  isEnglish(): boolean {
    return this.translateService.currentLang === LanguageEnum.en;
  }

  getPageNumbers(): number[] {
    const pages: number[] = [];
    const maxPagesToShow = 5;
    const halfRange = Math.floor(maxPagesToShow / 2);

    let startPage = Math.max(1, this.currentPage - halfRange);
    let endPage = Math.min(this.totalPages, this.currentPage + halfRange);

    // Adjust if we're near the beginning or end
    if (endPage - startPage + 1 < maxPagesToShow) {
      if (startPage === 1) {
        endPage = Math.min(this.totalPages, startPage + maxPagesToShow - 1);
      } else {
        startPage = Math.max(1, endPage - maxPagesToShow + 1);
      }
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    return pages;
  }



  // Navigation methods
  addNewAssessment(): void {
    this.router.navigate(['/admin/investment-funds/assessments/create'], {
      queryParams: { fundId: this.currentFundId }
    });
  }

  viewAssessmentDetails(assessmentId: number): void {
    if(this.isBoardMember)
         this.router.navigate(['admin/investment-funds/assessments/member-response'], {
                queryParams: { assessmentId: assessmentId ,memberId: this.boardMemberId }
          });
    else
         this.router.navigate(['/admin/investment-funds/assessments/details', assessmentId], {
         });

    }

  edit(assessmentId: number): void {
    this.router.navigate(['/admin/investment-funds/assessments/edit', assessmentId], {
    });
  }
  delete(assessmentId: number): void {
    // Find the assessment to get its title for confirmation
    const assessment = this.assessments.find(a => a.id === assessmentId);
    const assessmentTitle = assessment?.title || '';

    Swal.fire({
      title: this.translateService.instant('COMMON.CONFIRM_DELETE'),
      text: this.translateService.instant('ASSESSMENTS.DELETE_CONFIRM', { title: assessmentTitle }),
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#d33',
      cancelButtonColor: '#3085d6',
      confirmButtonText: this.translateService.instant('COMMON.DELETE'),
      cancelButtonText: this.translateService.instant('COMMON.CANCEL')
    }).then((result) => {
      if (result.isConfirmed) {
        this.executeDelete(assessmentId);
      }
    });
  }

  private executeDelete(assessmentId: number): void {
    // Use NSwag-generated AssessmentServiceProxy for deletion
    this.assessmentServiceProxy.delete(assessmentId).subscribe({
      next: (response) => {
        if (response.successed) {
          this.errorModalService.showSuccess(
            this.translateService.instant('ASSESSMENTS.DELETE_SUCCESS')
          );
          // Reload the assessments list
          this.loadAssessments();
        } else {
          this.errorModalService.showError(
            response.message || this.translateService.instant('ASSESSMENTS.DELETE_ERROR')
          );
        }
      },
      error: (error) => {
        console.error('Error deleting assessment:', error);
        this.errorModalService.showError(
          this.translateService.instant('ASSESSMENTS.DELETE_ERROR')
        );
      }
    });
  }

  // Utility methods
  getStatusClass(status: AssessmentStatus): string {
    let statusClass: string;
    switch (status) {
      case AssessmentStatus._1: // Draft
        statusClass = 'draft';
        break;
      case AssessmentStatus._2: // WaitingForApproval
        statusClass = 'pending';
        break;
      case AssessmentStatus._3: // Approved
        statusClass = 'confirmed';
        break;
      case AssessmentStatus._4: // Rejected
        statusClass = 'rejected';
        break;
      case AssessmentStatus._5: // Active
        statusClass = 'voting-inProgress';
        break;
      case AssessmentStatus._6: // Completed
        statusClass = 'confirmed';
        break;
      default:
        statusClass = 'rejected';
        break;
    }

    return statusClass;
  }

  getStatusTranslationKey(status: AssessmentStatus): string {
    switch (status) {
      case AssessmentStatus._1: // Draft
        return 'ASSESSMENTS.STATUS.DRAFT';
        break;
      case AssessmentStatus._2: // WaitingForApproval
        return 'ASSESSMENTS.STATUS.WAITINGFORAPPROVAL';
        break;
      case AssessmentStatus._3: // Approved
        return 'ASSESSMENTS.STATUS.APPROVED';
        break;
      case AssessmentStatus._4: // Rejected
        return 'ASSESSMENTS.STATUS.REJECTED';
        break;
      case AssessmentStatus._5: // Active
        return 'ASSESSMENTS.STATUS.ACTIVE';
        break;
      case AssessmentStatus._6: // Completed
        return 'ASSESSMENTS.STATUS.COMPLETED';
        break;
      default:
        return 'ASSESSMENTS.STATUS.UNKNOWN';
        break;
    }

  }

  canViewDetails(_assessment: SingleAssessmentResponse): boolean {
    return this.tokenService.hasPermission('Assessment.View');
  }

  canEdit(assessment: SingleAssessmentResponse): boolean {
    return assessment.canEdit && 
           this.tokenService.hasPermission('Assessment.Edit');
  }
   canDelete(assessment: SingleAssessmentResponse): boolean {
    return assessment.canDelete && 
           this.tokenService.hasPermission('Assessment.Delete');
  }
 canCreate(): boolean {
    return this.canCreateAssessment &&
           this.tokenService.hasPermission('Assessment.Create');
  }
  // Helper methods for display
  getTypeDisplayName(type: AssessmentType): string {
    switch (type) {
      case AssessmentType._1:
        return this.translateService.instant('ASSESSMENTS.QUESTIONNAIRE');
      case AssessmentType._2:
        return this.translateService.instant('ASSESSMENTS.ATTACHMENT');
      default:
        return '';
    }
  }


}
