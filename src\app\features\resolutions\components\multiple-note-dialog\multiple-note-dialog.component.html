<div class="dialog-container">
  <h2 class=" header">
    {{ comments ? ('INVESTMENT_FUNDS.VOTING.DECISION_COMMENTS' | translate) :
    ('INVESTMENT_FUNDS.VOTING.ITEM_COMMENTS' | translate) }}
  </h2>
<hr>

<div class="form-fields">
<div class="member-note-wrapper" *ngFor="let comment of comments.itemComments">
  <div class="row member-note-header">
    <div class="col-md-1 img-container">
      <img src="assets/images/avatar-member.png" alt="Member Avatar"
        class="avatar-img">
    </div>
    <div class="col-md-11 p-0">
      <div class="member-note-user-info">
        <span class="user-name">{{comment.createdByName}}</span>
      </div>
      <div class="member-note-role d-flex justify-content-between">
         <span class="user-role"> {{comment?.userRoleOrBoardMemberType}}</span>
        <p class="user-time">
  {{ getNotificationTime(comment.createdAt ?comment.createdAt :dateNow)}}  </p>
      </div>
      <div class="member-note-body">
        <p>{{comment.comment}}</p>
      </div>
    </div>
  </div>
</div>
 <hr>
   </div>

    <div class="form-container">
      <textarea
        class="form-control form-control-solid"
        type="text"
        [(ngModel)]="body"
        name="body"
        #note="ngModel" maxlength="500"
        placeholder="{{'INVESTMENT_FUNDS.VOTING.VOTING_COMMENTS' | translate}}"
        required
        ></textarea>
      <div *ngIf="note.invalid && note.touched"
        class="text-danger">
        <div *ngIf="note.errors?.['required']">{{'FORM.ERROR_REQUIRED' | translate}}</div>
      </div>
       <div *ngIf="note.errors?.['maxlength'] && note.touched" class="text-danger">
         {{ 'INVESTMENT_FUNDS.RESOLUTIONS.MAX_LENGTH_ERROR'| translate: { max: 500 } }}
</div>
    </div>
<hr>
  <div class="dialog-actions">
      <app-custom-button [btnName]="'COMMON.CANCEL' | translate" (click)="onCancel()" [buttonType]="buttonEnum.Secondary"
        [iconName]="IconEnum.cancel">
      </app-custom-button>

    <app-custom-button [btnName]="'INVESTMENT_FUNDS.VOTING.ADD_COMMENT' | translate" (click)="addNote()" [buttonType]="buttonEnum.Primary"
        [iconName]="IconEnum.verify">
      </app-custom-button>
  </div>
</div>


