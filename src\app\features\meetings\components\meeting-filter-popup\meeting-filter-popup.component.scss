::ng-deep{
    .mat-mdc-dialog-inner-container{
        height: fit-content !important;
    }

    .mat-mdc-dialog-container{
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .btn.outline-btn{
        padding-inline: 0.5rem !important;
    }
}

.filter-dialog-container {
  .header {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
    
    span {
      margin-right: 0.5rem;
      
      img {
        width: 24px;
        height: 24px;
      }
    }
  }
}

// RTL Support
:host-context([dir="rtl"]) {
  .filter-dialog-container {
    .header {
      span {
        margin-right: 0;
        margin-left: 0.5rem;
      }
    }
  }
}
