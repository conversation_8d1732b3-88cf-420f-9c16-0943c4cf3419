import { CommonModule } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { ResolutionMemberDto, ResolutionMembersDto, ResolutionMemberVoteServiceProxy } from '@core/api/api.generated';
import { MemberVotingResult } from '@core/enums/voting/member-voting-result';
import { ResolutionService } from '@core/services/resolution.service';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { FundStatus } from '@shared/enum/fund-status';
import { ResolutionStatus } from '@shared/enum/resolution-status';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-voting-members',
  standalone: true,
  imports: [TranslateModule, CommonModule],
  templateUrl: './voting-members.component.html',
  styleUrl: './voting-members.component.scss'
})
export class VotingMembersComponent implements OnInit {
  @Input() resolutionId: number = 0;
  @Input() resolutionCode: number = 0;
  @Input() fundId: number = 0;
  @Input() resolutionStatus: number = 0;
  votingMembers: ResolutionMembersDto | undefined;
  memberVotingResultEnum = MemberVotingResult;
  resolutionStatusEnum=ResolutionStatus;


  constructor(
    private resolutionsVotingProxy: ResolutionMemberVoteServiceProxy,
    private translateService: TranslateService,
    private router: Router
  ){}

  ngOnInit(): void {
    this.getVotingMembersData()
  }

  getVotingMembersData() {
    this.resolutionsVotingProxy.resolutionMembers(this.resolutionId).subscribe({
      next: (response) => {
        if (response.successed) {
          this.votingMembers = response.data;
          console.log('votingMembers',this.votingMembers);
        }
      },
      error: (error) => {
        console.error('Error loading voting members:', error);
      }
    });
  }

  getStatusClass(statusId: number): string {
    switch(statusId){
      case this.memberVotingResultEnum.Accept:
        return 'status-green';
      case this.memberVotingResultEnum.Reject:
        return 'status-red';
      case this.memberVotingResultEnum.NotEligibleToVote:
        return 'status-grey';
      case this.memberVotingResultEnum.NotVotedYet:
        return 'status-orange';
      // case this.memberVotingResultEnum.NotVoted:
      //   return 'not-voted';
      default:
        return '';
    }
  }

  onViewMemberDetails(member: ResolutionMemberDto) {
      this.router.navigate(
      ['/admin/investment-funds/resolutions/view-voting-result',this.resolutionId],
      {
        queryParams: { fundId: this.fundId , memberId: member.boardMemberId },
      }
    );
  }
  sendReminder(member: ResolutionMemberDto) {
    console.log(this.resolutionId, member.resolutionId);
    Swal.fire({
      title: this.translateService.instant('RESOLUTIONS.CONFIRM'),
      text: this.translateService.instant('RESOLUTIONS.VOTING.CONFIRM_REMINDER_TEXT', {memberName: member.fullName, resolutionId: this.resolutionCode}),
      imageUrl: 'assets/images/confirmation-green.svg',
      showCancelButton: true,
      customClass: {
        confirmButton: 'btn btn-primary ' + (this.translateService.currentLang == 'en' ? 'ms-3' : 'me-3'),
        cancelButton: 'btn outline-btn',
      },
      reverseButtons: true,
      buttonsStyling: false,
      confirmButtonText:`<img src="assets/images/approve-white.png" alt="verify" class="${this.translateService.currentLang == 'ar' ? 'ms-2' : 'me-2'}"/>` + this.translateService.instant('RESOLUTIONS.VOTING.SEND_REMINDER'),
      cancelButtonText: `<img src="assets/images/back-icon.png" alt="back" class="${this.translateService.currentLang == 'ar' ? 'ms-2' : 'me-2'}"/>` + this.translateService.instant('INVESTMENT_FUNDS.RESOLUTIONS.BACK')
    }).then((result) => {
      if (result.isConfirmed){
        this.resolutionsVotingProxy.sendReminder(this.resolutionId, member.boardMemberId).subscribe({
          next: (response) => {
            if (response.successed) {
              Swal.fire({
                title: this.translateService.instant('COMMON.SUCCESS'),
                text: this.translateService.instant('RESOLUTIONS.VOTING.REMINDER_SENT_SUCCESSFULLY'),
                icon: 'success',
                timer: 2000,
                showConfirmButton: false
              });
              this.getVotingMembersData();
            }
          },
          error: (error) => {
            console.error('Error sending reminder:', error);
          }
        });
      }
    })
  }
}
