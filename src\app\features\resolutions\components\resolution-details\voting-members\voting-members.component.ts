import { CommonModule } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';
import { ResolutionMemberDto, ResolutionMembersDto, ResolutionMemberVoteServiceProxy } from '@core/api/api.generated';
import { MemberVotingResult } from '@core/enums/voting/member-voting-result';
import { ResolutionService } from '@core/services/resolution.service';
import { TranslateModule } from '@ngx-translate/core';
import { FundStatus } from '@shared/enum/fund-status';
import { ResolutionStatus } from '@shared/enum/resolution-status';

@Component({
  selector: 'app-voting-members',
  standalone: true,
  imports: [TranslateModule, CommonModule],
  templateUrl: './voting-members.component.html',
  styleUrl: './voting-members.component.scss'
})
export class VotingMembersComponent implements OnInit {
  @Input() resolutionId: number = 0;
  @Input() resolutionStatus: number = 0;
  votingMembers: ResolutionMembersDto | undefined;
  memberVotingResultEnum = MemberVotingResult;
  resolutionStatusEnum=ResolutionStatus;


  constructor(private resolutionsVotingProxy: ResolutionMemberVoteServiceProxy){}

  ngOnInit(): void {
    this.getVotingMembersData()
  }

  getVotingMembersData() {
    this.resolutionsVotingProxy.resolutionMembers(this.resolutionId).subscribe({
      next: (response) => {
        if (response.successed) {
          this.votingMembers = response.data;
          console.log('votingMembers',this.votingMembers);
        }
      },
      error: (error) => {
        console.error('Error loading voting members:', error);
      }
    });
  }

  getStatusClass(statusId: number): string {
    switch(statusId){
      case this.memberVotingResultEnum.Accept:
        return 'status-green';
      case this.memberVotingResultEnum.Reject:
        return 'status-red';
      case this.memberVotingResultEnum.NotEligibleToVote:
        return 'status-grey';
      case this.memberVotingResultEnum.NotVotedYet:
        return 'status-orange';
      // case this.memberVotingResultEnum.NotVoted:
      //   return 'not-voted';
      default:
        return '';
    }
  }

  onViewMemberDetails(member: ResolutionMemberDto) {}
}
