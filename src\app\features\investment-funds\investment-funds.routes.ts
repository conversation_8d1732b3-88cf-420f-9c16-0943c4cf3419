import { Routes } from '@angular/router';
import { AuthGuard } from '@core/guards/auth.guard';
import { FundAccessGuard } from '@core/guards/fund-access.guard';

export const INVESTMENT_FUNDS_ROUTES: Routes = [
  {
    path: '',
    children: [
      {
        path: '',
        loadComponent: () =>
          import('./components/funds-list/investment-funds.component').then(
            (m) => m.InvestmentFundsComponent
          ),
      },
      {
        path: 'fund-details',
        loadComponent: () =>
          import('./components/fund-details/fund-details.component').then(
            (m) => m.FundDetailsComponent
          ),
        canActivate: [AuthGuard, FundAccessGuard],
      },

      {
        path: 'create',
        loadComponent: () =>
          import('./components/create-fund/create-fund.component').then(
            (m) => m.CreateFundComponent
          ),
      },
      {
        path: 'update/:id',
        loadComponent: () =>
          import('./components/update-fund/update-fund.component').then(
            (m) => m.UpdateFundComponent
          ),
      },
      {
        path: 'complete-fund-info/:id',
        loadComponent: () =>
          import('./components/update-fund/update-fund.component').then(
            (m) => m.UpdateFundComponent
          ),
      },
      {
        path: 'resolutions',
        loadChildren: () =>
          import('../resolutions/resolutions.routes').then(
            (m) => m.RESOLUTION_ROUTES
          ),
        canActivate: [AuthGuard, FundAccessGuard],
      },
      {
        path: 'members',
        loadChildren: () =>
          import('../members/members.routes').then(
            (m) => m.MEMBERS_ROUTES
          ),

      },
        {
        path: 'documents',
        loadChildren: () =>
          import('../documents/documents.routes').then(
            (m) => m.DOCUMENTS_ROUTES
          ),

      },
      {
        path: 'assessments',
        loadChildren: () =>
          import('../assessments/assessments.routes').then(
            (m) => m.ASSESSMENTS_ROUTES
          ),
      },
      //   {
      //   path: 'meetings',
      //   loadChildren: () =>
      //     import('../meetings/meetings-routing.module').then(
      //       (m) => m.MeetingsRoutingModule
      //     ),
      // },
      {
        path: 'meetings',
        loadChildren: () =>
          import('../meetings/meetings.routes').then(
            (m) => m.MEETINGS_ROUTES
          ),

      },
      // Add other investment funds routes here
    ],
  },
];
