.question-dialog {
  width: 100%;
  max-width: 800px;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    background: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;

    .dialog-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #00205a;
    }

    .close-btn {
      background: none;
      border: none;
      font-size: 18px;
      color: #666;
      cursor: pointer;
      padding: 4px;
      border-radius: 4px;
      transition: all 0.2s ease;

      &:hover {
        background: #e0e0e0;
        color: #333;
      }

      i {
        font-size: 16px;

        // Fallback for when FontAwesome doesn't load
        &.fas.fa-times:before {
          content: "×";
          font-family: Arial, sans-serif;
          font-size: 20px;
          font-weight: bold;
        }
      }
    }
  }

  .dialog-content {
    flex: 1;
    overflow-y: auto;
    max-height: calc(80vh - 140px); // Account for header and footer
    padding: 24px;

    // Custom scrollbar styling
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }

    .question-form-section {
      margin-bottom: 24px;
    }

    .options-section {
      .options-header {
        margin-bottom: 16px;

        .options-title {
          font-size: 16px;
          font-weight: 600;
          color: #1a1a1a;
          margin-bottom: 4px;
        }

        .options-description {
          font-size: 14px;
          color: #666;
          margin: 0;
        }
      }

      .options-list {
        margin-bottom: 16px;

        .option-item {
          margin-bottom: 12px;

          .option-input-group {
            display: flex;
            align-items: flex-start;
            gap: 12px;

            // RTL support
            html[dir="rtl"] & {
              direction: rtl;
            }

            .option-number {
              width: 28px;
              height: 38px;
              background: #007bff;
              color: white;
              border-radius: 6px;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 13px;
              font-weight: 600;
              flex-shrink: 0;
              margin-top: 1px;
            }

            .correct-answer-control {
              display: flex;
              align-items: center;
              justify-content: center;
              margin-top: 6px;
              flex-shrink: 0;

              .form-check-input {
                margin: 0;
                cursor: pointer;
                width: 18px;
                height: 18px;
                border: 2px solid #ced4da;

                // Radio button specific styling
                &[type="radio"] {
                  border-radius: 50%;

                  &:checked {
                    background-color: #28a745;
                    border-color: #28a745;
                    background-image: radial-gradient(circle, white 30%, #28a745 30%);
                  }
                }

                // Checkbox specific styling
                &[type="checkbox"] {
                  border-radius: 3px;

                  &:checked {
                    background-color: #28a745;
                    border-color: #28a745;
                    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='m6 10 3 3 6-6'/%3e%3c/svg%3e");
                  }
                }

                &:focus {
                  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
                  outline: none;
                }

                &:hover {
                  border-color: #28a745;
                }
              }
            }

            .option-input-container {
              flex: 1;

              .option-input {
                width: 100%;
                padding: 8px 12px;
                border: 1px solid #ced4da;
                border-radius: 6px;
                font-size: 14px;
                transition: all 0.2s ease;

                &:focus {
                  border-color: #007bff;
                  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
                  outline: none;
                }

                &.is-invalid {
                  border-color: #dc3545;

                  &:focus {
                    border-color: #dc3545;
                    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
                  }
                }

                &::placeholder {
                  color: #999;
                  font-style: italic;
                }
              }

              .invalid-feedback {
                display: block;
                width: 100%;
                margin-top: 4px;
                font-size: 12px;
                color: #dc3545;
              }
            }

            .remove-option-btn {
              padding: 8px 10px;
              border-radius: 6px;
              transition: all 0.2s ease;
              flex-shrink: 0;
              border: 1px solid #dc3545;
              background: #f8d7da;
              color: #721c24;

              &:hover {
                background-color: #dc3545;
                border-color: #dc3545;
                color: white;
                transform: scale(1.05);
              }

              &:focus {
                box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
                outline: none;
              }

              i {
                font-size: 12px;

                // FontAwesome fallback
                &.fas.fa-trash:before {
                  content: "🗑";
                  font-family: Arial, sans-serif;
                  font-size: 14px;
                }
              }

              // RTL support
              html[dir="rtl"] & {
                margin-left: 0;
                margin-right: auto;
              }
            }

              &:disabled {
                opacity: 0.5;
                cursor: not-allowed;
              }

              i {
                font-size: 12px;
              }
            }
          }
        }
      }

      .add-option-section {
        display: flex;
        justify-content: center;
        margin-bottom: 12px;

        .add-option-btn {
          padding: 8px 16px;
          border-radius: 6px;
          font-size: 13px;
          font-weight: 500;
          transition: all 0.2s ease;

          i {
            font-size: 11px;
            margin-right: 6px;
          }

          &:hover {
            background-color: #007bff;
            border-color: #007bff;
            color: white;
          }
        }
      }

      .options-info {
        text-align: center;
        padding: 8px;

        small {
          display: inline-flex;
          align-items: center;
          gap: 6px;
          font-size: 12px;

          i {
            font-size: 11px;
          }
        }
      }
    }

    .text-question-info {
      .info-card {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 16px;
        display: flex;
        align-items: flex-start;
        gap: 12px;

        .info-icon {
          width: 40px;
          height: 40px;
          background: #e3f2fd;
          color: #1976d2;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;

          i {
            font-size: 16px;
          }
        }

        .info-content {
          flex: 1;

          h5 {
            font-size: 14px;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 4px;
          }

          p {
            font-size: 13px;
            color: #666;
            margin: 0;
            line-height: 1.4;
          }
        }
      }
    }
  }

  .dialog-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 20px 24px;
    border-top: 1px solid #e0e0e0;
    background: #f8f9fa;

    // RTL support
    html[dir="rtl"] & {
      justify-content: flex-start;
    }
  }

  // Responsive Design
  @media (max-width: 768px) {
    .question-dialog {
      width: 95vw;
      max-height: 90vh;

    .dialog-header {
      padding: 16px 20px;

      .dialog-title {
        font-size: 16px;
      }
    }

    .dialog-content {
      padding: 20px;

      .options-section {
        .options-list {
          .option-item {
            .option-input-group {
              gap: 8px;

              .option-number {
                width: 24px;
                height: 32px;
                font-size: 12px;
              }

              .remove-option-btn {
                padding: 6px 8px;

                i {
                  font-size: 11px;
                }
              }
            }
          }
        }
      }
    }

    .dialog-actions {
      padding: 12px 20px;
      flex-direction: column;

      app-custom-button {
        width: 100%;
      }
    }
  }

  // Expected Answer Section
  .expected-answer-section {
    margin-bottom: 24px;

    .expected-answer-header {
      margin-bottom: 16px;

      .expected-answer-title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin-bottom: 8px;
      }

      .expected-answer-description {
        font-size: 14px;
        color: #666;
        margin-bottom: 0;
      }
    }

    .expected-answer-input {
      .form-control {
        border: 1px solid #ced4da;
        border-radius: 6px;
        padding: 12px;
        font-size: 14px;
        transition: all 0.2s ease;
        resize: vertical;
        min-height: 80px;

        &:focus {
          border-color: #007bff;
          box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
          outline: none;
        }

        &.is-invalid {
          border-color: #dc3545;

          &:focus {
            border-color: #dc3545;
            box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
          }
        }
      }

      .invalid-feedback {
        display: block;
        font-size: 12px;
        color: #dc3545;
        margin-top: 4px;
      }
    }
  }

  // Correct answer info styling
  .correct-answer-info {
    font-size: 13px;
    color: #666;
    margin-bottom: 8px;
    padding: 8px 12px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border-left: 3px solid #28a745;

    // RTL support
    html[dir="rtl"] & {
      border-left: none;
      border-right: 3px solid #28a745;
    }

    strong {
      color: #333;
    }
  }

  @media (max-width: 480px) {
    .dialog-content {
      padding: 16px;

      .options-section {
        .options-list {
          .option-item {
            .option-input-group {
              flex-direction: column;
              align-items: stretch;
              gap: 8px;

              .option-number {
                align-self: flex-start;
                width: 24px;
                height: 24px;
              }

              .remove-option-btn {
                align-self: flex-end;
                width: fit-content;
              }
            }
          }
        }
      }
    }
  }
}
