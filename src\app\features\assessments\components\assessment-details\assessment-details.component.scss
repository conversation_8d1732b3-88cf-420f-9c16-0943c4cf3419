@import "../../../../../assets/scss/variables";

.breadcrumb-container {
  margin-bottom: 1rem;
}

.header-container {
  display: flex;

  .title-container {
    .title {
      color: $navy-blue;
      font-size: 24px;
      font-style: normal;
      font-weight: 700;
      line-height: 20px;
      // margin-bottom: 17px;
    }
    .sub-title {
      color: #4f4f4f;

      font-size: 12px;
      font-weight: 400;
      line-height: 24px;
      margin-bottom: 33px;
      span {
        color: $navy-blue;
        font-size: 14px;
        font-weight: 500;
        line-height: 20px;
        margin-right: 8px;
      }
    }
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;

  .loading-text {
    margin-top: 1rem;
    color: var(--text-secondary);
  }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;

  .error-icon {
    font-size: 3rem;
    color: var(--error-color);
    margin-bottom: 1rem;
  }

  .error-message {
    color: var(--error-color);
    margin-bottom: 2rem;
    font-size: 1.1rem;
  }
}

.resolution-details-container,
.attachment-section,
.members-section {
  // padding: 1rem;
  // max-width: 1200px;
  // margin: 0 auto;

  background-color: $card-background;
  border: 1px solid $border-color;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .info-item {
    .info-label {
      color: $text-grey;
      font-size: 14px;
      font-weight: 700;
      line-height: 20px;
    }
    .description-text {
      max-width: 100%; // or set a fixed width like 250px
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      display: block;
    }
    .info-value {
      color: $navy-blue;
      font-size: 16px;
      font-weight: 500;
      line-height: 16px;
    }
    .status {
      display: flex;
      align-items: center;
      gap: 8px;
      height: 24px;
      border-radius: 20px;
      padding: 10px;

      font-size: 14px;
      font-weight: 400;
      line-height: 16px;
      width: fit-content;

      &.draft {
        background: rgba(117, 85, 172, 0.1);
        color: #7555ac;
      }
      &.pending {
        background: #fff3cd;
        color: #856404;
      }
      &.completing-data {
        background: rgba(157, 112, 9, 0.27);
        color: #9d7009;
      }
      &.waiting-for-confirmation {
        background: rgba(226, 180, 138, 0.34);
        color: #d16440;
      }
      &.confirmed {
        background: rgba(97, 253, 97, 0.14);

        color: #27ae60;
      }

      &.rejected {
        color: $text-grey;
        background: #eaeef1;
      }

      &.voting-inProgress {
        background: rgba(47, 128, 237, 0.1);
        color: #2f80ed;
      }

      &.approved {
        background: #f1faf1;
        color: #0e700e;
      }

      &.not-approved {
        background: rgba(197, 15, 31, 0.1);

        color: #c50f1f;
      }

      &.cancelled {
        background: var(--Color---Grey-5, #e0e0e0);

        color: #4f4f4f;
      }
    }
  }
}
.attachment-section,
.members-section {
  padding: 18px;
  height: fit-content;

  .title {
    color: $dark-blue;
    font-size: 16px;
    font-weight: 700;
    line-height: 28px;
    letter-spacing: -0.32px;
    span {
      border-radius: 14px;
      background: rgba(38, 86, 135, 0.12);
      color: var(--Color---Black-1, #000);
      font-size: 16px;
      font-weight: 400;
      padding: 5px 8px;
      margin-right: 10px;
      margin-left: 10px;
    }
  }
  .sub-title {
    color: $text-grey;
    font-size: 14px;
    font-weight: 700;
    line-height: 20px;
    padding: 8px 0;
    .attachment-number {
      border-radius: 14px;
      background: rgba(38, 86, 135, 0.12);
      color: #000;
      font-size: 12px;
      font-weight: 400;
      line-height: 18px;
      margin-right: 6px;
      padding: 0.6px 5.5px;
    }
  }
}

.resolution-details-container {
  background-color: $card-background;
  border: 1px solid $border-color;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  
  &:last-of-type{
    margin-bottom: 30px;
  }
}

.header {
  margin: 0 0 42px;
  font-size: 24px;
  font-weight: 700;
  line-height: 20px;
}

.resolution-details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;

  .header-actions {
    display: flex;
    gap: 12px;

    button {
      background: none;
      border: none;
      cursor: pointer;
      padding: 8px;
      color: #666;
      transition: color 0.2s ease;

      &:hover {
        color: #333;
      }
      .fa-pencil {
        color: #eaa300;
      }
      .expand {
        color: $navy-blue;
      }
      i {
        font-size: 16px;
      }
    }
  }

  .section-title {
    font-size: 16px;
    font-weight: 700;
    margin: 0;
    line-height: 22px;
    display: flex;
    align-items: center;
    span {
      border-radius: 14px;
      background: rgba(38, 86, 135, 0.12);
      color: var(--Color---Black-1, #000);

      font-size: 16px;
      font-weight: 400;
      line-height: 18px; /* 112.5% */
      display: flex;
      padding: 8px;
      justify-content: center;
      align-items: center;
      gap: 10px;
      margin-right: 10px;
      margin-left: 10px;
    }
  }
}

hr {
  color: $border-hr-color;
  border: 1px solid;
  margin: 0;
}

.resolution-details-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;

  &.expanded {
    max-height: fit-content;
  }

  .item-container {
    border-radius: 8px;
    background: #fff;
    padding: 16px;
    // display: flex;
    // justify-content: space-between;
    // align-items: center;
    .top-section {
      padding: 0 18px;

      .title {
        color: $navy-blue;
        font-size: 20px;
        font-weight: 500;
        line-height: 32px;
      }
      .conflict-btn {
        color: #b68107;
        font-size: 14px;
        font-weight: 400;
        line-height: 18px;
        border-radius: 8px;
        border: 1px solid #ffc800;
        background: #fffaeb;
        padding: 7px 13px;
      }
    }
    .sub-title {
      color: #333;
      font-size: 14px;
      font-weight: 400;
      line-height: 21px;
      margin-top: 12px;
      padding: 0 18px;
    }
  }
}

.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 400;
  line-height: 16px;

  .dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin: 0px;
    margin-inline-end: 6px;
  }

  &.status-green {
    background-color: #f1faf1;
    color: #27ae60;

    .dot {
      background-color: #27ae60;
    }
  }

  &.status-blue {
    background-color: #e5eefb;
    color: #2f80ed;

    .dot {
      background-color: #2f80ed;
    }
  }

  &.status-orange {
    background-color: #fdf1eb;
    color: #ff5f3d;

    .dot {
      background-color: #ff5f3d;
    }
  }

  &.status-red {
    background-color: #FFEBED;
    color: #C50F1F;

    .dot {
      background-color: #C50F1F;
    }
  }

  &.status-grey {
    background-color: #e0e0e0;
    color: #828282;

    .dot {
      background-color: #828282;
    }
  }

  &.status-purple {
    background-color: #f3f0ff;
    color: #7c3aed;

    .dot {
      background-color: #7c3aed;
    }
  }
}

// Members Section Styling
.members-section {
  .title {
    color: $navy-blue;
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;

    .member-count {
      color: #666;
      font-size: 16px;
      font-weight: 500;
    }
  }

  .members-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin: 12px 0px;
  }

  .member-card {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: 8px;
    transition: all 0.2s ease;

    .member-info {
      display: flex;
      align-items: center;
      gap: 12px;

      .member-avatar {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        overflow: hidden;
        background: #f5f5f5;
        display: flex;
        align-items: center;
        justify-content: center;

        .avatar-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .member-details {
        .member-name {
          font-size: 14px;
          font-weight: 400;
          color: $navy-blue;
          margin-bottom: 4px;
          line-height: 22px;
        }

        .member-role {
          font-size: 12px;
          color: #666;
          margin: 0;
          line-height: 1.2;
        }
      }
    }

    .voting-status {
      display: flex;
      flex-direction: column;
      .update-link {
        background: none;
        border: none;
        color: #0F6CBD;
        text-decoration: underline;
        font-size: 12px;
        cursor: pointer;
        padding: 0;

        &:hover {
          color: #0a5299;
        }
      }
    }
  }

  .response-statistics {
    display: flex;
    gap: 8px;
    justify-content: space-between;
    margin-top: 16px;

    .summary-card {
      display: flex;
      flex-direction: column;
      padding: 12px;
      padding-top: 8px;
      border-radius: 12px;
      width: 33%;

      .summary-count {
        font-size: 18px;
        font-weight: 500;
      }

      .summary-label {
        font-size: 12px;
        font-weight: 500;
        line-height: 20px;
        margin-bottom: 5px;
      }

      &.pending-summary {
        background: #EAA3002E;
        color: #CC910B;
      }

      &.submitted-summary {
        background: #27AE602E;
        color: #27AE60;
      }

      &.expected-summary {
        background: #3B82F62E;
        color: #2563EB;
      }
    }
  }
}
.action-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
  padding: 2rem 0;
  border-top: 1px solid var(--border-color);
}

// Enhanced Question Cards Styling
.enhanced-question-card {
  background: #FFFFFF;
  border: 2px solid #E5E7EB;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  transition: all 0.3s ease;
  position: relative;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  &:hover {
    border-color: #3B82F6;
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.15);
    transform: translateY(-2px);
  }

  &:last-child {
    margin-bottom: 0;
  }

  .question-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    gap: 16px;

    .question-title-section {
      flex: 1;

      .question-title {
        font-size: 18px;
        font-weight: 600;
        color: $navy-blue;
        margin: 0;
        line-height: 1.4;
        display: flex;
        align-items: center;
        gap: 8px;

        .question-number {
          background: linear-gradient(135deg, $navy-blue 0%, #1e3a8a 100%);
          color: white;
          width: 28px;
          height: 28px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 14px;
          font-weight: 700;
          flex-shrink: 0;
        }

        .required-indicator {
          color: #DC2626;
          font-size: 20px;
          font-weight: 700;
          margin-left: 6px;
        }
      }
    }

    .question-meta-section {
      .question-type-badge {
        display: inline-flex;
        align-items: center;
        gap: 6px;
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 11px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        border: 1px solid transparent;
        white-space: nowrap;
        transition: all 0.2s ease;

        .question-type-icon {
          font-size: 12px;
        }

        &.single-choice {
          background: linear-gradient(135deg, #EFF6FF 0%, #DBEAFE 100%);
          color: #1E40AF;
          border-color: #3B82F6;
        }

        &.multi-choice {
          background: linear-gradient(135deg, #F0FDF4 0%, #DCFCE7 100%);
          color: #166534;
          border-color: #22C55E;
        }

        &.text-answer {
          background: linear-gradient(135deg, #FEF3C7 0%, #FDE68A 100%);
          color: #92400E;
          border-color: #F59E0B;
        }
      }
    }
  }

  .question-body {
    .question-text-container {
      margin-bottom: 20px;

      .question-text {
        font-size: 15px;
        color: #374151;
        line-height: 1.6;
        font-weight: 500;
        margin: 0;
        padding: 16px;
        background: #F9FAFB;
        border-radius: 8px;
        border-left: 4px solid $navy-blue;
      }
    }

    .question-stats {
      display: flex;
      gap: 16px;
      margin-bottom: 20px;
      flex-wrap: wrap;

      .stats-item {
        display: flex;
        align-items: center;
        gap: 6px;
        padding: 6px 12px;
        background: #F3F4F6;
        border-radius: 16px;
        font-size: 12px;
        color: #6B7280;

        .stats-icon {
          font-size: 11px;

          &.required {
            color: #DC2626;
          }
        }

        .stats-text {
          font-weight: 500;

          &.required {
            color: #DC2626;
          }
        }
      }
    }

    .options-container {
      .options-header {
        margin-bottom: 16px;

        .options-title {
          font-size: 14px;
          font-weight: 600;
          color: $navy-blue;
          margin: 0;
          display: flex;
          align-items: center;
          gap: 8px;

          .options-icon {
            color: #6B7280;
            font-size: 13px;
          }
        }
      }

      .options-list {
        .option-item {
          display: flex;
          align-items: flex-start;
          gap: 12px;
          padding: 12px 0;
          border-bottom: 1px solid #F3F4F6;
          transition: all 0.2s ease;

          &:last-child {
            border-bottom: none;
          }

          &:hover {
            background: #F9FAFB;
            margin: 0 -12px;
            padding: 12px;
            border-radius: 6px;
          }

          .option-indicator {
            .option-number {
              background: linear-gradient(135deg, #E5E7EB 0%, #D1D5DB 100%);
              color: #374151;
              width: 24px;
              height: 24px;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 12px;
              font-weight: 600;
              flex-shrink: 0;
              border: 2px solid #E5E7EB;
            }
          }

          .option-content {
            flex: 1;

            .option-text {
              font-size: 14px;
              color: #374151;
              line-height: 1.5;
              font-weight: 400;
            }
          }
        }
      }
    }

    .text-answer-info {
      .text-answer-placeholder {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 16px;
        background: #FFFBEB;
        border: 1px dashed #F59E0B;
        border-radius: 8px;
        color: #92400E;

        .text-icon {
          font-size: 16px;
          color: #F59E0B;
        }

        .text-info {
          font-size: 14px;
          font-weight: 500;
        }
      }
    }
  }
}

// Animation for question cards
.enhanced-question-card {
  animation: fadeInUp 0.4s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Responsive Design
@media (max-width: 768px) {
  .enhanced-question-card {
    padding: 20px;
    margin-bottom: 20px;

    .question-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;

      .question-title-section {
        .question-title {
          font-size: 16px;

          .question-number {
            width: 24px;
            height: 24px;
            font-size: 12px;
          }
        }
      }

      .question-meta-section {
        .question-type-badge {
          font-size: 10px;
          padding: 6px 12px;
        }
      }
    }

    .question-body {
      .question-text-container {
        .question-text {
          font-size: 14px;
          padding: 14px;
        }
      }

      .question-stats {
        gap: 12px;

        .stats-item {
          font-size: 11px;
          padding: 5px 10px;
        }
      }

      .options-container {
        .options-list {
          .option-item {
            padding: 10px 0;

            .option-indicator {
              .option-number {
                width: 20px;
                height: 20px;
                font-size: 11px;
              }
            }

            .option-content {
              .option-text {
                font-size: 13px;
              }
            }
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .enhanced-question-card {
    padding: 16px;

    .question-header {
      .question-title-section {
        .question-title {
          font-size: 15px;
        }
      }
    }

    .question-body {
      .question-text-container {
        .question-text {
          font-size: 13px;
          padding: 12px;
        }
      }
    }
  }
}

// RTL Support
[dir="rtl"] {
  .enhanced-question-card {
    .question-header {
      .question-title-section {
        .question-title {
          .required-indicator {
            margin-left: 0;
            margin-right: 6px;
          }
        }
      }
    }

    .question-body {
      .question-text-container {
        .question-text {
          border-left: none;
          border-right: 4px solid $navy-blue;
        }
      }

      .options-container {
        .options-list {
          .option-item {
            &:hover {
              margin: 0 -12px;
            }
          }
        }
      }
    }
  }
}

// Hover Effects Enhancement
.enhanced-question-card:hover {
  .question-type-badge {
    transform: scale(1.05);

    &.single-choice {
      background: linear-gradient(135deg, #DBEAFE 0%, #BFDBFE 100%);
    }

    &.multi-choice {
      background: linear-gradient(135deg, #DCFCE7 0%, #BBF7D0 100%);
    }

    &.text-answer {
      background: linear-gradient(135deg, #FDE68A 0%, #FCD34D 100%);
    }
  }

  .question-number {
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(0, 32, 90, 0.3);
  }
}