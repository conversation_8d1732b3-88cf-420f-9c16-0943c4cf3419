<div class="investment-funds">
    <div class="mb-4">

        <app-page-header  [showCreateButton]="isHasPermissionAdd" [showSearch]="true" [showFilter]="true"
            createButtonText="INVESTMENT_FUNDS.CREATE_NEW_FUND" searchPlaceholder="INVESTMENT_FUNDS.SEARCH_PLACEHOLDER"
            (create)="onCreateNewFund()" (search)="onSearch($event)" (filter)="openFilter()"></app-page-header>

    </div>
    <!-- Funds Accordion -->
    <mat-accordion class="funds-accordion" multi *ngIf="!isNoData">
        <mat-expansion-panel *ngFor="let group of fundGroups ; let i=index" [expanded]="group.expanded"
            (opened)="handlePanelOpen(i)" (afterExpand)="group.expanded = true"
            (afterCollapse)="group.expanded = false">
            <mat-expansion-panel-header [collapsedHeight]="'auto'" [expandedHeight]="'auto'">
                <mat-panel-title>
                    <div class="d-flex justify-content-start align-items-center w-100 h-48 gap-2">
                        <img [src]="'assets/images/' + (group.expanded ? 'accrdion_up.png' : 'accrdion_down.png')"
                            class="accordion-icon" alt="toggle" />
                        {{ group.title }}

                        <span class="group-count-badge" *ngIf="group.count">
                            <span class="red-dot" *ngIf="group.hasNotification"></span>
                            <span *ngIf="group.count>2">
                                {{ group.count }}
                            </span>
                            {{ getFundLabel(group.count) |translate}}
                        </span>
                    </div>
                </mat-panel-title>
            </mat-expansion-panel-header>

            <!-- Funds Grid -->
            <div class="funds-grid">
                <div class="row g-4">
                    <!-- Fund Card Template -->
                    <div class="col-12 col-md-6 col-lg-4 col-xl-3" *ngFor="let fund of group.funds">
                        <div class="fund-card hover-shadow">
                            <div class="card-header">
                                <div class="status-section">
                                    <div class="status-badge" [ngClass]="getStatusClass(fund.statusId)">
                                        <span class="dot"></span>
                                        {{fund.status }}
                                    </div>
                                    <div class="d-flex gap-3">
                                        <div class="bill-icon-wrapper position-relative">
                                            <img src="assets/images/bill.png" alt="bill" />
                                            <span class="notification-badge"
                                                *ngIf="fund.notificationTotalCount && fund.notificationTotalCount > 0">
                                                {{fund.notificationTotalCount}}
                                            </span>
                                        </div>

                                    </div>
                                </div>
                                <h3 class="fund-name">{{ fund.name}}</h3>
                            </div>
                            <div class="row m-0 h-100" (click)="navigateToUpdate(fund)">
                                <div class="card-body col-12">
                                    <div class="row flex-nowrap justify-content-start">
                                        <div class="info-row col" *ngIf="fund.initiationDate">
                                            <span class="label">{{ 'INVESTMENT_FUNDS.DATES.CREATION_DATE' | translate
                                                }}</span>
                                            <span class="value">{{ fund.initiationDate.toJSDate() | date:'dd / M / yyyy'
                                                }}</span>
                                            <div class="hijri-value">{{formatDateToString(fund.initiationDate)|
                                                dateHijriConverter:'hijri' }}</div>
                                        </div>
                                        <div *ngIf="fund.exitDate" class="info-row col">
                                            <span class="label">{{ 'INVESTMENT_FUNDS.DATES.EXIT_DATE' | translate }}</span>
                                            <span class="value">{{ fund.exitDate.toJSDate() | date:'dd / M / yyyy' }}</span>
                                            <div class="hijri-value">{{ formatDateToString(fund.exitDate)|
                                                dateHijriConverter:'hijri' }}</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-footer  d-flex  justify-content-end p-0">
                                    <div class="" >
                                        <img src="assets/images/Arrow_Icon.png" alt="Arrow" class="arrow-icon rotate-icon" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </mat-expansion-panel>
    </mat-accordion>

    <div *ngIf="isNoData"
        class="d-flex  flex-column gap-4 justify-content-center mt-5 align-items-center">
        <img src="assets/images/nodata.png" width="350" alt="No Data" />

        <ng-container>
            <p  class="text-center mt-3 header fs-20">{{'INVESTMENT_FUNDS.NO_DATA' | translate}}</p>
            <app-custom-button *ngIf="isHasPermissionAdd" class="mt-3" [btnName]="'INVESTMENT_FUNDS.CREATE_NEW_FUND' | translate"
                [iconName]="createButtonIcon.plus" (click)="onCreateNewFund()">
            </app-custom-button>
        </ng-container>
    </div>
</div>
