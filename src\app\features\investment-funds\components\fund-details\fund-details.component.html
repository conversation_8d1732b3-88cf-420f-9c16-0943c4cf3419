<div class="">
  <app-breadcrumb  [breadcrumbs]="breadcrumbItems"   (onClickEvent)="handleBreadcrumbClick($event)"
    [size]="breadcrumbSizeEnum.Medium" divider=">"></app-breadcrumb>
  <div class="d-flex flex-wrap align-items-center mb-3 mt-3 mt-lg-0 gap-3">
  <h2 class=" header m-0" *ngIf="fundDetails">
   {{ fundDetails?.name}}  </h2>
   <div class="w-50 flex-grow-1 flex-lg-grow-0" *ngIf="fundDetails?.membersCount == 0 && fundDetails?.statusId == fundStatus?.WaitingForAddingMembers">
    <app-alert [hasClose]="false" [isStaticPosition]="true"  [alertType]="AlertType.Warning" [msg]="'INVESTMENT_FUNDS.MEMBERS.MINIMUM_MEMBERS_WARNING' | translate"></app-alert>
  </div>
  </div>
  <div class="row" *ngIf="fundDetails" >
    <div class="col-12 col-lg-8">
    <div class=" fund-details-container">
      <div class="fund-details-header">
        <p class="section-title navy-color">
          {{ 'FUND_DETAILS.BASIC_INFO' | translate }}
        </p>
        <div class="header-actions">
          <button class="edit-button" (click)="goToDetails(fundDetails?.id)" *ngIf="tokenService.hasPermission('Fund.Edit') && fundDetails?.statusId != fundStatus?.UnderConstruction ">
            <img src="assets/images/edit-table-icon.png" alt="edit" />
          </button>
          <!-- <button class="expand-button" (click)="toggleExpand()">
            <img [src]="isExpanded ?'assets/images/accrdion_up.png' :'assets/images/accrdion_down.png'" alt="edit" style="width: 14px;height: 8px;"/>
          </button> -->

            <button class="expand-button" (click)="toggleExpand()">
            <span *ngIf="isExpanded">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="10" viewBox="0 0 16 10" fill="none">
                <path d="M13.5961 8.67437C13.4697 8.50224 13.0923 7.98838 12.8676 7.69215C12.4174 7.09886 11.8023 6.3105 11.1388 5.52448C10.472 4.73448 9.77144 3.9651 9.14909 3.39951C8.83703 3.1159 8.5629 2.9004 8.33669 2.76014C8.12394 2.62822 7.99827 2.60079 7.99827 2.60079C7.99827 2.60079 7.87628 2.62822 7.66354 2.76013C7.43733 2.9004 7.1632 3.1159 6.85114 3.3995C6.2288 3.9651 5.52826 4.73449 4.8614 5.52449C4.19789 6.31052 3.58281 7.09889 3.13265 7.69219C2.9079 7.98842 2.53103 8.50155 2.40463 8.67369C2.1499 9.02944 1.66248 9.10618 1.3166 8.84417C0.970723 8.58216 0.896834 8.08136 1.15157 7.7256L1.15347 7.72301C1.28596 7.54259 1.67694 7.01014 1.90632 6.70782C2.36664 6.10112 2.99969 5.28949 3.68679 4.47551C4.37053 3.66551 5.12309 2.83489 5.82114 2.20049C6.16928 1.8841 6.52218 1.5996 6.86042 1.38986C7.17732 1.19337 7.57855 0.999996 8.00012 1C8.42169 1 8.82291 1.19337 9.13981 1.38987C9.47806 1.5996 9.83095 1.8841 10.1791 2.20049C10.8771 2.83489 11.6297 3.6655 12.3134 4.4755C13.0005 5.28946 13.6336 6.10109 14.0939 6.70778C14.3234 7.0102 14.7142 7.54242 14.8465 7.72265L14.8483 7.72504C15.103 8.0808 15.0295 8.5821 14.6836 8.84411C14.3378 9.10612 13.8509 9.0301 13.5961 8.67437Z" fill="#00205A" stroke="#00205A" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </span>
            <span *ngIf="!isExpanded">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="10" viewBox="0 0 16 10" fill="none">
                <path d="M2.40387 1.32563C2.53027 1.49776 2.90766 2.01162 3.13242 2.30785C3.58257 2.90114 4.19765 3.6895 4.86116 4.47552C5.52803 5.26552 6.22856 6.0349 6.85091 6.60049C7.16297 6.8841 7.4371 7.0996 7.66331 7.23986C7.87606 7.37178 8.00173 7.39922 8.00173 7.39922C8.00173 7.39922 8.12372 7.37178 8.33646 7.23987C8.56267 7.09961 8.8368 6.8841 9.14886 6.6005C9.7712 6.0349 10.4717 5.26552 11.1386 4.47551C11.8021 3.68949 12.4172 2.90111 12.8673 2.30781C13.0921 2.01158 13.469 1.49845 13.5954 1.32632C13.8501 0.970558 14.3375 0.893825 14.6834 1.15583C15.0293 1.41784 15.1032 1.91864 14.8484 2.2744L14.8465 2.27699C14.714 2.45741 14.3231 2.98986 14.0937 3.29218C13.6334 3.89888 13.0003 4.71051 12.3132 5.52449C11.6295 6.33449 10.8769 7.16511 10.1789 7.79951C9.83072 8.1159 9.47782 8.4004 9.13958 8.61014C8.82268 8.80664 8.42145 9 7.99988 9C7.57831 9 7.17709 8.80663 6.86019 8.61013C6.52194 8.4004 6.16905 8.1159 5.82091 7.79951C5.12285 7.16511 4.3703 6.3345 3.68655 5.52451C2.99945 4.71054 2.36641 3.89891 1.90609 3.29222C1.67663 2.9898 1.2858 2.45758 1.15345 2.27735L1.1517 2.27496C0.896965 1.9192 0.970487 1.4179 1.31636 1.15589C1.66223 0.893884 2.14913 0.969901 2.40387 1.32563Z" fill="#00205A" stroke="#00205A" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </span>
            <!-- <img [src]="isExpandedAction ?'assets/images/accrdion_up.png'
                :'assets/images/accrdion_down.png'" alt="edit" style="width:
                14px;height: 8px;" /> -->
          </button>



        </div>
      </div>
      <hr *ngIf="isExpanded">
      <div class="fund-details-content" [class.expanded]="isExpanded">
        <div class="details-grid">
          <div class="detail-item">
            <label>
              {{ 'FUND_DETAILS.FUND_CODE' | translate }}
            </label>
            <span class="value">{{fundDetails?.id}}</span>
          </div>
          <div class="detail-item">
            <label> {{ 'FUND_DETAILS.STRATEGY' | translate }}
            </label>
            <span class="value">{{fundDetails?.strategyName}}</span>
          </div>
          <div class="detail-item">
            <label> {{ 'FUND_DETAILS.BUILDING_COUNT' | translate }}
            </label>
            <span class="value">{{fundDetails?.propertiesNumber}}</span>
          </div>
          <div class="detail-item">
            <label> {{ 'FUND_DETAILS.CREATION_DATE' | translate }}
            </label>
            <div class="date-value" *ngIf="fundDetails?.initiationDate">
              <span class="gregorian">{{fundDetails?.initiationDate | date:'d/M/y' }}</span>-
              <span class="hijri">{{fundDetails?.initiationDate| dateHijriConverter}}</span>
            </div>
          </div>
          <div class="detail-item">
            <label> {{ 'FUND_DETAILS.END_DATE' | translate }}
              <button class="edit-button p-0" (click)="editFundExitDate(fundDetails?.id)" *ngIf="tokenService.hasPermission('Fund.EditFundExitdate') && tokenService.hasRole('legalcouncil') && fundDetails?.statusId != fundStatus?.UnderConstruction ">
                <img src="assets/images/edit-table-icon.png" class=" mx-1" style="width: 12px; height: 12px;" alt="edit" />

              </button>
            </label>
            <div class="date-value"  *ngIf="fundDetails?.exitDate">
              <span class="gregorian">{{fundDetails?.exitDate | date: 'd/M/y'}}</span>-
              <span class="hijri">{{fundDetails?.exitDate | dateHijriConverter}}</span>
            </div>
          </div>
          <div class="detail-item">
            <label> {{ 'FUND_DETAILS.STATUS' | translate }}
            </label>
            <div class="d-flex justify-content-between">
              <span class="status" [ngClass]="getStatusClass(fundDetails?.statusId)">
                <!-- <i class="fas fa-circle"></i> -->
                <span class="circle"></span>
                {{ fundDetails?.status }}
              </span>
              <button *ngIf="fundDetails?.statusId == fundStatus?.Active"
                class="custom-btn">
                {{ 'FUND_DETAILS.EXIT' | translate }}
              </button>
              <button *ngIf="fundDetails?.statusId == fundStatus?.Exited"
              class="custom-btn">
              {{ 'FUND_DETAILS.ACTIVATED' | translate }}
            </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="my-5">
      <div class="row">
        <div class="col-md-4 mb-3" *ngFor="let card of fundCards">
          <app-fund-card-info
            [notificationCount]="fundDetails[card.notificationCount]"
            [fundCount]="fundDetails[card.fundCountKey]"
            [title]="card.title"
            [queryParams]="card.queryParams"
            [icon]="card.icon"
            [disabled]="card.disabled"
            [routerLink]="card.routerLink"
            [moduleId]="card.moduleId"
            (filterNotification)="filterNotification($event)">
          </app-fund-card-info>
        </div>
      </div>
    </div>

  </div>
    <div class="fund-details-page col-lg-4">
      <app-fund-history
      *ngIf="fundDetails"
      [apiResponse]="fundDetails"
      (resetFilterNotification)="filterNotification()"
      (loadMoreNotification)="loadMoreNotification()"
      ></app-fund-history>
    </div>
  </div>

</div>
